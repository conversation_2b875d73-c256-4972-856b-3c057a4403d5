/*
 Highcharts JS v9.0.1 (2021-02-15)

 (c) 2014-2021 Highsoft AS
 Authors: <AUTHORS>

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/modules/treemap",["highcharts"],function(n){a(n);a.Highcharts=n;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){function n(a,e,d,l){a.hasOwnProperty(e)||(a[e]=l.apply(null,d))}a=a?a._modules:{};n(a,"Mixins/ColorMapSeries.js",[a["Core/Globals.js"],a["Core/Series/Point.js"],a["Core/Utilities.js"]],function(a,e,d){var l=d.defined;
return{colorMapPointMixin:{dataLabelOnNull:!0,isValid:function(){return null!==this.value&&Infinity!==this.value&&-Infinity!==this.value},setState:function(a){e.prototype.setState.call(this,a);this.graphic&&this.graphic.attr({zIndex:"hover"===a?1:0})}},colorMapSeriesMixin:{pointArrayMap:["value"],axisTypes:["xAxis","yAxis","colorAxis"],trackerGroups:["group","markerGroup","dataLabelsGroup"],getSymbol:a.noop,parallelArrays:["x","y","value"],colorKey:"value",pointAttribs:a.seriesTypes.column.prototype.pointAttribs,
colorAttribs:function(a){var d={};l(a.color)&&(d[this.colorProp||"fill"]=a.color);return d}}}});n(a,"Series/Treemap/TreemapAlgorithmGroup.js",[],function(){return function(){function a(a,d,l,m){this.height=a;this.width=d;this.plot=m;this.startDirection=this.direction=l;this.lH=this.nH=this.lW=this.nW=this.total=0;this.elArr=[];this.lP={total:0,lH:0,nH:0,lW:0,nW:0,nR:0,lR:0,aspectRatio:function(a,d){return Math.max(a/d,d/a)}}}a.prototype.addElement=function(a){this.lP.total=this.elArr[this.elArr.length-
1];this.total+=a;0===this.direction?(this.lW=this.nW,this.lP.lH=this.lP.total/this.lW,this.lP.lR=this.lP.aspectRatio(this.lW,this.lP.lH),this.nW=this.total/this.height,this.lP.nH=this.lP.total/this.nW,this.lP.nR=this.lP.aspectRatio(this.nW,this.lP.nH)):(this.lH=this.nH,this.lP.lW=this.lP.total/this.lH,this.lP.lR=this.lP.aspectRatio(this.lP.lW,this.lH),this.nH=this.total/this.width,this.lP.nW=this.lP.total/this.nH,this.lP.nR=this.lP.aspectRatio(this.lP.nW,this.nH));this.elArr.push(a)};a.prototype.reset=
function(){this.lW=this.nW=0;this.elArr=[];this.total=0};return a}()});n(a,"Mixins/DrawPoint.js",[],function(){var a=function(a){return"function"===typeof a},e=function(d){var e,m=this,f=m.graphic,t=d.animatableAttribs,k=d.onComplete,g=d.css,b=d.renderer,r=null===(e=m.series)||void 0===e?void 0:e.options.animation;if(m.shouldDraw())f||(m.graphic=f=b[d.shapeType](d.shapeArgs).add(d.group)),f.css(g).attr(d.attribs).animate(t,d.isNew?!1:r,k);else if(f){var C=function(){m.graphic=f=f.destroy();a(k)&&
k()};Object.keys(t).length?f.animate(t,void 0,function(){C()}):C()}};return{draw:e,drawPoint:function(a){(a.attribs=a.attribs||{})["class"]=this.getClassName();e.call(this,a)},isFn:a}});n(a,"Series/Treemap/TreemapPoint.js",[a["Mixins/DrawPoint.js"],a["Core/Series/SeriesRegistry.js"],a["Core/Utilities.js"]],function(a,e,d){var l=this&&this.__extends||function(){var a=function(b,g){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var g in b)b.hasOwnProperty(g)&&
(a[g]=b[g])};return a(b,g)};return function(b,g){function d(){this.constructor=b}a(b,g);b.prototype=null===g?Object.create(g):(d.prototype=g.prototype,new d)}}(),m=e.series.prototype.pointClass,f=e.seriesTypes;e=f.pie.prototype.pointClass;var t=d.extend,k=d.isNumber,g=d.pick;d=function(a){function b(){var b=null!==a&&a.apply(this,arguments)||this;b.name=void 0;b.node=void 0;b.options=void 0;b.series=void 0;b.value=void 0;return b}l(b,a);b.prototype.getClassName=function(){var a=m.prototype.getClassName.call(this),
b=this.series,d=b.options;this.node.level<=b.nodeMap[b.rootNode].level?a+=" highcharts-above-level":this.node.isLeaf||g(d.interactByLeaf,!d.allowTraversingTree)?this.node.isLeaf||(a+=" highcharts-internal-node"):a+=" highcharts-internal-node-interactive";return a};b.prototype.isValid=function(){return!(!this.id&&!k(this.value))};b.prototype.setState=function(a){m.prototype.setState.call(this,a);this.graphic&&this.graphic.attr({zIndex:"hover"===a?1:0})};b.prototype.shouldDraw=function(){return k(this.plotY)&&
null!==this.y};return b}(f.scatter.prototype.pointClass);t(d.prototype,{draw:a.drawPoint,setVisible:e.prototype.setVisible});return d});n(a,"Series/Treemap/TreemapUtilities.js",[a["Core/Utilities.js"]],function(a){var e=a.objectEach,d;(function(a){function d(a,e,k){void 0===k&&(k=this);a=e.call(k,a);!1!==a&&d(a,e,k)}a.AXIS_MAX=100;a.isBoolean=function(a){return"boolean"===typeof a};a.eachObject=function(a,d,k){k=k||this;e(a,function(g,b){d.call(k,g,b,a)})};a.recursive=d})(d||(d={}));return d});n(a,
"Mixins/TreeSeries.js",[a["Core/Color/Color.js"],a["Core/Utilities.js"]],function(a,e){var d=e.extend,l=e.isArray,m=e.isNumber,f=e.isObject,t=e.merge,k=e.pick;return{getColor:function(g,b){var d=b.index,e=b.mapOptionsToLevel,f=b.parentColor,m=b.parentColorIndex,u=b.series,y=b.colors,t=b.siblings,v=u.points,l=u.chart.options.chart,w;if(g){v=v[g.i];g=e[g.level]||{};if(e=v&&g.colorByPoint){var n=v.index%(y?y.length:l.colorCount);var D=y&&y[n]}if(!u.chart.styledMode){y=v&&v.options.color;l=g&&g.color;
if(w=f)w=(w=g&&g.colorVariation)&&"brightness"===w.key?a.parse(f).brighten(d/t*w.to).get():f;w=k(y,l,D,w,u.color)}var G=k(v&&v.options.colorIndex,g&&g.colorIndex,n,m,b.colorIndex)}return{color:w,colorIndex:G}},getLevelOptions:function(a){var b=null;if(f(a)){b={};var e=m(a.from)?a.from:1;var g=a.levels;var k={};var n=f(a.defaults)?a.defaults:{};l(g)&&(k=g.reduce(function(a,b){if(f(b)&&m(b.level)){var g=t({},b);var k="boolean"===typeof g.levelIsConstant?g.levelIsConstant:n.levelIsConstant;delete g.levelIsConstant;
delete g.level;b=b.level+(k?0:e-1);f(a[b])?d(a[b],g):a[b]=g}return a},{}));g=m(a.to)?a.to:1;for(a=0;a<=g;a++)b[a]=t({},n,f(k[a])?k[a]:{})}return b},setTreeValues:function C(a,e){var b=e.before,f=e.idRoot,m=e.mapIdToNode[f],r=e.points[a.i],n=r&&r.options||{},l=0,t=[];d(a,{levelDynamic:a.level-(("boolean"===typeof e.levelIsConstant?e.levelIsConstant:1)?0:m.level),name:k(r&&r.name,""),visible:f===a.id||("boolean"===typeof e.visible?e.visible:!1)});"function"===typeof b&&(a=b(a,e));a.children.forEach(function(b,
f){var k=d({},e);d(k,{index:f,siblings:a.children.length,visible:a.visible});b=C(b,k);t.push(b);b.visible&&(l+=b.val)});a.visible=0<l||a.visible;b=k(n.value,l);d(a,{children:t,childrenTotal:l,isLeaf:a.visible&&!l,val:b});return a},updateRootId:function(a){if(f(a)){var b=f(a.options)?a.options:{};b=k(a.rootNode,b.rootId,"");f(a.userOptions)&&(a.userOptions.rootId=b);a.rootNode=b}return b}}});n(a,"Series/Treemap/TreemapComposition.js",[a["Core/Series/SeriesRegistry.js"],a["Series/Treemap/TreemapUtilities.js"],
a["Core/Utilities.js"]],function(a,e,d){var l=d.addEvent,m=d.extend,f=!1;l(a.series,"afterBindAxes",function(){var a=this.xAxis,d=this.yAxis;if(a&&d)if(this.is("treemap")){var g={endOnTick:!1,gridLineWidth:0,lineWidth:0,min:0,dataMin:0,minPadding:0,max:e.AXIS_MAX,dataMax:e.AXIS_MAX,maxPadding:0,startOnTick:!1,title:null,tickPositions:[]};m(d.options,g);m(a.options,g);f=!0}else f&&(d.setOptions(d.userOptions),a.setOptions(a.userOptions),f=!1)})});n(a,"Series/Treemap/TreemapSeries.js",[a["Core/Color/Color.js"],
a["Mixins/ColorMapSeries.js"],a["Core/Globals.js"],a["Mixins/LegendSymbol.js"],a["Core/Color/Palette.js"],a["Core/Series/SeriesRegistry.js"],a["Series/Treemap/TreemapAlgorithmGroup.js"],a["Series/Treemap/TreemapPoint.js"],a["Series/Treemap/TreemapUtilities.js"],a["Mixins/TreeSeries.js"],a["Core/Utilities.js"]],function(a,e,d,l,m,f,n,k,g,b,r){var t=this&&this.__extends||function(){var a=function(b,c){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,a){c.__proto__=a}||function(c,
a){for(var h in a)a.hasOwnProperty(h)&&(c[h]=a[h])};return a(b,c)};return function(b,c){function h(){this.constructor=b}a(b,c);b.prototype=null===c?Object.create(c):(h.prototype=c.prototype,new h)}}(),J=a.parse,K=e.colorMapSeriesMixin;a=d.noop;var u=f.series;e=f.seriesTypes;var y=e.column,G=e.heatmap,v=e.scatter,N=b.getColor,w=b.getLevelOptions,O=b.updateRootId,D=r.addEvent,P=r.correctFloat,A=r.defined,Q=r.error,F=r.extend,R=r.fireEvent,L=r.isArray,S=r.isObject,H=r.isString,z=r.merge,x=r.pick,T=r.stableSort;
b=function(a){function b(){var c=null!==a&&a.apply(this,arguments)||this;c.axisRatio=void 0;c.data=void 0;c.mapOptionsToLevel=void 0;c.nodeMap=void 0;c.options=void 0;c.points=void 0;c.rootNode=void 0;c.tree=void 0;return c}t(b,a);b.prototype.algorithmCalcPoints=function(c,a,b,E){var h,q,d,e,g=b.lW,I=b.lH,f=b.plot,k=0,m=b.elArr.length-1;if(a)g=b.nW,I=b.nH;else var l=b.elArr[b.elArr.length-1];b.elArr.forEach(function(c){if(a||k<m)0===b.direction?(h=f.x,q=f.y,d=g,e=c/d):(h=f.x,q=f.y,e=I,d=c/e),E.push({x:h,
y:q,width:d,height:P(e)}),0===b.direction?f.y+=e:f.x+=d;k+=1});b.reset();0===b.direction?b.width-=g:b.height-=I;f.y=f.parent.y+(f.parent.height-b.height);f.x=f.parent.x+(f.parent.width-b.width);c&&(b.direction=1-b.direction);a||b.addElement(l)};b.prototype.algorithmFill=function(c,a,b){var h=[],p,q=a.direction,d=a.x,e=a.y,g=a.width,f=a.height,k,m,l,n;b.forEach(function(b){p=b.val/a.val*a.height*a.width;k=d;m=e;0===q?(n=f,l=p/n,g-=l,d+=l):(l=g,n=p/l,f-=n,e+=n);h.push({x:k,y:m,width:l,height:n});c&&
(q=1-q)});return h};b.prototype.algorithmLowAspectRatio=function(c,a,b){var h=[],p=this,q,d={x:a.x,y:a.y,parent:a},e=0,g=b.length-1,f=new n(a.height,a.width,a.direction,d);b.forEach(function(b){q=b.val/a.val*a.height*a.width;f.addElement(q);f.lP.nR>f.lP.lR&&p.algorithmCalcPoints(c,!1,f,h,d);e===g&&p.algorithmCalcPoints(c,!0,f,h,d);e+=1});return h};b.prototype.alignDataLabel=function(a,b,d){var c=d.style;!A(c.textOverflow)&&b.text&&b.getBBox().width>b.text.textWidth&&b.css({textOverflow:"ellipsis",
width:c.width+="px"});y.prototype.alignDataLabel.apply(this,arguments);a.dataLabel&&a.dataLabel.attr({zIndex:(a.node.zIndex||0)+1})};b.prototype.buildNode=function(a,b,d,e,p){var c=this,h=[],q=c.points[b],g=0,f;(e[a]||[]).forEach(function(b){f=c.buildNode(c.points[b].id,b,d+1,e,a);g=Math.max(f.height+1,g);h.push(f)});b={id:a,i:b,children:h,height:g,level:d,parent:p,visible:!1};c.nodeMap[b.id]=b;q&&(q.node=b);return b};b.prototype.calculateChildrenAreas=function(a,b){var c=this,h=c.options,p=c.mapOptionsToLevel[a.level+
1],d=x(c[p&&p.layoutAlgorithm]&&p.layoutAlgorithm,h.layoutAlgorithm),e=h.alternateStartingDirection,f=[];a=a.children.filter(function(a){return!a.ignore});p&&p.layoutStartingDirection&&(b.direction="vertical"===p.layoutStartingDirection?0:1);f=c[d](b,a);a.forEach(function(a,h){h=f[h];a.values=z(h,{val:a.childrenTotal,direction:e?1-b.direction:b.direction});a.pointValues=z(h,{x:h.x/c.axisRatio,y:g.AXIS_MAX-h.y-h.height,width:h.width/c.axisRatio});a.children.length&&c.calculateChildrenAreas(a,a.values)})};
b.prototype.drawDataLabels=function(){var a=this,b=a.mapOptionsToLevel,d,e;a.points.filter(function(a){return a.node.visible}).forEach(function(c){e=b[c.node.level];d={style:{}};c.node.isLeaf||(d.enabled=!1);e&&e.dataLabels&&(d=z(d,e.dataLabels),a._hasPointLabels=!0);c.shapeArgs&&(d.style.width=c.shapeArgs.width,c.dataLabel&&c.dataLabel.css({width:c.shapeArgs.width+"px"}));c.dlOptions=z(d,c.options.dataLabels)});u.prototype.drawDataLabels.call(this)};b.prototype.drawPoints=function(){var a=this,b=
a.chart,d=b.renderer,e=b.styledMode,p=a.options,f=e?{}:p.shadow,g=p.borderRadius,k=b.pointCount<p.animationLimit,l=p.allowTraversingTree;a.points.forEach(function(c){var b=c.node.levelDynamic,h={},q={},E={},B="level-group-"+c.node.level,M=!!c.graphic,n=k&&M,m=c.shapeArgs;c.shouldDraw()&&(g&&(q.r=g),z(!0,n?h:q,M?m:{},e?{}:a.pointAttribs(c,c.selected?"select":void 0)),a.colorAttribs&&e&&F(E,a.colorAttribs(c)),a[B]||(a[B]=d.g(B).attr({zIndex:1E3-(b||0)}).add(a.group),a[B].survive=!0));c.draw({animatableAttribs:h,
attribs:q,css:E,group:a[B],renderer:d,shadow:f,shapeArgs:m,shapeType:"rect"});l&&c.graphic&&(c.drillId=p.interactByLeaf?a.drillToByLeaf(c):a.drillToByGroup(c))})};b.prototype.drillToByGroup=function(a){var c=!1;1!==a.node.level-this.nodeMap[this.rootNode].level||a.node.isLeaf||(c=a.id);return c};b.prototype.drillToByLeaf=function(a){var c=!1;if(a.node.parent!==this.rootNode&&a.node.isLeaf)for(a=a.node;!c;)a=this.nodeMap[a.parent],a.parent===this.rootNode&&(c=a.id);return c};b.prototype.drillToNode=
function(a,b){Q(32,!1,void 0,{"treemap.drillToNode":"use treemap.setRootNode"});this.setRootNode(a,b)};b.prototype.drillUp=function(){var a=this.nodeMap[this.rootNode];a&&H(a.parent)&&this.setRootNode(a.parent,!0,{trigger:"traverseUpButton"})};b.prototype.getExtremes=function(){var a=u.prototype.getExtremes.call(this,this.colorValueData),b=a.dataMax;this.valueMin=a.dataMin;this.valueMax=b;return u.prototype.getExtremes.call(this)};b.prototype.getListOfParents=function(a,b){a=L(a)?a:[];var c=L(b)?
b:[];b=a.reduce(function(a,b,c){b=x(b.parent,"");"undefined"===typeof a[b]&&(a[b]=[]);a[b].push(c);return a},{"":[]});g.eachObject(b,function(a,b,h){""!==b&&-1===c.indexOf(b)&&(a.forEach(function(a){h[""].push(a)}),delete h[b])});return b};b.prototype.getTree=function(){var a=this.data.map(function(a){return a.id});a=this.getListOfParents(this.data,a);this.nodeMap={};return this.buildNode("",-1,0,a)};b.prototype.hasData=function(){return!!this.processedXData.length};b.prototype.init=function(a,b){K&&
(this.colorAttribs=K.colorAttribs);var c=D(this,"setOptions",function(a){a=a.userOptions;A(a.allowDrillToNode)&&!A(a.allowTraversingTree)&&(a.allowTraversingTree=a.allowDrillToNode,delete a.allowDrillToNode);A(a.drillUpButton)&&!A(a.traverseUpButton)&&(a.traverseUpButton=a.drillUpButton,delete a.drillUpButton)});u.prototype.init.call(this,a,b);delete this.opacity;this.eventsToUnbind.push(c);this.options.allowTraversingTree&&this.eventsToUnbind.push(D(this,"click",this.onClickDrillToNode))};b.prototype.onClickDrillToNode=
function(a){var b=(a=a.point)&&a.drillId;H(b)&&(a.setState(""),this.setRootNode(b,!0,{trigger:"click"}))};b.prototype.pointAttribs=function(a,b){var c=S(this.mapOptionsToLevel)?this.mapOptionsToLevel:{},d=a&&c[a.node.level]||{};c=this.options;var h=b&&c.states[b]||{},e=a&&a.getClassName()||"";a={stroke:a&&a.borderColor||d.borderColor||h.borderColor||c.borderColor,"stroke-width":x(a&&a.borderWidth,d.borderWidth,h.borderWidth,c.borderWidth),dashstyle:a&&a.borderDashStyle||d.borderDashStyle||h.borderDashStyle||
c.borderDashStyle,fill:a&&a.color||this.color};-1!==e.indexOf("highcharts-above-level")?(a.fill="none",a["stroke-width"]=0):-1!==e.indexOf("highcharts-internal-node-interactive")?(b=x(h.opacity,c.opacity),a.fill=J(a.fill).setOpacity(b).get(),a.cursor="pointer"):-1!==e.indexOf("highcharts-internal-node")?a.fill="none":b&&(a.fill=J(a.fill).brighten(h.brightness).get());return a};b.prototype.renderTraverseUpButton=function(a){var b=this,c=b.options.traverseUpButton,d=x(c.text,b.nodeMap[a].name,"\u25c1 Back");
if(""===a||b.is("sunburst")&&1===b.tree.children.length&&a===b.tree.children[0].id)b.drillUpButton&&(b.drillUpButton=b.drillUpButton.destroy());else if(this.drillUpButton)this.drillUpButton.placed=!1,this.drillUpButton.attr({text:d}).align();else{var e=(a=c.theme)&&a.states;this.drillUpButton=this.chart.renderer.button(d,0,0,function(){b.drillUp()},a,e&&e.hover,e&&e.select).addClass("highcharts-drillup-button").attr({align:c.position.align,zIndex:7}).add().align(c.position,!1,c.relativeTo||"plotBox")}};
b.prototype.setColorRecursive=function(a,b,d,e,f){var c=this,h=c&&c.chart;h=h&&h.options&&h.options.colors;if(a){var g=N(a,{colors:h,index:e,mapOptionsToLevel:c.mapOptionsToLevel,parentColor:b,parentColorIndex:d,series:c,siblings:f});if(b=c.points[a.i])b.color=g.color,b.colorIndex=g.colorIndex;(a.children||[]).forEach(function(b,d){c.setColorRecursive(b,g.color,g.colorIndex,d,a.children.length)})}};b.prototype.setPointValues=function(){var a=this,b=a.xAxis,d=a.yAxis,e=a.chart.styledMode;a.points.forEach(function(c){var h=
c.node,g=h.pointValues;h=h.visible;if(g&&h){h=g.height;var f=g.width,k=g.x,p=g.y,q=e?0:(a.pointAttribs(c)["stroke-width"]||0)%2/2;g=Math.round(b.toPixels(k,!0))-q;f=Math.round(b.toPixels(k+f,!0))-q;k=Math.round(d.toPixels(p,!0))-q;h=Math.round(d.toPixels(p+h,!0))-q;c.shapeArgs={x:Math.min(g,f),y:Math.min(k,h),width:Math.abs(f-g),height:Math.abs(h-k)};c.plotX=c.shapeArgs.x+c.shapeArgs.width/2;c.plotY=c.shapeArgs.y+c.shapeArgs.height/2}else delete c.plotX,delete c.plotY})};b.prototype.setRootNode=function(a,
b,d){a=F({newRootId:a,previousRootId:this.rootNode,redraw:x(b,!0),series:this},d);R(this,"setRootNode",a,function(a){var b=a.series;b.idPreviousRoot=a.previousRootId;b.rootNode=a.newRootId;b.isDirty=!0;a.redraw&&b.chart.redraw()})};b.prototype.setState=function(a){this.options.inactiveOtherPoints=!0;u.prototype.setState.call(this,a,!1);this.options.inactiveOtherPoints=!1};b.prototype.setTreeValues=function(a){var b=this,c=b.options,d=b.nodeMap[b.rootNode];c=g.isBoolean(c.levelIsConstant)?c.levelIsConstant:
!0;var e=0,f=[],k=b.points[a.i];a.children.forEach(function(a){a=b.setTreeValues(a);f.push(a);a.ignore||(e+=a.val)});T(f,function(a,b){return(a.sortIndex||0)-(b.sortIndex||0)});var l=x(k&&k.options.value,e);k&&(k.value=l);F(a,{children:f,childrenTotal:e,ignore:!(x(k&&k.visible,!0)&&0<l),isLeaf:a.visible&&!e,levelDynamic:a.level-(c?0:d.level),name:x(k&&k.name,""),sortIndex:x(k&&k.sortIndex,-l),val:l});return a};b.prototype.sliceAndDice=function(a,b){return this.algorithmFill(!0,a,b)};b.prototype.squarified=
function(a,b){return this.algorithmLowAspectRatio(!0,a,b)};b.prototype.strip=function(a,b){return this.algorithmLowAspectRatio(!1,a,b)};b.prototype.stripes=function(a,b){return this.algorithmFill(!1,a,b)};b.prototype.translate=function(){var a=this,b=a.options,d=O(a);u.prototype.translate.call(a);var e=a.tree=a.getTree();var f=a.nodeMap[d];a.renderTraverseUpButton(d);a.mapOptionsToLevel=w({from:f.level+1,levels:b.levels,to:e.height,defaults:{levelIsConstant:a.options.levelIsConstant,colorByPoint:b.colorByPoint}});
""===d||f&&f.children.length||(a.setRootNode("",!1),d=a.rootNode,f=a.nodeMap[d]);g.recursive(a.nodeMap[a.rootNode],function(b){var c=!1,d=b.parent;b.visible=!0;if(d||""===d)c=a.nodeMap[d];return c});g.recursive(a.nodeMap[a.rootNode].children,function(a){var b=!1;a.forEach(function(a){a.visible=!0;a.children.length&&(b=(b||[]).concat(a.children))});return b});a.setTreeValues(e);a.axisRatio=a.xAxis.len/a.yAxis.len;a.nodeMap[""].pointValues=d={x:0,y:0,width:g.AXIS_MAX,height:g.AXIS_MAX};a.nodeMap[""].values=
d=z(d,{width:d.width*a.axisRatio,direction:"vertical"===b.layoutStartingDirection?0:1,val:e.val});a.calculateChildrenAreas(e,d);a.colorAxis||b.colorByPoint||a.setColorRecursive(a.tree);b.allowTraversingTree&&(b=f.pointValues,a.xAxis.setExtremes(b.x,b.x+b.width,!1),a.yAxis.setExtremes(b.y,b.y+b.height,!1),a.xAxis.setScale(),a.yAxis.setScale());a.setPointValues()};b.defaultOptions=z(v.defaultOptions,{allowTraversingTree:!1,animationLimit:250,showInLegend:!1,marker:void 0,colorByPoint:!1,dataLabels:{defer:!1,
enabled:!0,formatter:function(){var a=this&&this.point?this.point:{};return H(a.name)?a.name:""},inside:!0,verticalAlign:"middle"},tooltip:{headerFormat:"",pointFormat:"<b>{point.name}</b>: {point.value}<br/>"},ignoreHiddenPoint:!0,layoutAlgorithm:"sliceAndDice",layoutStartingDirection:"vertical",alternateStartingDirection:!1,levelIsConstant:!0,drillUpButton:{position:{align:"right",x:-10,y:10}},traverseUpButton:{position:{align:"right",x:-10,y:10}},borderColor:m.neutralColor10,borderWidth:1,colorKey:"colorValue",
opacity:.15,states:{hover:{borderColor:m.neutralColor40,brightness:G?0:.1,halo:!1,opacity:.75,shadow:!1}}});return b}(v);F(b.prototype,{buildKDTree:a,colorKey:"colorValue",directTouch:!0,drawLegendSymbol:l.drawRectangle,getExtremesFromAll:!0,getSymbol:a,optionalAxis:"colorAxis",parallelArrays:["x","y","value","colorValue"],pointArrayMap:["value"],pointClass:k,trackerGroups:["group","dataLabelsGroup"],utils:{recursive:g.recursive}});f.registerSeriesType("treemap",b);"";return b});n(a,"masters/modules/treemap.src.js",
[],function(){})});
//# sourceMappingURL=treemap.js.map