package Dao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;


/**
 Title:Dao
 Description:TODO
 Auther:閽栨盯鏁撻弬銈嗗10170365
 Date:2017-10-17闁跨喐鏋婚幏鐑芥晸閺傘倖瀚�10:50:40
 */
public class DaoIwork2 {
	
	protected static String Username="iwork2";
	protected static String Password="Root_123+NR-drl";
	protected static String url="**************************************************************************************************************************************************************************";
	
	protected static String className="com.mysql.cj.jdbc.Driver";
	public static  Connection conn=null;
	/**
	  闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔告灮閹凤拷
	 Description:闁跨喐纾ラ弸鍕晸閺傘倖瀚归柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏鐤綁闁跨噦鎷�
	 Auther:閽栨盯鏁撻弬銈嗗10170365
	 Date:2017-10-17闁跨喐鏋婚幏鐑芥晸閺傘倖瀚�10:41:30
	 */
	public DaoIwork2() {
		try {
			Class.forName(className);
			//System.out.println(new Date()+" mysql闁跨喐鏋婚幏绋狣BC闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔稿焻閹存劗娅㈤幏鐑芥晸閺傘倖瀚�");
		} catch (ClassNotFoundException ex) {
			System.out.println("SQLException: " + ex.getMessage());
		}
		try {
			conn=DriverManager.getConnection(url,Username,Password);
			//System.out.println(new Date()+" 闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔告灮閹风柉褰夐柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏鐑芥晸缂傚娅㈤幏鐑芥晸閺傘倖瀚�");
		} catch (SQLException ex) {
			System.out.println("SQLException: " + ex.getMessage());
		}
	}
	/**
	  闁跨喐纾ラ弬褰掓晸閺傘倖瀚�
	 Description:闁跨喐鍩呴幉瀣闁跨喐鏋婚幏鐤綁闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归幏銉ヮ潒闁跨喐鏋婚幏鐑芥晸閿燂拷
	 Auther:閽栨盯鏁撻弬銈嗗10170365
	 Date:2017-10-17闁跨喐鏋婚幏鐑芥晸閺傘倖瀚�11:13:21
	 */
	public  void close(){
		if(conn!=null){
			try {
				conn.close();
				//System.out.println(new Date()+" 闁跨喓鍗抽惂鍛婂闁跨喕绶濋崠鈩冨闁跨喐鏋婚幏鐤綁闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐甯撮敐蹇斿");
			} catch (SQLException e) {
				System.out.println("SQLException: " + e.getMessage());
			}finally{
				conn = null;
			}
		}
	}
	/**
	 * 
	  闁跨喐纾ラ弬褰掓晸閺傘倖瀚�
	 Description:閹笛囨晸閺傘倖瀚筍ql闁跨喐鏋婚幏鐑芥晸娓氥儱鍤栭幏鐑芥晸閺傘倖瀚�
	 Auther:閽栨盯鏁撻弬銈嗗10170365
	 Date:2017-10-17闁跨喐鏋婚幏鐑芥晸閺傘倖瀚�12:28:38
	 */
	public  int execute(String sql){
		
		if(conn==null) 
			new DaoIwork2();	//闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔告灮閹疯渹鍓戦柨鐔告灮閹风兘鏁撴潪娆句悍閹烽婀柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏椋庮暠闁跨喐鏋婚幏鐑芥箒闁跨喐鏋婚幏閿嬫⒖閺傘倖瀚归柨鐕傛嫹
		try {
			return conn.createStatement().executeUpdate(sql);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			System.out.print("SQLException: " + e.getMessage());System.out.println("  闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔虹崵鏉堢偓瀚归柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏宄帮拷濂告晸閺傘倖瀚�");
			return -1;
		}
	}
	/**
	 * 
	  闁跨喐纾ラ弬褰掓晸閺傘倖瀚�
	 Description:闁跨喐鏋婚幏鐑芥晸娴犲绱氶柨鐔告灮閹风柉顕楅柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔稿焻鐠囇勫鐠囥垽鏁撻弬銈嗗闁跨噦鎷�
	 Auther:閽栨盯鏁撻弬銈嗗10170365
	 Date:2017-10-17闁跨喐鏋婚幏鐑芥晸閺傘倖瀚�02:35:58
	 */
	public static  ResultSet executeQuery(String sql){
		try {
			if(conn==null)  new DaoIwork2();  
			return conn.createStatement(ResultSet.TYPE_SCROLL_SENSITIVE,
					ResultSet.CONCUR_UPDATABLE).executeQuery(sql);//閹笛囨晸閸欘偉顕滈幏鐤嚄
		} catch (SQLException e) {
			System.out.print("SQLException: " + e.getMessage());
			return null;
		} finally {
		}
	}
	
	
	/**
	  闁跨喐纾ラ弬褰掓晸閺傘倖瀚�
	 Description:闁跨喐鏋婚幏鐑芥晸閺傘倖瀚归柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏鐤祿闁跨喓鎯畇er闁跨喐鏋婚幏鐑芥晸閸欘偉顕滈幏鐑芥晸鐠囶偄宕熼柨鐔告灮閹风兘鏁撻弬銈嗗闁跨喐鏋婚幏鐑芥晸娓氥儱灏呴幏宄邦潒闁跨喐鏋婚幏鐑芥晸閿燂拷
	 Auther:閽栨盯鏁撻弬銈嗗10170365
	 Date:2017-10-17闁跨喐鏋婚幏鐑芥晸閺傘倖瀚�11:30:24
	 */

	
	

}

