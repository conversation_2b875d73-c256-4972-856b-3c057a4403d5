/*
 Highcharts Gantt JS v9.0.1 (2021-02-15)

 Tree Grid

 (c) 2016-2021 Jon <PERSON><PERSON>

 License: www.highcharts.com/license
*/
(function(b){"object"===typeof module&&module.exports?(b["default"]=b,module.exports=b):"function"===typeof define&&define.amd?define("highcharts/modules/treegrid",["highcharts"],function(C){b(C);b.Highcharts=C;return b}):b("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(b){function C(b,r,v,q){b.hasOwnProperty(r)||(b[r]=q.apply(null,v))}b=b?b._modules:{};C(b,"Gantt/Tree.js",[b["Core/Utilities.js"]],function(b){var r=b.extend,v=b.isNumber,q=b.pick,n=function(b,k){var t=b.reduce(function(h,
b){var p=q(b.parent,"");"undefined"===typeof h[p]&&(h[p]=[]);h[p].push(b);return h},{});Object.keys(t).forEach(function(h,b){var p=t[h];""!==h&&-1===k.indexOf(h)&&(p.forEach(function(h){b[""].push(h)}),delete b[h])});return t},k=function(b,n,t,h,y,p){var f=0,w=0,m=p&&p.after,a=p&&p.before;n={data:h,depth:t-1,id:b,level:t,parent:n};var g,l;"function"===typeof a&&a(n,p);a=(y[b]||[]).map(function(e){var c=k(e.id,b,t+1,e,y,p),d=e.start;e=!0===e.milestone?d:e.end;g=!v(g)||d<g?d:g;l=!v(l)||e>l?e:l;f=f+
1+c.descendants;w=Math.max(c.height+1,w);return c});h&&(h.start=q(h.start,g),h.end=q(h.end,l));r(n,{children:a,descendants:f,height:w});"function"===typeof m&&m(n,p);return n};return{getListOfParents:n,getNode:k,getTree:function(b,q){var t=b.map(function(h){return h.id});b=n(b,t);return k("",null,1,null,b,q)}}});C(b,"Core/Axis/TreeGridTick.js",[b["Core/Color/Palette.js"],b["Core/Utilities.js"]],function(b,r){var v=r.addEvent,q=r.isObject,n=r.isNumber,k=r.pick,A=r.wrap,x;(function(t){function h(){this.treeGrid||
(this.treeGrid=new m(this))}function y(a,g){a=a.treeGrid;var l=!a.labelIcon,e=g.renderer,c=g.xy,d=g.options,B=d.width,D=d.height,h=c.x-B/2-d.padding;c=c.y-D/2;var f=g.collapsed?90:180,m=g.show&&n(c),u=a.labelIcon;u||(a.labelIcon=u=e.path(e.symbols[d.type](d.x,d.y,B,D)).addClass("highcharts-label-icon").add(g.group));m||u.attr({y:-9999});e.styledMode||u.attr({"stroke-width":1,fill:k(g.color,b.neutralColor60)}).css({cursor:"pointer",stroke:d.lineColor,strokeWidth:d.lineWidth});u[l?"attr":"animate"]({translateX:h,
translateY:c,rotation:f})}function p(a,g,l,e,c,d,B,D,h){var f=k(this.options&&this.options.labels,d);d=this.pos;var H=this.axis,u="treegrid"===H.options.type;a=a.apply(this,[g,l,e,c,f,B,D,h]);u&&(g=f&&q(f.symbol,!0)?f.symbol:{},f=f&&n(f.indentation)?f.indentation:0,d=(d=(H=H.treeGrid.mapOfPosToGridNode)&&H[d])&&d.depth||1,a.x+=g.width+2*g.padding+(d-1)*f);return a}function f(a){var g=this,l=g.pos,e=g.axis,c=g.label,d=e.treeGrid.mapOfPosToGridNode,B=e.options,D=k(g.options&&g.options.labels,B&&B.labels),
f=D&&q(D.symbol,!0)?D.symbol:{},h=(d=d&&d[l])&&d.depth;B="treegrid"===B.type;var b=-1<e.tickPositions.indexOf(l);l=e.chart.styledMode;B&&d&&c&&c.element&&c.addClass("highcharts-treegrid-node-level-"+h);a.apply(g,Array.prototype.slice.call(arguments,1));B&&c&&c.element&&d&&d.descendants&&0<d.descendants&&(e=e.treeGrid.isCollapsed(d),y(g,{color:!l&&c.styles&&c.styles.color||"",collapsed:e,group:c.parentGroup,options:f,renderer:c.renderer,show:b,xy:c.xy}),f="highcharts-treegrid-node-"+(e?"expanded":
"collapsed"),c.addClass("highcharts-treegrid-node-"+(e?"collapsed":"expanded")).removeClass(f),l||c.css({cursor:"pointer"}),[c,g.treeGrid.labelIcon].forEach(function(d){d&&!d.attachedTreeGridEvents&&(v(d.element,"mouseover",function(){c.addClass("highcharts-treegrid-node-active");c.renderer.styledMode||c.css({textDecoration:"underline"})}),v(d.element,"mouseout",function(){var d=q(D.style)?D.style:{};c.removeClass("highcharts-treegrid-node-active");c.renderer.styledMode||c.css({textDecoration:d.textDecoration})}),
v(d.element,"click",function(){g.treeGrid.toggleCollapse()}),d.attachedTreeGridEvents=!0)}))}var w=!1;t.compose=function(a){w||(v(a,"init",h),A(a.prototype,"getLabelPosition",p),A(a.prototype,"renderLabel",f),a.prototype.collapse=function(a){this.treeGrid.collapse(a)},a.prototype.expand=function(a){this.treeGrid.expand(a)},a.prototype.toggleCollapse=function(a){this.treeGrid.toggleCollapse(a)},w=!0)};var m=function(){function a(a){this.tick=a}a.prototype.collapse=function(a){var g=this.tick,e=g.axis,
c=e.brokenAxis;c&&e.treeGrid.mapOfPosToGridNode&&(g=e.treeGrid.collapse(e.treeGrid.mapOfPosToGridNode[g.pos]),c.setBreaks(g,k(a,!0)))};a.prototype.expand=function(a){var g=this.tick,e=g.axis,c=e.brokenAxis;c&&e.treeGrid.mapOfPosToGridNode&&(g=e.treeGrid.expand(e.treeGrid.mapOfPosToGridNode[g.pos]),c.setBreaks(g,k(a,!0)))};a.prototype.toggleCollapse=function(a){var g=this.tick,e=g.axis,c=e.brokenAxis;c&&e.treeGrid.mapOfPosToGridNode&&(g=e.treeGrid.toggleCollapse(e.treeGrid.mapOfPosToGridNode[g.pos]),
c.setBreaks(g,k(a,!0)))};return a}();t.Additions=m})(x||(x={}));return x});C(b,"Mixins/TreeSeries.js",[b["Core/Color/Color.js"],b["Core/Utilities.js"]],function(b,r){var v=r.extend,q=r.isArray,n=r.isNumber,k=r.isObject,A=r.merge,x=r.pick;return{getColor:function(t,h){var k=h.index,p=h.mapOptionsToLevel,f=h.parentColor,w=h.parentColorIndex,m=h.series,a=h.colors,g=h.siblings,l=m.points,e=m.chart.options.chart,c;if(t){l=l[t.i];t=p[t.level]||{};if(p=l&&t.colorByPoint){var d=l.index%(a?a.length:e.colorCount);
var B=a&&a[d]}if(!m.chart.styledMode){a=l&&l.options.color;e=t&&t.color;if(c=f)c=(c=t&&t.colorVariation)&&"brightness"===c.key?b.parse(f).brighten(k/g*c.to).get():f;c=x(a,e,B,c,m.color)}var D=x(l&&l.options.colorIndex,t&&t.colorIndex,d,w,h.colorIndex)}return{color:c,colorIndex:D}},getLevelOptions:function(b){var h=null;if(k(b)){h={};var y=n(b.from)?b.from:1;var p=b.levels;var f={};var w=k(b.defaults)?b.defaults:{};q(p)&&(f=p.reduce(function(b,a){if(k(a)&&n(a.level)){var g=A({},a);var f="boolean"===
typeof g.levelIsConstant?g.levelIsConstant:w.levelIsConstant;delete g.levelIsConstant;delete g.level;a=a.level+(f?0:y-1);k(b[a])?v(b[a],g):b[a]=g}return b},{}));p=n(b.to)?b.to:1;for(b=0;b<=p;b++)h[b]=A({},w,k(f[b])?f[b]:{})}return h},setTreeValues:function p(b,k){var f=k.before,h=k.idRoot,m=k.mapIdToNode[h],a=k.points[b.i],g=a&&a.options||{},l=0,e=[];v(b,{levelDynamic:b.level-(("boolean"===typeof k.levelIsConstant?k.levelIsConstant:1)?0:m.level),name:x(a&&a.name,""),visible:h===b.id||("boolean"===
typeof k.visible?k.visible:!1)});"function"===typeof f&&(b=f(b,k));b.children.forEach(function(c,d){var a=v({},k);v(a,{index:d,siblings:b.children.length,visible:b.visible});c=p(c,a);e.push(c);c.visible&&(l+=c.val)});b.visible=0<l||b.visible;f=x(g.value,l);v(b,{children:e,childrenTotal:l,isLeaf:b.visible&&!l,val:f});return b},updateRootId:function(b){if(k(b)){var h=k(b.options)?b.options:{};h=x(b.rootNode,h.rootId,"");k(b.userOptions)&&(b.userOptions.rootId=h);b.rootNode=h}return h}}});C(b,"Core/Axis/GridAxis.js",
[b["Core/Axis/Axis.js"],b["Core/Globals.js"],b["Core/Axis/Tick.js"],b["Core/Utilities.js"]],function(b,r,v,q){var n=q.addEvent,k=q.defined,A=q.erase,x=q.find,t=q.isArray,h=q.isNumber,y=q.merge,p=q.pick,f=q.timeUnits,w=q.wrap,m=r.Chart,a=function(e){var c=e.options;c.labels||(c.labels={});c.labels.align=p(c.labels.align,"center");e.categories||(c.showLastLabel=!1);e.labelRotation=0;c.labels.rotation=0};"";b.prototype.getMaxLabelDimensions=function(e,c){var d={width:0,height:0};c.forEach(function(c){c=
e[c];if(q.isObject(c,!0)){var a=q.isObject(c.label,!0)?c.label:{};c=a.getBBox?a.getBBox().height:0;a.textStr&&!h(a.textPxLength)&&(a.textPxLength=a.getBBox().width);var b=h(a.textPxLength)?Math.round(a.textPxLength):0;a.textStr&&(b=Math.round(a.getBBox().width));d.height=Math.max(c,d.height);d.width=Math.max(b,d.width)}});return d};r.dateFormats.W=function(a){a=new this.Date(a);var c=(this.get("Day",a)+6)%7,d=new this.Date(a.valueOf());this.set("Date",d,this.get("Date",a)-c+3);c=new this.Date(this.get("FullYear",
d),0,1);4!==this.get("Day",c)&&(this.set("Month",a,0),this.set("Date",a,1+(11-this.get("Day",c))%7));return(1+Math.floor((d.valueOf()-c.valueOf())/6048E5)).toString()};r.dateFormats.E=function(a){return this.dateFormat("%a",a,!0).charAt(0)};n(m,"afterSetChartSize",function(){this.axes.forEach(function(a){(a.grid&&a.grid.columns||[]).forEach(function(c){c.setAxisSize();c.setAxisTranslation()})})});n(v,"afterGetLabelPosition",function(a){var c=this.label,d=this.axis,e=d.reversed,b=d.chart,g=d.options.grid||
{},f=d.options.labels,k=f.align,u=l.Side[d.side],m=a.tickmarkOffset,E=d.tickPositions,z=this.pos-m;E=h(E[a.index+1])?E[a.index+1]-m:d.max+m;var F=d.tickSize("tick");m=F?F[0]:0;F=F?F[1]/2:0;if(!0===g.enabled){if("top"===u){g=d.top+d.offset;var G=g-m}else"bottom"===u?(G=b.chartHeight-d.bottom+d.offset,g=G+m):(g=d.top+d.len-d.translate(e?E:z),G=d.top+d.len-d.translate(e?z:E));"right"===u?(u=b.chartWidth-d.right+d.offset,e=u+m):"left"===u?(e=d.left+d.offset,u=e-m):(u=Math.round(d.left+d.translate(e?E:
z))-F,e=Math.round(d.left+d.translate(e?z:E))-F);this.slotWidth=e-u;a.pos.x="left"===k?u:"right"===k?e:u+(e-u)/2;a.pos.y=G+(g-G)/2;b=b.renderer.fontMetrics(f.style.fontSize,c.element);c=c.getBBox().height;f.useHTML?a.pos.y+=b.b+-(c/2):(c=Math.round(c/b.h),a.pos.y+=(b.b-(b.h-b.f))/2+-((c-1)*b.h/2));a.pos.x+=d.horiz&&f.x||0}});var g=function(){function a(c){this.axis=c}a.prototype.isOuterAxis=function(){var c=this.axis,a=c.grid.columnIndex,e=c.linkedParent&&c.linkedParent.grid.columns||c.grid.columns,
b=a?c.linkedParent:c,g=-1,f=0;c.chart[c.coll].forEach(function(a,d){a.side!==c.side||a.options.isInternal||(f=d,a===b&&(g=d))});return f===g&&(h(a)?e.length===a:!0)};a.prototype.renderBorder=function(c){var a=this.axis,e=a.chart.renderer,b=a.options;c=e.path(c).addClass("highcharts-axis-line").add(a.axisBorder);e.styledMode||c.attr({stroke:b.lineColor,"stroke-width":b.lineWidth,zIndex:7});return c};return a}(),l=function(){function e(){}e.compose=function(c){b.keepProps.push("grid");w(c.prototype,
"unsquish",e.wrapUnsquish);n(c,"init",e.onInit);n(c,"afterGetOffset",e.onAfterGetOffset);n(c,"afterGetTitlePosition",e.onAfterGetTitlePosition);n(c,"afterInit",e.onAfterInit);n(c,"afterRender",e.onAfterRender);n(c,"afterSetAxisTranslation",e.onAfterSetAxisTranslation);n(c,"afterSetOptions",e.onAfterSetOptions);n(c,"afterSetOptions",e.onAfterSetOptions2);n(c,"afterSetScale",e.onAfterSetScale);n(c,"afterTickSize",e.onAfterTickSize);n(c,"trimTicks",e.onTrimTicks);n(c,"destroy",e.onDestroy)};e.onAfterGetOffset=
function(){var c=this.grid;(c&&c.columns||[]).forEach(function(c){c.getOffset()})};e.onAfterGetTitlePosition=function(c){if(!0===(this.options.grid||{}).enabled){var a=this.axisTitle,b=this.height,g=this.horiz,f=this.left,l=this.offset,h=this.opposite,m=this.options.title,k=void 0===m?{}:m;m=this.top;var E=this.width,z=this.tickSize(),F=a&&a.getBBox().width,G=k.x||0,w=k.y||0,J=p(k.margin,g?5:10);a=this.chart.renderer.fontMetrics(k.style&&k.style.fontSize,a).f;z=(g?m+b:f)+(g?1:-1)*(h?-1:1)*(z?z[0]/
2:0)+(this.side===e.Side.bottom?a:0);c.titlePosition.x=g?f-F/2-J+G:z+(h?E:0)+l+G;c.titlePosition.y=g?z-(h?b:0)+(h?a:-a)/2+l+w:m-J+w}};e.onAfterInit=function(){var c=this.chart,d=this.options.grid;d=void 0===d?{}:d;var e=this.userOptions;d.enabled&&(a(this),w(this,"labelFormatter",function(a){var c=this.axis,d=this.value,e=c.tickPositions,b=(c.isLinked?c.linkedParent:c).series[0],g=d===e[0];e=d===e[e.length-1];var f=b&&x(b.options.data,function(a){return a[c.isXAxis?"x":"y"]===d});if(f&&b.is("gantt")){var l=
y(f);r.seriesTypes.gantt.prototype.pointClass.setGanttPointAliases(l)}this.isFirst=g;this.isLast=e;this.point=l;return a.call(this)}));if(d.columns)for(var g=this.grid.columns=[],f=this.grid.columnIndex=0;++f<d.columns.length;){var l=y(e,d.columns[d.columns.length-f-1],{linkedTo:0,type:"category",scrollbar:{enabled:!1}});delete l.grid.columns;l=new b(this.chart,l);l.grid.isColumn=!0;l.grid.columnIndex=f;A(c.axes,l);A(c[this.coll],l);g.push(l)}};e.onAfterRender=function(){var c,a=this.grid,b=this.options;
if(!0===(b.grid||{}).enabled){this.maxLabelDimensions=this.getMaxLabelDimensions(this.ticks,this.tickPositions);this.rightWall&&this.rightWall.destroy();if(this.grid&&this.grid.isOuterAxis()&&this.axisLine&&(b=b.lineWidth)){b=this.getLinePath(b);var g=b[0],f=b[1],l=((this.tickSize("tick")||[1])[0]-1)*(this.side===e.Side.top||this.side===e.Side.left?-1:1);"M"===g[0]&&"L"===f[0]&&(this.horiz?(g[2]+=l,f[2]+=l):(g[1]+=l,f[1]+=l));!this.horiz&&this.chart.marginRight&&(g=[g,["L",this.left,g[2]]],l=["L",
this.chart.chartWidth-this.chart.marginRight,this.toPixels(this.max+this.tickmarkOffset)],f=[["M",f[1],this.toPixels(this.max+this.tickmarkOffset)],l],this.grid.upperBorder||0===this.min%1||(this.grid.upperBorder=this.grid.renderBorder(g)),this.grid.upperBorder&&this.grid.upperBorder.animate({d:g}),this.grid.lowerBorder||0===this.max%1||(this.grid.lowerBorder=this.grid.renderBorder(f)),this.grid.lowerBorder&&this.grid.lowerBorder.animate({d:f}));this.grid.axisLineExtra?this.grid.axisLineExtra.animate({d:b}):
this.grid.axisLineExtra=this.grid.renderBorder(b);this.axisLine[this.showAxis?"show":"hide"](!0)}(a&&a.columns||[]).forEach(function(a){a.render()});!this.horiz&&this.chart.hasRendered&&(this.scrollbar||(null===(c=this.linkedParent)||void 0===c?0:c.scrollbar))&&(c=this.max,a=this.tickmarkOffset,b=this.tickPositions[this.tickPositions.length-1],f=this.tickPositions[0],this.min-f>a?this.ticks[f].label.hide():this.ticks[f].label.show(),b-c>a?this.ticks[b].label.hide():this.ticks[b].label.show(),b-c<
a&&0<b-c&&this.ticks[b].isLast?this.ticks[b].mark.hide():this.ticks[b-1]&&this.ticks[b-1].mark.show())}};e.onAfterSetAxisTranslation=function(){var a,d=this.tickPositions&&this.tickPositions.info,b=this.options,e=this.userOptions.labels||{};(b.grid||{}).enabled&&(this.horiz?(this.series.forEach(function(a){a.options.pointRange=0}),d&&b.dateTimeLabelFormats&&b.labels&&!k(e.align)&&(!1===b.dateTimeLabelFormats[d.unitName].range||1<d.count)&&(b.labels.align="left",k(e.x)||(b.labels.x=3))):"treegrid"!==
this.options.type&&(null===(a=this.grid)||void 0===a?0:a.columns)&&(this.minPointOffset=this.tickInterval))};e.onAfterSetOptions=function(a){var c=this.options;a=a.userOptions;var b=c&&q.isObject(c.grid,!0)?c.grid:{};if(!0===b.enabled){var e=y(!0,{className:"highcharts-grid-axis "+(a.className||""),dateTimeLabelFormats:{hour:{list:["%H:%M","%H"]},day:{list:["%A, %e. %B","%a, %e. %b","%E"]},week:{list:["Week %W","W%W"]},month:{list:["%B","%b","%o"]}},grid:{borderWidth:1},labels:{padding:2,style:{fontSize:"13px"}},
margin:0,title:{text:null,reserveSpace:!1,rotation:0},units:[["millisecond",[1,10,100]],["second",[1,10]],["minute",[1,5,15]],["hour",[1,6]],["day",[1]],["week",[1]],["month",[1]],["year",null]]},a);"xAxis"===this.coll&&(k(a.linkedTo)&&!k(a.tickPixelInterval)&&(e.tickPixelInterval=350),k(a.tickPixelInterval)||!k(a.linkedTo)||k(a.tickPositioner)||k(a.tickInterval)||(e.tickPositioner=function(a,c){var b=this.linkedParent&&this.linkedParent.tickPositions&&this.linkedParent.tickPositions.info;if(b){var d,
g=e.units;for(d=0;d<g.length;d++)if(g[d][0]===b.unitName){var l=d;break}if(g[l+1]){var z=g[l+1][0];var h=(g[l+1][1]||[1])[0]}else"year"===b.unitName&&(z="year",h=10*b.count);b=f[z];this.tickInterval=b*h;return this.getTimeTicks({unitRange:b,count:h,unitName:z},a,c,this.options.startOfWeek)}}));y(!0,this.options,e);this.horiz&&(c.minPadding=p(a.minPadding,0),c.maxPadding=p(a.maxPadding,0));h(c.grid.borderWidth)&&(c.tickWidth=c.lineWidth=b.borderWidth)}};e.onAfterSetOptions2=function(a){a=(a=a.userOptions)&&
a.grid||{};var c=a.columns;a.enabled&&c&&y(!0,this.options,c[c.length-1])};e.onAfterSetScale=function(){(this.grid.columns||[]).forEach(function(a){a.setScale()})};e.onAfterTickSize=function(a){var c=b.defaultLeftAxisOptions,e=this.horiz,g=this.maxLabelDimensions,f=this.options.grid;f=void 0===f?{}:f;f.enabled&&g&&(c=2*Math.abs(c.labels.x),e=e?f.cellHeight||c+g.height:c+g.width,t(a.tickSize)?a.tickSize[0]=e:a.tickSize=[e,0])};e.onDestroy=function(a){var c=this.grid;(c.columns||[]).forEach(function(c){c.destroy(a.keepEvents)});
c.columns=void 0};e.onInit=function(a){a=a.userOptions||{};var c=a.grid||{};c.enabled&&k(c.borderColor)&&(a.tickColor=a.lineColor=c.borderColor);this.grid||(this.grid=new g(this))};e.onTrimTicks=function(){var a=this.options,b=this.categories,e=this.tickPositions,g=e[0],f=e[e.length-1],l=this.linkedParent&&this.linkedParent.min||this.min,h=this.linkedParent&&this.linkedParent.max||this.max,m=this.tickInterval;!0!==(a.grid||{}).enabled||b||!this.horiz&&!this.isLinked||(g<l&&g+m>l&&!a.startOnTick&&
(e[0]=l),f>h&&f-m<h&&!a.endOnTick&&(e[e.length-1]=h))};e.wrapUnsquish=function(a){var c=this.options.grid;return!0===(void 0===c?{}:c).enabled&&this.categories?this.tickInterval:a.apply(this,Array.prototype.slice.call(arguments,1))};return e}();(function(a){a=a.Side||(a.Side={});a[a.top=0]="top";a[a.right=1]="right";a[a.bottom=2]="bottom";a[a.left=3]="left"})(l||(l={}));l.compose(b);return l});C(b,"Core/Axis/BrokenAxis.js",[b["Core/Axis/Axis.js"],b["Core/Series/Series.js"],b["Extensions/Stacking.js"],
b["Core/Utilities.js"]],function(b,r,v,q){var n=q.addEvent,k=q.find,A=q.fireEvent,x=q.isArray,t=q.isNumber,h=q.pick,y=function(){function p(b){this.hasBreaks=!1;this.axis=b}p.isInBreak=function(b,h){var f=b.repeat||Infinity,a=b.from,g=b.to-b.from;h=h>=a?(h-a)%f:f-(a-h)%f;return b.inclusive?h<=g:h<g&&0!==h};p.lin2Val=function(b){var f=this.brokenAxis;f=f&&f.breakArray;if(!f)return b;var h;for(h=0;h<f.length;h++){var a=f[h];if(a.from>=b)break;else a.to<b?b+=a.len:p.isInBreak(a,b)&&(b+=a.len)}return b};
p.val2Lin=function(b){var f=this.brokenAxis;f=f&&f.breakArray;if(!f)return b;var h=b,a;for(a=0;a<f.length;a++){var g=f[a];if(g.to<=b)h-=g.len;else if(g.from>=b)break;else if(p.isInBreak(g,b)){h-=b-g.from;break}}return h};p.prototype.findBreakAt=function(b,h){return k(h,function(f){return f.from<b&&b<f.to})};p.prototype.isInAnyBreak=function(b,k){var f=this.axis,a=f.options.breaks,g=a&&a.length,l;if(g){for(;g--;)if(p.isInBreak(a[g],b)){var e=!0;l||(l=h(a[g].showPoints,!f.isXAxis))}var c=e&&k?e&&!l:
e}return c};p.prototype.setBreaks=function(f,k){var m=this,a=m.axis,g=x(f)&&!!f.length;a.isDirty=m.hasBreaks!==g;m.hasBreaks=g;a.options.breaks=a.userOptions.breaks=f;a.forceRedraw=!0;a.series.forEach(function(a){a.isDirty=!0});g||a.val2lin!==p.val2Lin||(delete a.val2lin,delete a.lin2val);g&&(a.userOptions.ordinal=!1,a.lin2val=p.lin2Val,a.val2lin=p.val2Lin,a.setExtremes=function(a,e,c,d,g){if(m.hasBreaks){for(var f,h=this.options.breaks;f=m.findBreakAt(a,h);)a=f.to;for(;f=m.findBreakAt(e,h);)e=f.from;
e<a&&(e=a)}b.prototype.setExtremes.call(this,a,e,c,d,g)},a.setAxisTranslation=function(){b.prototype.setAxisTranslation.call(this);m.unitLength=null;if(m.hasBreaks){var g=a.options.breaks||[],e=[],c=[],d=0,f,k=a.userMin||a.min,n=a.userMax||a.max,q=h(a.pointRangePadding,0),t;g.forEach(function(a){f=a.repeat||Infinity;p.isInBreak(a,k)&&(k+=a.to%f-k%f);p.isInBreak(a,n)&&(n-=n%f-a.from%f)});g.forEach(function(a){r=a.from;for(f=a.repeat||Infinity;r-f>k;)r-=f;for(;r<k;)r+=f;for(t=r;t<n;t+=f)e.push({value:t,
move:"in"}),e.push({value:t+(a.to-a.from),move:"out",size:a.breakSize})});e.sort(function(a,b){return a.value===b.value?("in"===a.move?0:1)-("in"===b.move?0:1):a.value-b.value});var u=0;var r=k;e.forEach(function(a){u+="in"===a.move?1:-1;1===u&&"in"===a.move&&(r=a.value);0===u&&(c.push({from:r,to:a.value,len:a.value-r-(a.size||0)}),d+=a.value-r-(a.size||0))});a.breakArray=m.breakArray=c;m.unitLength=n-k-d+q;A(a,"afterBreaks");a.staticScale?a.transA=a.staticScale:m.unitLength&&(a.transA*=(n-a.min+
q)/m.unitLength);q&&(a.minPixelPadding=a.transA*a.minPointOffset);a.min=k;a.max=n}});h(k,!0)&&a.chart.redraw()};return p}();q=function(){function b(){}b.compose=function(b,k){b.keepProps.push("brokenAxis");var f=r.prototype;f.drawBreaks=function(a,b){var g=this,e=g.points,c,d,f,k;if(a&&a.brokenAxis&&a.brokenAxis.hasBreaks){var m=a.brokenAxis;b.forEach(function(b){c=m&&m.breakArray||[];d=a.isXAxis?a.min:h(g.options.threshold,a.min);e.forEach(function(e){k=h(e["stack"+b.toUpperCase()],e[b]);c.forEach(function(b){if(t(d)&&
t(k)){f=!1;if(d<b.from&&k>b.to||d>b.from&&k<b.from)f="pointBreak";else if(d<b.from&&k>b.from&&k<b.to||d>b.from&&k>b.to&&k<b.from)f="pointInBreak";f&&A(a,f,{point:e,brk:b})}})})})}};f.gappedPath=function(){var a=this.currentDataGrouping,b=a&&a.gapSize;a=this.options.gapSize;var f=this.points.slice(),e=f.length-1,c=this.yAxis,d;if(a&&0<e)for("value"!==this.options.gapUnit&&(a*=this.basePointRange),b&&b>a&&b>=this.basePointRange&&(a=b),d=void 0;e--;)d&&!1!==d.visible||(d=f[e+1]),b=f[e],!1!==d.visible&&
!1!==b.visible&&(d.x-b.x>a&&(d=(b.x+d.x)/2,f.splice(e+1,0,{isNull:!0,x:d}),c.stacking&&this.options.stacking&&(d=c.stacking.stacks[this.stackKey][d]=new v(c,c.options.stackLabels,!1,d,this.stack),d.total=0)),d=b);return this.getGraphPath(f)};n(b,"init",function(){this.brokenAxis||(this.brokenAxis=new y(this))});n(b,"afterInit",function(){"undefined"!==typeof this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)});n(b,"afterSetTickPositions",function(){var a=this.brokenAxis;if(a&&a.hasBreaks){var b=
this.tickPositions,f=this.tickPositions.info,e=[],c;for(c=0;c<b.length;c++)a.isInAnyBreak(b[c])||e.push(b[c]);this.tickPositions=e;this.tickPositions.info=f}});n(b,"afterSetOptions",function(){this.brokenAxis&&this.brokenAxis.hasBreaks&&(this.options.ordinal=!1)});n(k,"afterGeneratePoints",function(){var a=this.options.connectNulls,b=this.points,f=this.xAxis,e=this.yAxis;if(this.isDirty)for(var c=b.length;c--;){var d=b[c],h=!(null===d.y&&!1===a)&&(f&&f.brokenAxis&&f.brokenAxis.isInAnyBreak(d.x,!0)||
e&&e.brokenAxis&&e.brokenAxis.isInAnyBreak(d.y,!0));d.visible=h?!1:!1!==d.options.visible}});n(k,"afterRender",function(){this.drawBreaks(this.xAxis,["x"]);this.drawBreaks(this.yAxis,h(this.pointArrayMap,["y"]))})};return b}();q.compose(b,r);return q});C(b,"Core/Axis/TreeGridAxis.js",[b["Core/Axis/Axis.js"],b["Core/Axis/Tick.js"],b["Gantt/Tree.js"],b["Core/Axis/TreeGridTick.js"],b["Mixins/TreeSeries.js"],b["Core/Utilities.js"]],function(b,r,v,q,n,k){var A=n.getLevelOptions,x=k.addEvent,t=k.find,h=
k.fireEvent,y=k.isArray,p=k.isNumber,f=k.isObject,w=k.isString,m=k.merge,a=k.pick,g=k.wrap,l;(function(b){function c(a,b){var c=a.collapseStart||0;a=a.collapseEnd||0;a>=b&&(c-=.5);return{from:c,to:a,showPoints:!1}}function e(a,b,c){var e=[],d=[],g={},h={},k=-1,z="boolean"===typeof b?b:!1;a=v.getTree(a,{after:function(a){a=h[a.pos];var b=0,c=0;a.children.forEach(function(a){c+=(a.descendants||0)+1;b=Math.max((a.height||0)+1,b)});a.descendants=c;a.height=b;a.collapsed&&d.push(a)},before:function(a){var b=
f(a.data,!0)?a.data:{},c=w(b.name)?b.name:"",d=g[a.parent];d=f(d,!0)?h[d.pos]:null;var l=function(a){return a.name===c},m;z&&f(d,!0)&&(m=t(d.children,l))?(l=m.pos,m.nodes.push(a)):l=k++;h[l]||(h[l]=m={depth:d?d.depth+1:0,name:c,id:b.id,nodes:[a],children:[],pos:l},-1!==l&&e.push(c),f(d,!0)&&d.children.push(m));w(a.id)&&(g[a.id]=a);m&&!0===b.collapsed&&(m.collapsed=!0);a.pos=l}});h=function(a,b){var c=function(a,e,d){var g=e+(-1===e?0:b-1),h=(g-e)/2,k=e+h;a.nodes.forEach(function(a){var b=a.data;f(b,
!0)&&(b.y=e+(b.seriesIndex||0),delete b.seriesIndex);a.pos=k});d[k]=a;a.pos=k;a.tickmarkOffset=h+.5;a.collapseStart=g+.5;a.children.forEach(function(a){c(a,g+1,d);g=(a.collapseEnd||0)-.5});a.collapseEnd=g+.5;return d};return c(a["-1"],-1,{})}(h,c);return{categories:e,mapOfIdToNode:g,mapOfPosToGridNode:h,collapsedNodes:d,tree:a}}function k(a){a.target.axes.filter(function(a){return"treegrid"===a.options.type}).forEach(function(b){var c=b.options||{},d=c.labels,g=c.uniqueNames,h=0;c=c.max;if(!b.treeGrid.mapOfPosToGridNode||
b.series.some(function(a){return!a.hasRendered||a.isDirtyData||a.isDirty})){var k=b.series.reduce(function(a,b){b.visible&&((b.options.data||[]).forEach(function(c){b.options.keys&&b.options.keys.length&&(c=b.pointClass.prototype.optionsToObject.call({series:b},c),b.pointClass.setGanttPointAliases(c));f(c,!0)&&(c.seriesIndex=h,a.push(c))}),!0===g&&h++);return a},[]);if(c&&k.length<c)for(var l=k.length;l<=c;l++)k.push({name:l+"\u200b"});c=e(k,g||!1,!0===g?h:1);b.categories=c.categories;b.treeGrid.mapOfPosToGridNode=
c.mapOfPosToGridNode;b.hasNames=!0;b.treeGrid.tree=c.tree;b.series.forEach(function(a){var b=(a.options.data||[]).map(function(b){y(b)&&a.options.keys&&a.options.keys.length&&k.forEach(function(a){0<=b.indexOf(a.x)&&0<=b.indexOf(a.x2)&&(b=a)});return f(b,!0)?m(b):b});a.visible&&a.setData(b,!1)});b.treeGrid.mapOptionsToLevel=A({defaults:d,from:1,levels:d&&d.levels,to:b.treeGrid.tree&&b.treeGrid.tree.height});"beforeRender"===a.type&&(b.treeGrid.collapsedNodes=c.collapsedNodes)}})}function l(a,b){var c=
this.treeGrid.mapOptionsToLevel||{},e=this.ticks,d=e[b],f;if("treegrid"===this.options.type&&this.treeGrid.mapOfPosToGridNode){var g=this.treeGrid.mapOfPosToGridNode[b];(c=c[g.depth])&&(f={labels:c});d?(d.parameters.category=g.name,d.options=f,d.addLabel()):e[b]=new r(this,b,void 0,void 0,{category:g.name,tickmarkOffset:g.tickmarkOffset,options:f})}else a.apply(this,Array.prototype.slice.call(arguments,1))}function n(a){var b=this.options;b=(b=b&&b.labels)&&p(b.indentation)?b.indentation:0;var c=
a.apply(this,Array.prototype.slice.call(arguments,1));if("treegrid"===this.options.type&&this.treeGrid.mapOfPosToGridNode){var e=this.treeGrid.mapOfPosToGridNode[-1].height||0;c.width+=b*(e-1)}return c}function C(a,b,c){var d=this,f="treegrid"===c.type;d.treeGrid||(d.treeGrid=new I(d));f&&(x(b,"beforeRender",k),x(b,"beforeRedraw",k),x(b,"addSeries",function(a){a.options.data&&(a=e(a.options.data,c.uniqueNames||!1,1),d.treeGrid.collapsedNodes=(d.treeGrid.collapsedNodes||[]).concat(a.collapsedNodes))}),
x(d,"foundExtremes",function(){d.treeGrid.collapsedNodes&&d.treeGrid.collapsedNodes.forEach(function(a){var b=d.treeGrid.collapse(a);d.brokenAxis&&(d.brokenAxis.setBreaks(b,!1),d.treeGrid.collapsedNodes&&(d.treeGrid.collapsedNodes=d.treeGrid.collapsedNodes.filter(function(b){return a.collapseStart!==b.collapseStart||a.collapseEnd!==b.collapseEnd})))})}),x(d,"afterBreaks",function(){var a;"yAxis"===d.coll&&!d.staticScale&&(null===(a=d.chart.options.chart)||void 0===a?0:a.height)&&(d.isDirty=!0)}),
c=m({grid:{enabled:!0},labels:{align:"left",levels:[{level:void 0},{level:1,style:{fontWeight:"bold"}}],symbol:{type:"triangle",x:-5,y:-5,height:10,width:10,padding:5}},uniqueNames:!1},c,{reversed:!0,grid:{columns:void 0}}));a.apply(d,[b,c]);f&&(d.hasNames=!0,d.options.showLastLabel=!0)}function K(b){var c=this.options;"treegrid"===c.type?(this.min=a(this.userMin,c.min,this.dataMin),this.max=a(this.userMax,c.max,this.dataMax),h(this,"foundExtremes"),this.setAxisTranslation(),this.tickmarkOffset=.5,
this.tickInterval=1,this.tickPositions=this.treeGrid.mapOfPosToGridNode?this.treeGrid.getTickPositions():[]):b.apply(this,Array.prototype.slice.call(arguments,1))}var u=!1;b.compose=function(a){u||(g(a.prototype,"generateTick",l),g(a.prototype,"getMaxLabelDimensions",n),g(a.prototype,"init",C),g(a.prototype,"setTickInterval",K),q.compose(r),u=!0)};var I=function(){function a(a){this.axis=a}a.prototype.setCollapsedStatus=function(a){var b=this.axis,c=b.chart;b.series.forEach(function(b){var d=b.options.data;
if(a.id&&d){var e=c.get(a.id);b=d[b.data.indexOf(e)];e&&b&&(e.collapsed=a.collapsed,b.collapsed=a.collapsed)}})};a.prototype.collapse=function(a){var b=this.axis,d=b.options.breaks||[],e=c(a,b.max);d.push(e);a.collapsed=!0;b.treeGrid.setCollapsedStatus(a);return d};a.prototype.expand=function(a){var b=this.axis,d=b.options.breaks||[],e=c(a,b.max);a.collapsed=!1;b.treeGrid.setCollapsedStatus(a);return d.reduce(function(a,b){b.to===e.to&&b.from===e.from||a.push(b);return a},[])};a.prototype.getTickPositions=
function(){var a=this.axis,b=Math.floor(a.min/a.tickInterval)*a.tickInterval,c=Math.ceil(a.max/a.tickInterval)*a.tickInterval;return Object.keys(a.treeGrid.mapOfPosToGridNode||{}).reduce(function(d,e){e=+e;!(e>=b&&e<=c)||a.brokenAxis&&a.brokenAxis.isInAnyBreak(e)||d.push(e);return d},[])};a.prototype.isCollapsed=function(a){var b=this.axis,d=b.options.breaks||[],e=c(a,b.max);return d.some(function(a){return a.from===e.from&&a.to===e.to})};a.prototype.toggleCollapse=function(a){return this.isCollapsed(a)?
this.expand(a):this.collapse(a)};return a}();b.Additions=I})(l||(l={}));b.prototype.utils={getNode:v.getNode};l.compose(b);return l});C(b,"masters/modules/treegrid.src.js",[],function(){})});
//# sourceMappingURL=treegrid.js.map