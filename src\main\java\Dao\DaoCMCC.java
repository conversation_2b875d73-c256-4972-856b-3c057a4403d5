package Dao;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;


/**
 Title:Dao
 Description:TODO
 Auther:薛锟斤拷10170365
 Date:2017-10-17锟斤拷锟斤拷10:50:40
 */
public class DaoCMCC {
	
	protected static String Username="iwork2";
	protected static String Password="Root_123+NR-drl";
	protected static String url="*************************************************************************************************************";
	
	protected static String className="com.mysql.cj.jdbc.Driver";
	public   Connection conn=null;
	/**
	  锟斤拷锟斤拷锟斤拷
	 Description:锟洁构锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷菘锟�
	 Auther:薛锟斤拷10170365
	 Date:2017-10-17锟斤拷锟斤拷10:41:30
	 */
	public DaoCMCC() {
		try {
			Class.forName(className);
			//System.out.println(new Date()+" mysql锟斤拷JDBC锟斤拷锟斤拷锟截成癸拷锟斤拷");
		} catch (ClassNotFoundException ex) {
			System.out.println("SQLException: " + ex.getMessage());
		}
		try {
			conn=DriverManager.getConnection(url,Username,Password);
			//System.out.println(new Date()+" 锟斤拷锟斤拷锟斤拷菘锟斤拷锟斤拷锟斤拷锟缴癸拷锟斤拷");
		} catch (SQLException ex) {
			System.out.println("SQLException: " + ex.getMessage());
		}
	}
	/**
	  锟洁方锟斤拷
	 Description:锟截憋拷锟斤拷菘锟斤拷锟斤拷拥姆锟斤拷锟�
	 Auther:薛锟斤拷10170365
	 Date:2017-10-17锟斤拷锟斤拷11:13:21
	 */
	public  void close(){
		if(conn!=null){
			try {
				conn.close();
				//System.out.println(new Date()+" 锟缴癸拷锟较匡拷锟斤拷菘锟斤拷锟斤拷锟斤拷锟斤拷锟接ｏ拷");
			} catch (SQLException e) {
				System.out.println("SQLException: " + e.getMessage());
			}finally{
				conn = null;
			}
		}
	}
	/**
	 * 
	  锟洁方锟斤拷
	 Description:执锟斤拷Sql锟斤拷锟侥凤拷锟斤拷
	 Auther:薛锟斤拷10170365
	 Date:2017-10-17锟斤拷锟斤拷12:28:38
	 */
	public   int execute(String sql){
		
		if(conn==null) 
			new DaoCMCC();	//锟斤拷锟斤拷锟斤拷佣锟斤拷锟轿拷眨锟斤拷锟斤拷锟斤拷碌锟斤拷霉锟斤拷旆斤拷锟�
		try {
			return conn.createStatement().executeUpdate(sql);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			System.out.print("SQLException: " + e.getMessage());System.out.println("  错误");
			return -1;
		}
	}
	/**
	 * 
	  锟洁方锟斤拷
	 Description:锟斤拷锟介）锟斤拷询锟斤拷锟斤拷锟斤拷锟斤拷锟截诧拷询锟斤拷锟�
	 Auther:薛锟斤拷10170365
	 Date:2017-10-17锟斤拷锟斤拷02:35:58
	 */
	public   ResultSet executeQuery(String sql){
		try {
			if(conn==null)  new DaoCMCC();  
			return conn.createStatement(ResultSet.TYPE_SCROLL_SENSITIVE,
					ResultSet.CONCUR_UPDATABLE).executeQuery(sql);//执锟叫诧拷询
		} catch (SQLException e) {
			System.out.print("SQLException: " + e.getMessage());
			return null;
		} finally {
		}
	}
	
	
	
	public Connection getConnection() {
		return this.conn;
	}
	
	

}
