/**
 * 
 */
  var mytrenddata;
var xmlHttp=false;      
	//var document.getElementById("temchange").value = "";
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function init()
{
	function resetTabs(){
        $("#content > div").hide(); //Hide all content
        $("#tabs a").attr("id",""); //Reset id's      
    }
    var myUrl = window.location.href; //get URL
    var myUrlTab = myUrl.substring(myUrl.indexOf("#")); // For localhost/tabs.html#tab2, myUrlTab = #tab2     
    var myUrlTabName = myUrlTab.substring(0,4); // For the above example, myUrlTabName = #tab
    (function(){
        $("#content > div").hide(); // Initially hide all content
        $("#tabs li:first a").attr("id","current"); // Activate first tab
        $("#content > div:first").fadeIn(); // Show first tab content       
        $("#tabs a").on("click",function(e) {
            e.preventDefault();
            console.log(this);
            if ($(this).attr("id") == "current"){ //detection for current tab
             return       
            }
            else{             
            resetTabs();
            $(this).attr("id","current"); // Activate this
            $($(this).attr('name')).fadeIn(); // Show content for current tab
            }
        });

        for (i = 1; i <= $("#tabs li").length; i++) {
          if (myUrlTab == myUrlTabName + i) {
              resetTabs();
              $("a[name='"+myUrlTab+"']").attr("id","current"); // Activate url tab
              $(myUrlTab).fadeIn(); // Show url tab content        
          }
        }
    })()
}
//-----------------------------------------------父界面传递的数据----------------------------------------------------------
var loc =encodeURI( location.href);//获取整个跳转地址内容，其实就是你传过来的整个地址字符串
	//var loc = "https://iwork.zx.zte.com.cn/iWork2ChartHandler2/index.jsp?taskid=1908&tem=1&jobname=0";
	console.log("我的地址"+loc);
	var n1 = loc.length;//地址的总长
	var n2 = loc.indexOf("?");//取得=号的位置
	var parameter = decodeURIComponent(decodeURI(loc.substr(n2+1, n1-n2)));//截取从?号后面的内容,也就是参数列表，因为传过来的路径是加了码的，所以要解码
	var parameters  = parameter.split("&");//从&处拆分，返回字符串数组
	var padata = new Array();//创建一个用于保存具体值得数组
	for (var i = 0; i < parameters.length; i++) {
		var m1 = parameters[i].length;//获得每个键值对的长度
		var m2 = parameters[i].indexOf("=");//获得每个键值对=号的位置
		var value = parameters[i].substr(m2+1, m1-m2).split(":")[0];//获取每个键值对=号后面具体的值
		var key=parameters[i].split("=")[0];
		padata[key] = value;
		console.log("参数值"+i+":"+value);
	}
	//console.log(padata);
//--------------------------------------------标题渲染-------------------------------------------------------------	
//padata ={taskid: "1811", tem: "1", jobname: "中移TNR站内comp JR+JT测试"};
var t = document.getElementById("title");
	s="";
	s+="<a style=' color:rgb(0, 142, 211);font-size: 33px;font-weight: 550;font-family:Microsoft YaHei' id='titleaa'> </a><span style='margin-left:0.5%' class='layui-badge layui-bg-blue'>iWork</span>";
var temall = padata.tem;
 document.getElementById("temchange").value =padata.tem ;
 console.log(document.getElementById("temchange").value);
console.log(s);
//console.log(k2);
t.innerHTML=s;
console.log(t);
document.getElementById("titleaa").innerHTML = padata.taskid+" : "+padata.jobname+"  任务数据看板";
//---------------------------------------------判断百分比的函数------------------------------------------------------------
function comparePercentages(percentage1, percentage2) {
  const decimal1 = parseFloat(percentage1) / 100;
  const decimal2 = parseFloat(percentage2) / 100;

  if (decimal1 > decimal2) {
    return 'ok';
  } else if (decimal1 < decimal2) {
    return 'no';
  } else {
    return 'ok';
  }
} 

var tjump = document.getElementById("jump");
	sjump="";
	sjump+="<a  style='float:right;padding:0.5%;font-weight:bolder;color:#008ED3' href='/iWork2ChartHandler2/index.jsp?taskid="+padata.taskid+"&tem="+padata.tem+"'>旧版界面</a>";	  
	tjump.innerHTML=sjump;
//====================初始化回显值===================
var heldata = [];
//=======================================
	
//--------------------------------------------任务概览数据渲染【TAB1】-----------------------------------------------------------	
//初始页面的表格和图的数据传递和显示：
var datatable2 = [];//历史构建的表	
var chartx = "";//表x轴
var charty1 = "";//表时间Y轴
var charty21 = "";//表用例成功
var charty22 = "";//表用例失败
//----------------初始页面数据的ajax：
	  $.ajax({
	type: "get",
				   url:'taskindex',
				   //url:'initdata2.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					console.log(data[2].x);
					chartx = data[2].x;
					charty1 = data[2].y1;
					charty21 = data[2].y21;
					charty22 = data[2].y22;
					console.log(data[1].buildnumber);
					datatable2=data[0];
				document.getElementById("card1").innerHTML ="共"+data[1].buildnumber+"次";
				document.getElementById("card2").innerHTML ="共"+data[1].buildcase+"条";
				document.getElementById("card3").innerHTML =data[1].successrate;
				document.getElementById("card4").innerHTML ="成功："+data[1].newbuildcase.split("-")[0]+"  失败："+data[1].newbuildcase.split("-")[1];
					/*document.getElementById("newbuild").innerHTML =  "当前构建号为："+data[3][0].buildnum+"#"*/
				}
}); 
//===组件渲染：
layui.use('table', function(){
	var treeTable = layui.treeTable; 
	 
  treeTable.render({//任务概览表格数据
    elem: '#taskinfo'
    ,maxHeight: '500px'
    ,height:'400px'
    ,cols: [[
       {field:'casename',title: '构建号',width: '10%'}
      ,{field:'starttime', title: '开始时间',width: '12%', align: 'center'}
      ,{field:'endtime', title: '结束时间',align: 'center',width: '12%'}
      ,{field:'status',title: '状态',align: 'center',width: '10%',templet: function(d) {
																            if (d.status=="已完成") {
																              return '<span style="color: green;">' + d.status+ '</span>';
																            }
																            else if(d.status=="已取消"){
																	 return '<span style="color: red;">' + d.status + '</span>';
																}}}
      ,{field:'status',title: '用例执行情况',align: 'center',width: '10%',templet: function (d) {
						if(d.level==1){
							 return '<span class="layui-badge layui-bg-green">'+d.execute.split("-")[0]+'</span><span class="layui-badge ">'+d.execute.split("-")[1]+'</span>'
						}else{
							
						}}
                       }
      ,{field:'spendtime', title: '耗时(min)',align: 'center',width: '8%'} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'people', title: '数据下载',align: 'center',width: '20%',toolbar: '#bardown',}
      ,{field:'other', title: '其它信息',align: 'center',width:'6%',toolbar:'#bardown1'}
    ]],
   data:datatable2,
   text:{
                                none:'当前任务暂无历史构建数据！'},
   }); 	
  })
 //-----用例图表渲染
 var chartcase = echarts.init(document.getElementById('chartcase'));
option = {
		  tooltip: {
		    trigger: 'axis',
		    axisPointer: {
		      type: 'shadow'
		    }
		  },
		  title: {
			    text: '当前任务各构建号用例执行情况',
			    textStyle: {
			      fontSize: 12 // 标题字体大小
			    },
			    left:'center'
			  },
		  legend: {  right: '2%'},
		  grid: {
		    left: '3%',
		    right: '4%',
		    bottom: '3%',
		    containLabel: true
		  },
		  xAxis: [
		    {
		      type: 'category',
		      data: chartx,
		    }
		  ],
		  yAxis: [
		    {
		      type: 'value'
		    }
		  ],
		  series: [
		    {
		      name: 'success',
		      type: 'bar',
		      showBackground: true,
		       barMaxWidth:30,
		      stack: 'Ad',
		      emphasis: {
		        focus: 'series'
		      },
		       data: charty21,
		      color:'#00e272'
		    },
		    {
		      name: 'fail',
		      type: 'bar',
		      stack: 'Ad',
		       showBackground: true,
		       barMaxWidth:30,
		      emphasis: {
		        focus: 'series'
		      },
		      data: charty22,
		      color:'#feb56a'
		    }
		  ]
		};
        chartcase.setOption(option); 
 //-----时间图表渲染   
 var charttime = echarts.init(document.getElementById('charttime'));
        var option2 = {
            title: {
			    text: '当前任务各构建号时间统计',
			    textStyle: {
			      fontSize: 12 // 标题字体大小
			    },
			    left:'center'
			  },
            tooltip: {},
            grid: {
    		    left: '3%',
    		    right: '4%',
    		    bottom: '3%',
    		    containLabel: true
    		  },
            xAxis: {
                  data: chartx,
            },
            yAxis: {},
            series: [{
                name: '时间',
                type: 'bar',
                label: {
        show: true,
        position: 'inside'
      },
                showBackground: true,
                 data: charty1,
                 barMaxWidth:30,
                itemStyle: {
                    normal: {
                        // 随机显示
                    	color:'#008ED3'
                    },
                },
            }],
        };
        charttime.setOption(option2);
//--------------------------------------------layui_fomn_on监听事件-------------------------------------------------------------
 layui.use( function(){
	var treeTable = layui.treeTable;  
	var table = layui.table; 
	var form = layui.form; 
	 var dropdown = layui.dropdown;	
	 //console.log(padata.taskid);
//----------数据下载：
   treeTable.on( 'tool(taskinfo)',function (obj) {
	console.log(obj);
	console.log(obj.event);
	console.log(obj.data.casename);
	//console.log(padata.taskid);
	//var layEvent = obj.event;
	switch(obj.event){
                case 'log':               
                   downlog(padata.taskid,obj.data.casename);
                    break;
                case 'downdata':       
                    console.log(obj.data.kktem); 
                    if(obj.data.kktem=="5"){
							 log("kpi",obj.data.casename);
							}else {
								downkpi(padata.taskid,obj.data.casename);	
							}                         
                    break;
               case 'downdata2':               
                    log("mts",obj.data.casename);
                    break;

            }
	
});
//------------------------------mts的下载：
function log(type,number){
				//console.log("www");
				console.log(type);
				number = number.slice(0, -1);
				console.log(number);
							$.ajax({
				   type: "get",
				   url:'hisdatajudge',
				   async:false,
				   data: {jobid:padata.taskid,type:type,number:number},
				    dataType:'json',
				   success: function(data){
					console.log(data[0].msg);
					if(data[0].msg=="no"){
					layer.msg('暂时无当前构建号的'+type+'数据！');
					return false;
					}else{
						downloadFile("/iWork2ChartHandler2/filedatadownload?jobid="+padata.taskid+"&type="+type+"&number="+number+"");
					}
}
})
};
//----------健康度对比事件监听：
var datasea = "";
var datasea2 = "";
//console.log(padata.taskid);
form.on('submit(helcomp)', function(data){
	//console.log(padata.taskid);
	var comnum = xmSelect.get("#helnum",true).getValue('valueStr');//当前测试用例的值
	var comcase = xmSelect.get("#helcase",true).getValue('valueStr');//当前测试用例的值
	xmSelect.get("#helnum",true).closed();
	xmSelect.get("#helcase",true).closed();
	var heldata2 = {};
	heldata2.num  = comnum;
	heldata2.case  = comcase;
	heldata.push(heldata2);
	if(!(comcase.includes(","))||((comnum==""))){
			layer.msg('请选择需要分析的构建号及用例!');
			 return false; // 阻止表单跳转
			}else{
					console.log(comcase);
				$.ajax({
				   type: "post",
				   url:'http://***********:8080/iWorkDataHandlerAll/iWork2TableHandler',
				   //url:'https://iwork.zx.zte.com.cn/iWorkDataHandlerAll/iWork2TableHandler',
				   //url:'health.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:document.getElementById("temchange").value,usecase:encodeURI(comcase)},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					datasea = data[0];
					const arr = {version1:data[2][0],version2:data[2][1],usecasename1:data[2][2],usecasename2:data[2][3],task:data[2][4],imp:data[2][5],flat:data[2][6],worse:data[2][7],score:data[2][8],con:data[2][9]}
						//console.log(arr);
						Highcharts.chart('healthhcharts', {
		chart: {
				zoomType: 'xy',
		},
		title: {
				text: 'KPI指标情况',
				style:{
						fontSize:'30px'
					}
		},
		xAxis: [{
				categories: ["关键一级(A类)","关键一级(B类)","关键一级(C类)","关键一级","关键二级","关键三级"],
				crosshair: true,
				labels: {
					
					style:{
						fontSize:'20px'
					}
						//rotation:-30
				}				
		}],
		yAxis: [
		{
				gridLineWidth: 0,
				title: {
						text: '',
				},
				labels: {
						format: '{value} ',
						style:{
						fontSize:'20px'
					}
				}		
		}
		],
		tooltip: {
				shared: true
		},
		legend: {
				layout: 'vertical',
				align: 'left',
				x: 80,
				verticalAlign: 'top',
				y: 55,
				floating: true,
				backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || '#FFFFFF'
		},
		plotOptions: {
        column: {
			maxPointWidth: 80,
            //stacking: 'normal',
            dataLabels: {
                enabled: true,
                color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
                style: {
                    // 如果不需要数据标签阴影，可以尿 textOutline 设置丿 'none'
                    textOutline: '1px 1px black'
                }
            }
        }
    },
		series: [ 
		{
				name: '改善项',
				type: 'column',
				yAxis: 0,
				data:data[1][0],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#A6FFA6'
		},
		{
				name: '持平项',
				type: 'column',
				yAxis: 0,
				data:data[1][1],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#ACD6FF'
		},
		{
				name: '恶化项',
				type: 'column',
				yAxis: 0,
				data:data[1][2],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: 'red'
		}]
});
					table.reload('helscore', {
							    data: [arr] // 设置新的数据[打分]
							  });	
					table.reload('helkpidata', {
							    data: data[0] // 设置新的数据【具体指标对比】
							  });
				 
				},error() {
                  	layer.confirm('加载暂时出现了点问题！', {icon: 2, btn: ['好的，回主页吧', '去登陆页'], title: '错误信息'})                     
                  }
				   });
				    return false; // 阻止表单跳转
			}
})

//----------基础自主分析数据获取按钮监听：
 form.on('submit(zztbasic)', function(data){
	var fnnum= xmSelect.get("#zztbnum",true).getValue('valueStr');//构建号的值
	xmSelect.get("#zztbnum",true).closed();
	console.log(document.getElementById("temchange").value);
		if((fnnum=="")){
			layer.msg('请检查是否完成构建号的选择！');   
						 return false; // 阻止表单跳转
		}else{
			$.ajax({
				   type: "post",
				   //url:'https://iwork.zx.zte.com.cn/iWorkDataHandlerAll/iWork2ChartHandler',
				    url:'http://***********:8080/iWorkDataHandlerAll/iWork2ChartHandler',
				   //url:'zizhu.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:document.getElementById("temchange").value,usecase:"",index:"",build:fnnum,newcase:"",newcell:""},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					table.reload('zztbasic', {
							    data: data[0] // 设置新的数据[打分]
							  });	
							   return false; // 阻止表单跳转
				},error() {
                  	layer.confirm('加载暂时出现了点问题！', {icon: 2, btn: ['好的，回主页吧', '去登陆页'], title: '错误信息'})
                      
                  }
				})
				 return false; // 阻止表单跳转
		}
	 }) 
//----------基础自主分析【多组】数据获取按钮监听：
var zzttaddata = [];
 form.on('submit(zztadd)', function(data){
	var zznum = xmSelect.get("#zztbnum2",true).getValue('valueStr');//自主分析测试用例的值
	var zzcase = xmSelect.get("#zztcase",true).getValue('valueStr');//自主分析测试用例的值
	xmSelect.get("#zztbnum2",true).closed();
	xmSelect.get("#zztcase",true).closed();
	console.log(document.getElementById("temchange").value);
	var zzdata2 = {};
	if(zzcase==""){
		
	}else{
		zzdata2.case1  = zzcase.split(",")[0];
		zzdata2.case2  = zzcase.split(",")[1];
		zzttaddata.push(zzdata2);
	}
	
	if(!(zzcase.includes(","))||((zznum==""))){
			layer.msg('请检查是否完成构建号和测试用例的选择！');   
						 return false; // 阻止表单跳转
	}else{
		console.log(zzttaddata);
	
		xmSelect.get("#zztcase",true).setValue([]);
		xmSelect.get("#zztbnum2",true).closed();
		xmSelect.get("#zztcase",true).closed();
		table.reload('zztaddcase', {
							    data: zzttaddata// 将数据放进去
							  });	
							  
		 return false; // 阻止表单跳转
	}
})
//----------基础自主分析[多组]数据获取按钮监听：
var datamcom ;
		 form.on('submit(zzdcom)', function(data){
			//var adddata = table.getData('zztaddcase');
			//console.log(table.getData('zztaddcase').length);
			//console.log(table.getData('zztaddcase'));
			//console.log(encodeURI(JSON.stringify(table.getData('zztaddcase'))));
			/*if(table.getData('zztaddcase').length<1){*/
			if(adddata=""){
					layer.msg('请添加对应的对比组别！');   
				 return false; // 阻止表单跳转
			}else{	
				$.ajax({
				   type: "post",
				   //url:'urltest',
				    url:'http://***********:8080/iWorkDataHandlerAll/iWork2AutoTableHandler',
				    //url:'table222.json',
				    beforeSend: function() { //当一个Ajax请求开始时触发
					          layer.load(0, {
							        shade: 0.2
							      });
					        },
				   async:true,
				   data: {jobid:padata.taskid,kpitemplate:document.getElementById("temchange").value,usecase:encodeURI(JSON.stringify(table.getData('zztaddcase'))),index:"",fudu:""},
				    dataType:'json',
				   success: function(data){
					 layer.closeAll();
					console.log(data);
					datamcom = data;
					datasea2 = data[0];
					var lista = [{field:'level',title: '类别',align:'center',width: '10%',sort:true,'fixed':'left', templet: function (d) {return '<div style="text-align:left">' + d.level + '</div>' }}
      									,{field:'title', title: '指标',align:'center',width: '30%','fixed':'left', templet: function (d) {return '<div style="text-align:left">' + d.title + '</div>' }}
      									,{'field':'fudu','title': '门限','align':'center','width': '8%','fixed':'left',edit: 'text'}
      										];
                    for(var il = 0; il < data[2].length; il++){
								lista.push(data[2][il])
							}
					console.log(lista);	
							  table.reload('zzmtscore', {
							    data: data[1]// 将数据放进去
							  });	
					table.render({
						    elem: '#zzmtdata'
						    ,toolbar: '#bar2'
						    ,cols: [lista],
						   data:data[0],
						   maxHeight : '400px',
						   text:{
						                                none:'请选择对比用例！'}, 		  
            done: function (res, curr, count) {
            console.log(res.data);
            //merge(res);
            var that = this.elem.next();
            console.log(this.elem)
            console.log(that)
            res.data.forEach(function (item, index) {
               /* if (item.accstatus === "封禁") {
                  var tr = that.find(".layui-table-box tbody tr[data-index='" + index + "']");
                  tr.css("background-color", "yellow");
                  tr.find(".laytable-cell-1-0-9").css("color","red");
					console
                } */
                //console.log(index);
                //console.log(item);
                  trs = that.find(".layui-table-box tbody tr[data-index='" + index + "']").children();
                   // console.log(trs[1].dataset.field);
                    
                    for (var i = 0; i < trs.length; i++) {
                                    if (trs[i].dataset.field.includes('comparison' )&& trs[i].outerText=='恶化项') {
                                        trs[i].style.color = 'red';
                                    }else if(trs[i].dataset.field.includes('comparison' )&& trs[i].outerText=='提升项'){
											  trs[i].style.color = 'green';
										}
					}
            });
          }
                                          
						   });   
						  
							   return false; // 阻止表单跳转
				},error() {
					 layer.closeAll();
                  	layer.confirm('加载暂时出现了点问题！', {icon: 2, btn: ['好的，回主页吧', '去登陆页'], title: '错误信息'})
                      
                  }
				})
				
				 return false; // 阻止表单跳转
			};
			
			
			
		})
//table监听排序：		
 table.on('sort(hel2)', function(obj){
	table.reload('zzmtdata', {
							    done: function (res, curr, count) {
            var that = this.elem.next();
            res.data.forEach(function (item, index) {
                  trs = that.find(".layui-table-box tbody tr[data-index='" + index + "']").children();  
                    for (var i = 0; i < trs.length; i++) {
                                    if (trs[i].dataset.field.includes('comparison' )&& trs[i].outerText=='恶化项') {
                                        trs[i].style.color = 'red';
                                    }else if(trs[i].dataset.field.includes('comparison' )&& trs[i].outerText=='提升项'){
											  trs[i].style.color = 'green';
										}
					}
            });
          }// 将数据放进去
							  });
	 
})	
//
 table.on('edit(hel2)', function(obj){
	console.log(obj.value);
	console.log(obj.data);
	console.log(padata.taskid);
	console.log(padata.tem);
	console.log(table.getData('zztaddcase'));
	console.log(obj.data.title);
	$.ajax({
				   type: "post",
				   //url:'zzmudatacol',
				    url:'http://***********:8080/iWorkDataHandlerAll/iWork2AutoTableHandler',
				    //url:'table222.json',
				   beforeSend: function() { //当一个Ajax请求开始时触发
					          layer.load(0, {
							        shade: 0.2
							      });
					        },
				   async:true,
				   data: {jobid:padata.taskid,kpitemplate:document.getElementById("temchange").value,usecase:encodeURI(JSON.stringify(table.getData('zztaddcase'))),index:encodeURI(obj.data.title),fudu:encodeURI(obj.value)},
				    dataType:'json',
				   success: function(data){
						datamcom = data;
						 layer.closeAll();
						console.log(data);
						datasea2 = data[0];
						var clista = [{field:'level',title: '类别',align:'center',width: '10%',sort:true,'fixed':'left', templet: function (d) {return '<div style="text-align:left">' + d.level + '</div>' }}
      									,{field:'title', title: '指标',align:'center',width: '30%','fixed':'left', templet: function (d) {return '<div style="text-align:left">' + d.title + '</div>' }}
      									,{'field':'fudu','title': '门限','align':'center','width': '8%','fixed':'left',edit: 'text'}
      										];
      					for(var cil = 0; cil < data[2].length; cil++){
								clista.push(data[2][cil])
							}
						table.reload('zzmtscore', {
							    data: data[1]// 将数据放进去
							  });
						table.render({
						    elem: '#zzmtdata'
						    ,toolbar: '#bar2'
						    ,cols: [clista],
						   data:data[0],
						   maxHeight : '400px',
						   text:{
						                                none:'请选择对比用例！'}, 		  
            done: function (res, curr, count) {
            console.log(res.data);
            var that = this.elem.next();
            console.log(this.elem)
            console.log(that)
            res.data.forEach(function (item, index) {
                  trs = that.find(".layui-table-box tbody tr[data-index='" + index + "']").children();
                   for (var i = 0; i < trs.length; i++) {
                                    if (trs[i].dataset.field.includes('comparison' )&& trs[i].outerText=='恶化项') {
                                        trs[i].style.color = 'red';
                                    }else if(trs[i].dataset.field.includes('comparison' )&& trs[i].outerText=='提升项'){
											  trs[i].style.color = 'green';
										}
					}
            });
          }
                                          
						   });	  						
				}
				   })
	
	
})	
//删除组别：
table.on('tool(zztaddcase)', function (obj) {
            var data = obj.data;       
            //添加删除行
            if (obj.event === 'delete') {
                //alert('delete');
                layer.confirm('真的删除行么', function (index) {                  
                    var cacheData = layui.table.cache['zztaddcase'];//获取缓存
                    obj.del();//①移除这一行
                    var rowIndex = obj.tr.attr("data-index"); //获取行索引
                    cacheData.splice(rowIndex, 1);  //②彻底移除元素，从缓存里移除这一行
                    console.log(cacheData);
                    console.log( zzttaddata);
                    console.log(table.getData('zztaddcase'))
                    zzttaddata = table.getData('zztaddcase')
                    table.reload('zztaddcase', {
							    data: zzttaddata// 将数据放进去
							  });
                    layer.close(index);//关闭弹窗
                });
            }
        });
        
 //双击事件：
 table.on('rowDouble(hel2)', function(obj){
	     console.log( obj);
	     console.log(table.getData('zztaddcase'))
	     var rddata = table.getData('zztaddcase');
	     var newcase=[];
	     var newcell=[];
	     var jsondata = {};
	     for(let ird = 0; ird< rddata.length; ird++){
			var newcase0 = rddata[ird].case1.split("#")[0]+"#"+rddata[ird].case1.split("#")[3]+"#"+rddata[ird].case1.split("#")[4]+"#"+rddata[ird].case1.split("#")[5]
			var newcase1 = rddata[ird].case2.split("#")[0]+"#"+rddata[ird].case2.split("#")[3]+"#"+rddata[ird].case2.split("#")[4]+"#"+rddata[ird].case2.split("#")[5];				
	     	var newcell0 =  rddata[ird].case1.split("#")[1]+"_"+rddata[ird].case1.split("#")[2]
	        var newcell1 = rddata[ird].case2.split("#")[1]+"_"+rddata[ird].case2.split("#")[2];
	     	newcase.push(newcase0);
	     	newcase.push(newcase1);
	     	newcell.push(newcell0);
	     	newcell.push(newcell1);
	}
	    console.log(newcase);
	    console.log(unique(newcase).join(','));
	    console.log(newcell);
	    console.log(unique(newcell).join(','));
	    jsondata.jobid = padata.taskid;
	    jsondata.kpitemplate = padata.tem;
	    jsondata.index = obj.data.title;
	    jsondata.newcase = unique(newcase).join(',');
	    jsondata.newcell = unique(newcell).join(',');
	    console.log(jsondata);
	    mytrenddata = JSON.stringify(jsondata);
  layer.open({
								type: 2,
								area: ['85%', '70%'],
								title: ''+obj.data.title+'指标趋势图',
								content: ['/iWork2ChartHandler2/trendpic.jsp','no'],
								success: function(layero, index){
									
								},
							});
	
})       
//===============自主分析图的button===========================
 form.on('submit(zzchartbtn)', function(data){
	var zzcnum = xmSelect.get("#zzbnum",true).getValue('valueStr');//构建号的值
    var zzcase = xmSelect.get("#zzcase",true).getValue('valueStr');//当前测试用例的值
     var zzcell = xmSelect.get("#zzcell",true).getValue('valueStr');//当前测试用例的值
	var zzcindex= xmSelect.get("#zzbindex",true).getValue('valueStr');// 当前指标的值
	xmSelect.get("#zzbnum",true).closed();
	xmSelect.get("#zzcase",true).closed();
	xmSelect.get("#zzcell",true).closed();
	xmSelect.get("#zzbindex",true).closed();
		let arr = zzcase.split(",");
	let arrc = zzcell.split(",");
	  console.log(arr); // 获取表单数据
	  console.log(zzcase); // 获取表单数据
	   console.log(arrc); // 获取表单数据
	var caseend = "";
for (let i = 0; i < arr.length; i++) {
 	let arr1 = arr[i].split("#")
 	for(let i2 = 0; i2 < arrc.length; i2++){
	 	let arrc2= arrc[i2].split("_")
	caseend = caseend+','+arr1[0]+'#'+arrc2[0]+'#'+arrc2[1]+'#'+arr1[1]+'#'+arr1[2]+'#'+arr1[3];
	console.log(caseend); // 获取表单数据
}
}
			   if((zzcnum=="")||(zzcase=="")||(zzcell=="")||(zzcindex=="")){
					layer.msg('请是否完成检查构建号、测试用例、测试小区和指标的选择！');
		           
					 return false; // 阻止表单跳转
		}else{
			document.getElementById("zzbcall").innerHTML = "";
	 			$.ajax({
				   type: "post",
				   //url:'https://iwork.zx.zte.com.cn/iWorkDataHandlerAll/QueryAutoDataAnalysisServlet',//20表+4个图
				   //url:'chartsdata2.json',
				    url:'http://***********:8080/iWorkDataHandlerAll/iWork2ChartHandler',
				   async:false,
				     data: {jobid:padata.taskid,kpitemplate:document.getElementById("temchange").value,usecase:encodeURI(caseend.substring(1)),index:encodeURI(zzcindex),build:"",newcase:encodeURI(zzcase),newcell:zzcell},
				   //data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data[1].length);
					console.log(data);
					for (let icc= 0; icc<data[1].length; icc++) {
						if (icc% 2 === 0) {//偶数
							$('#zzbcall').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%'><div style='hight:300px' id='chart"+icc+"'></div></div>");	
						}else{//奇数
							$('#zzbcall').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%;margin-left:1%'><div style='hight:300px' id='chart"+icc+"' ></div></div>");
						}
					}
					for(let icf=0;icf<data[1].length;icf++){
						console.log(data[1][icf].y[0].max);
							const nullPositions = data[1][icf].case.reduce((acc, curr, index) => {
							  if (curr === null) {
							    acc.push(index);
							  }
							  return acc;
							}, []);
							nullPositions.unshift(0)	;
							var listband = [];
							for(let icb=0;icb<nullPositions.length-1;icb++){
									var cfband={from:nullPositions[icb],to:nullPositions[icb+1],color:'white',borderWidth:1,borderColor:'rgba(68, 170, 213, 0.1)',label:{text:''+data[1][icf].case[nullPositions[icb+1]-1]+''}}
									listband.push(cfband);
							}		  
						console.log(nullPositions);				
						//动态增加图	
						//渲染增加的图：
						Highcharts.chart('chart'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+data[1][icf].title+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [{
								categories: data[1][icf].x,
								labels: {
									  formatter: function() {
										if(Number.isInteger(this.value)){
												this.value = ""; 
										}else{
											
										}
				           return this.value
				        },
								rotation:90,
					         style :{
						fontSize: 8
						}
								},
						 plotLines:listband,
						 lineColor:'black'		
						}],
						yAxis: [{ // Primary yAxis
								max:data[1][icf].y[0].max,
								min:data[1][icf].y[0].min,
								tickInterval:data[1][icf].y[0].tickInterval,
								labels: {
										format: '{value}',
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								},
								title: {
										text: data[1][icf].y[0].label,
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								}
								
						}
							],
						tooltip: {
						},
						series:data[1][icf].series,
				
				});	
					}
				}
				}) 
				 return false; // 阻止表单跳转
		}	
})

table.on('toolbar(hel)', function(obj){
            switch(obj.event){
                case 'search':               
                    search();
                    break;
            }
        });
 function search(){
				//console.log("www");
				 var demoReload = document.getElementById("demoReload").value;
	                console.log(demoReload);
	                console.log(datasea);
	                
	                if(demoReload==""){		
		table.reload('helkpidata', {
							    data: datasea // 设置新的数据【具体指标对比】
							  }); 
			}else{
					 ssdatasea = datasea.filter((item) => {
							return item.level.includes(demoReload)||item.title.includes(demoReload)||item.old.includes(demoReload)||item.new.includes(demoReload)||item.tempchazhi.includes(demoReload)
							||item.tempfudu.includes(demoReload)||item.comparison.includes(demoReload)||item.deduction.includes(demoReload);
							});
							table.reload('helkpidata', {
							    data: ssdatasea // 设置新的数据【具体指标对比】
							  }); 
			}
       
	  document.getElementById("demoReload").value=demoReload;						    
}

table.on('toolbar(hel2)', function(obj){
            switch(obj.event){
                case 'search2':               
                    search2();
                    break;
            }
        });          
 function search2(){
				//console.log("www");
				 var demoReload2 = document.getElementById("demoReload2").value;
	                console.log(demoReload2);
	                console.log(datasea2);
	                if(demoReload2==""){	
					table.reload('zzmtdata', {
										    data: datasea2 // 设置新的数据【具体指标对比】
										  }); 
			}else{
					 ssdatasea2 = datasea2.filter((item) => {
						return item.level.includes(demoReload2)||item.title.includes(demoReload2)||item.old0.includes(demoReload2)||item.new0.includes(demoReload2)||item.tempchazhi0.includes(demoReload2)
						||item.tempfudu0.includes(demoReload2)||item.comparison0.includes(demoReload2)||item.deduction0.includes(demoReload2);
						});
						table.reload('zzmtdata', {
										    data: ssdatasea2 // 设置新的数据【具体指标对比】
										  }); 
			}                  
			console.log("过滤出来的array",datasea2)
			       
				  document.getElementById("demoReload2").value=demoReload2;						    
};  
///------------------------------------------------------------------------------- 
//下拉选择框：

//			  
	 })
//--------------------------------------------其他的function-------------------------------------------------------------	
function downloadFile(url) {
  var xhr = new XMLHttpRequest(); 
  xhr.open("GET", url, true);
   console.log(xhr);
  xhr.responseType = "blob";
  xhr.onload = function(e) {
    if (this.status == 200) {
      var blob = this.response;
      console.log(url);
      	var n2 = url.indexOf("?");//取得=号的位置
	var parameter = decodeURI(url.substr(n2+1, url.length-n2));//截取从?号后面的内容,也就是参数列表，因为传过来的路径是加了码的，所以要解码
	var parameters  = parameter.split("&");//从&处拆分，返回字符串数组
	console.log(parameters);
      var link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = ''+parameters[0].split("=")[1]+'_'+parameters[2].split("=")[1]+'_'+parameters[1].split("=")[1]+'.zip'; // 你需要设置正确的文件名
      link.click();
    }
  };
  xhr.send();
}	
///----下载log：
function downlog(id,buildno){
	console.log(buildno);
	var buildno1 = buildno.slice(0, -1);
	//console.log(buildno1);
	window.location.href='http://***********:8080/iWork2ChartHandler2/logdownload?id='+id+"&buildno="+buildno1
}	
//------下载kpi：
function downkpi(id,buildno){
	console.log(buildno);
	var buildno1 = buildno.slice(0, -1);
	//console.log(buildno1);
	window.location.href='http://***********:8080/iWork2ChartHandler2/kpidownload?jobid='+id+"&buildno="+buildno1+"&kpitemplate="+padata.tem
}	
function findMinMaxNonNullIndex(list) {
  let minIndex = list.findIndex((value) => value !== null);
  let maxIndex = minIndex;

  for (let i = minIndex + 1; i < list.length; i++) {
    if (list[i] !== null) {
      maxIndex = i;
    }
  }

  return [minIndex, maxIndex];
}
//--------------------------------------------标题渲染-------------------------------------------------------------
function unique (arr) { 
  return Array.from(new Set(arr)) 
}
	
//--------------------------------------------标题渲染-------------------------------------------------------------	
//--------------------------------------------标题渲染-------------------------------------------------------------	
//--------------------------------------------标题渲染-------------------------------------------------------------	