/*
 Highstock JS v9.0.1 (2021-02-15)

 Highstock as a plugin for Highcharts

 (c) 2010-2021 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(d){"object"===typeof module&&module.exports?(d["default"]=d,module.exports=d):"function"===typeof define&&define.amd?define("highcharts/modules/stock",["highcharts"],function(K){d(K);d.Highcharts=K;return d}):d("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(d){function K(d,B,x,n){d.hasOwnProperty(B)||(d[B]=n.apply(null,x))}d=d?d._modules:{};K(d,"Core/Axis/NavigatorAxis.js",[d["Core/Globals.js"],d["Core/Utilities.js"]],function(d,B){var x=d.isTouchDevice,n=B.addEvent,w=B.correctFloat,
D=B.defined,A=B.isNumber,v=B.pick,C=function(){function a(a){this.axis=a}a.prototype.destroy=function(){this.axis=void 0};a.prototype.toFixedRange=function(a,h,l,e){var c=this.axis,k=c.chart;k=k&&k.fixedRange;var G=(c.pointRange||0)/2;a=v(l,c.translate(a,!0,!c.horiz));h=v(e,c.translate(h,!0,!c.horiz));c=k&&(h-a)/k;D(l)||(a=w(a+G));D(e)||(h=w(h-G));.7<c&&1.3>c&&(e?a=h-k:h=a+k);A(a)&&A(h)||(a=h=void 0);return{min:a,max:h}};return a}();return function(){function a(){}a.compose=function(a){a.keepProps.push("navigatorAxis");
n(a,"init",function(){this.navigatorAxis||(this.navigatorAxis=new C(this))});n(a,"zoom",function(a){var l=this.chart.options,e=l.navigator,c=this.navigatorAxis,k=l.chart.pinchType,h=l.rangeSelector;l=l.chart.zoomType;this.isXAxis&&(e&&e.enabled||h&&h.enabled)&&("y"===l?a.zoomed=!1:(!x&&"xy"===l||x&&"xy"===k)&&this.options.range&&(e=c.previousZoom,D(a.newMin)?c.previousZoom=[this.min,this.max]:e&&(a.newMin=e[0],a.newMax=e[1],c.previousZoom=void 0)));"undefined"!==typeof a.zoomed&&a.preventDefault()})};
a.AdditionsClass=C;return a}()});K(d,"Core/Axis/ScrollbarAxis.js",[d["Core/Globals.js"],d["Core/Utilities.js"]],function(d,B){var x=B.addEvent,n=B.defined,w=B.pick;return function(){function D(){}D.compose=function(A,v){var C=function(a){var p=w(a.options&&a.options.min,a.min),h=w(a.options&&a.options.max,a.max);return{axisMin:p,axisMax:h,scrollMin:n(a.dataMin)?Math.min(p,a.min,a.dataMin,w(a.threshold,Infinity)):p,scrollMax:n(a.dataMax)?Math.max(h,a.max,a.dataMax,w(a.threshold,-Infinity)):h}};x(A,
"afterInit",function(){var a=this;a.options&&a.options.scrollbar&&a.options.scrollbar.enabled&&(a.options.scrollbar.vertical=!a.horiz,a.options.startOnTick=a.options.endOnTick=!1,a.scrollbar=new v(a.chart.renderer,a.options.scrollbar,a.chart),x(a.scrollbar,"changed",function(p){var h=C(a),l=h.axisMax,e=h.scrollMin,c=h.scrollMax-e;n(h.axisMin)&&n(l)&&(a.horiz&&!a.reversed||!a.horiz&&a.reversed?(h=e+c*this.to,e+=c*this.from):(h=e+c*(1-this.from),e+=c*(1-this.to)),w(this.options.liveRedraw,d.svg&&!d.isTouchDevice&&
!this.chart.isBoosting)||"mouseup"===p.DOMType||"touchend"===p.DOMType||!n(p.DOMType)?a.setExtremes(e,h,!0,"mousemove"!==p.DOMType&&"touchmove"!==p.DOMType,p):this.setRange(this.from,this.to))}))});x(A,"afterRender",function(){var a=C(this),p=a.scrollMin,h=a.scrollMax;a=this.scrollbar;var l=this.axisTitleMargin+(this.titleOffset||0),e=this.chart.scrollbarsOffsets,c=this.options.margin||0;a&&(this.horiz?(this.opposite||(e[1]+=l),a.position(this.left,this.top+this.height+2+e[1]-(this.opposite?c:0),
this.width,this.height),this.opposite||(e[1]+=c),l=1):(this.opposite&&(e[0]+=l),a.position(this.left+this.width+2+e[0]-(this.opposite?0:c),this.top,this.width,this.height),this.opposite&&(e[0]+=c),l=0),e[l]+=a.size+a.options.margin,isNaN(p)||isNaN(h)||!n(this.min)||!n(this.max)||this.min===this.max?a.setRange(0,1):(e=(this.min-p)/(h-p),p=(this.max-p)/(h-p),this.horiz&&!this.reversed||!this.horiz&&this.reversed?a.setRange(e,p):a.setRange(1-p,1-e)))});x(A,"afterGetOffset",function(){var a=this.horiz?
2:1,p=this.scrollbar;p&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[a]+=p.size+p.options.margin)})};return D}()});K(d,"Core/Scrollbar.js",[d["Core/Axis/Axis.js"],d["Core/Globals.js"],d["Core/Color/Palette.js"],d["Core/Axis/ScrollbarAxis.js"],d["Core/Utilities.js"],d["Core/Options.js"]],function(d,B,x,n,w,D){var A=w.addEvent,v=w.correctFloat,C=w.defined,a=w.destroyObjectProperties,p=w.fireEvent,h=w.merge,l=w.pick,e=w.removeEvent;w=D.defaultOptions;var c=B.isTouchDevice,k=B.swapXY=function(a,
m){m&&a.forEach(function(m){for(var f=m.length,a,q=0;q<f;q+=2)a=m[q+1],"number"===typeof a&&(m[q+1]=m[q+2],m[q+2]=a)});return a};D=function(){function G(m,a,f){this._events=[];this.from=this.chartY=this.chartX=0;this.scrollbar=this.group=void 0;this.scrollbarButtons=[];this.scrollbarGroup=void 0;this.scrollbarLeft=0;this.scrollbarRifles=void 0;this.scrollbarStrokeWidth=1;this.to=this.size=this.scrollbarTop=0;this.track=void 0;this.trackBorderWidth=1;this.userOptions={};this.y=this.x=0;this.chart=
f;this.options=a;this.renderer=f.renderer;this.init(m,a,f)}G.prototype.addEvents=function(){var m=this.options.inverted?[1,0]:[0,1],a=this.scrollbarButtons,f=this.scrollbarGroup.element,k=this.track.element,c=this.mouseDownHandler.bind(this),e=this.mouseMoveHandler.bind(this),l=this.mouseUpHandler.bind(this);m=[[a[m[0]].element,"click",this.buttonToMinClick.bind(this)],[a[m[1]].element,"click",this.buttonToMaxClick.bind(this)],[k,"click",this.trackClick.bind(this)],[f,"mousedown",c],[f.ownerDocument,
"mousemove",e],[f.ownerDocument,"mouseup",l]];B.hasTouch&&m.push([f,"touchstart",c],[f.ownerDocument,"touchmove",e],[f.ownerDocument,"touchend",l]);m.forEach(function(m){A.apply(null,m)});this._events=m};G.prototype.buttonToMaxClick=function(m){var a=(this.to-this.from)*l(this.options.step,.2);this.updatePosition(this.from+a,this.to+a);p(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:m})};G.prototype.buttonToMinClick=function(m){var a=v(this.to-this.from)*l(this.options.step,
.2);this.updatePosition(v(this.from-a),v(this.to-a));p(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:m})};G.prototype.cursorToScrollbarPosition=function(m){var a=this.options;a=a.minWidth>this.calculatedWidth?a.minWidth:0;return{chartX:(m.chartX-this.x-this.xOffset)/(this.barWidth-a),chartY:(m.chartY-this.y-this.yOffset)/(this.barWidth-a)}};G.prototype.destroy=function(){var m=this.chart.scroller;this.removeEvents();["track","scrollbarRifles","scrollbar","scrollbarGroup",
"group"].forEach(function(a){this[a]&&this[a].destroy&&(this[a]=this[a].destroy())},this);m&&this===m.scrollbar&&(m.scrollbar=null,a(m.scrollbarButtons))};G.prototype.drawScrollbarButton=function(a){var m=this.renderer,f=this.scrollbarButtons,H=this.options,c=this.size;var e=m.g().add(this.group);f.push(e);e=m.rect().addClass("highcharts-scrollbar-button").add(e);this.chart.styledMode||e.attr({stroke:H.buttonBorderColor,"stroke-width":H.buttonBorderWidth,fill:H.buttonBackgroundColor});e.attr(e.crisp({x:-.5,
y:-.5,width:c+1,height:c+1,r:H.buttonBorderRadius},e.strokeWidth()));e=m.path(k([["M",c/2+(a?-1:1),c/2-3],["L",c/2+(a?-1:1),c/2+3],["L",c/2+(a?2:-2),c/2]],H.vertical)).addClass("highcharts-scrollbar-arrow").add(f[a]);this.chart.styledMode||e.attr({fill:H.buttonArrowColor})};G.prototype.init=function(a,q,f){this.scrollbarButtons=[];this.renderer=a;this.userOptions=q;this.options=h(G.defaultOptions,q);this.chart=f;this.size=l(this.options.size,this.options.height);q.enabled&&(this.render(),this.addEvents())};
G.prototype.mouseDownHandler=function(a){a=this.chart.pointer.normalize(a);a=this.cursorToScrollbarPosition(a);this.chartX=a.chartX;this.chartY=a.chartY;this.initPositions=[this.from,this.to];this.grabbedCenter=!0};G.prototype.mouseMoveHandler=function(a){var m=this.chart.pointer.normalize(a),f=this.options.vertical?"chartY":"chartX",k=this.initPositions||[];!this.grabbedCenter||a.touches&&0===a.touches[0][f]||(m=this.cursorToScrollbarPosition(m)[f],f=this[f],f=m-f,this.hasDragged=!0,this.updatePosition(k[0]+
f,k[1]+f),this.hasDragged&&p(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:a.type,DOMEvent:a}))};G.prototype.mouseUpHandler=function(a){this.hasDragged&&p(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:a.type,DOMEvent:a});this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null};G.prototype.position=function(a,k,f,c){var m=this.options.vertical,q=0,e=this.rendered?"animate":"attr";this.x=a;this.y=k+this.trackBorderWidth;this.width=f;this.xOffset=
this.height=c;this.yOffset=q;m?(this.width=this.yOffset=f=q=this.size,this.xOffset=k=0,this.barWidth=c-2*f,this.x=a+=this.options.margin):(this.height=this.xOffset=c=k=this.size,this.barWidth=f-2*c,this.y+=this.options.margin);this.group[e]({translateX:a,translateY:this.y});this.track[e]({width:f,height:c});this.scrollbarButtons[1][e]({translateX:m?0:f-k,translateY:m?c-q:0})};G.prototype.removeEvents=function(){this._events.forEach(function(a){e.apply(null,a)});this._events.length=0};G.prototype.render=
function(){var a=this.renderer,c=this.options,f=this.size,e=this.chart.styledMode,l;this.group=l=a.g("scrollbar").attr({zIndex:c.zIndex,translateY:-99999}).add();this.track=a.rect().addClass("highcharts-scrollbar-track").attr({x:0,r:c.trackBorderRadius||0,height:f,width:f}).add(l);e||this.track.attr({fill:c.trackBackgroundColor,stroke:c.trackBorderColor,"stroke-width":c.trackBorderWidth});this.trackBorderWidth=this.track.strokeWidth();this.track.attr({y:-this.trackBorderWidth%2/2});this.scrollbarGroup=
a.g().add(l);this.scrollbar=a.rect().addClass("highcharts-scrollbar-thumb").attr({height:f,width:f,r:c.barBorderRadius||0}).add(this.scrollbarGroup);this.scrollbarRifles=a.path(k([["M",-3,f/4],["L",-3,2*f/3],["M",0,f/4],["L",0,2*f/3],["M",3,f/4],["L",3,2*f/3]],c.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup);e||(this.scrollbar.attr({fill:c.barBackgroundColor,stroke:c.barBorderColor,"stroke-width":c.barBorderWidth}),this.scrollbarRifles.attr({stroke:c.rifleColor,"stroke-width":1}));
this.scrollbarStrokeWidth=this.scrollbar.strokeWidth();this.scrollbarGroup.translate(-this.scrollbarStrokeWidth%2/2,-this.scrollbarStrokeWidth%2/2);this.drawScrollbarButton(0);this.drawScrollbarButton(1)};G.prototype.setRange=function(a,c){var f=this.options,m=f.vertical,k=f.minWidth,e=this.barWidth,q,l=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(C(e)){a=Math.max(a,0);var h=Math.ceil(e*a);this.calculatedWidth=q=v(e*Math.min(c,1)-h);q<
k&&(h=(e-k+q)*a,q=k);k=Math.floor(h+this.xOffset+this.yOffset);e=q/2-.5;this.from=a;this.to=c;m?(this.scrollbarGroup[l]({translateY:k}),this.scrollbar[l]({height:q}),this.scrollbarRifles[l]({translateY:e}),this.scrollbarTop=k,this.scrollbarLeft=0):(this.scrollbarGroup[l]({translateX:k}),this.scrollbar[l]({width:q}),this.scrollbarRifles[l]({translateX:e}),this.scrollbarLeft=k,this.scrollbarTop=0);12>=q?this.scrollbarRifles.hide():this.scrollbarRifles.show(!0);!1===f.showFull&&(0>=a&&1<=c?this.group.hide():
this.group.show());this.rendered=!0}};G.prototype.trackClick=function(a){var m=this.chart.pointer.normalize(a),f=this.to-this.from,c=this.y+this.scrollbarTop,k=this.x+this.scrollbarLeft;this.options.vertical&&m.chartY>c||!this.options.vertical&&m.chartX>k?this.updatePosition(this.from+f,this.to+f):this.updatePosition(this.from-f,this.to-f);p(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:a})};G.prototype.update=function(a){this.destroy();this.init(this.chart.renderer,h(!0,
this.options,a),this.chart)};G.prototype.updatePosition=function(a,c){1<c&&(a=v(1-v(c-a)),c=1);0>a&&(c=v(c-a),a=0);this.from=a;this.to=c};G.defaultOptions={height:c?20:14,barBorderRadius:0,buttonBorderRadius:0,liveRedraw:void 0,margin:10,minWidth:6,step:.2,zIndex:3,barBackgroundColor:x.neutralColor20,barBorderWidth:1,barBorderColor:x.neutralColor20,buttonArrowColor:x.neutralColor80,buttonBackgroundColor:x.neutralColor10,buttonBorderColor:x.neutralColor20,buttonBorderWidth:1,rifleColor:x.neutralColor80,
trackBackgroundColor:x.neutralColor5,trackBorderColor:x.neutralColor5,trackBorderWidth:1};return G}();B.Scrollbar||(w.scrollbar=h(!0,D.defaultOptions,w.scrollbar),B.Scrollbar=D,n.compose(d,D));return B.Scrollbar});K(d,"Core/Navigator.js",[d["Core/Axis/Axis.js"],d["Core/Chart/Chart.js"],d["Core/Color/Color.js"],d["Core/Globals.js"],d["Core/Axis/NavigatorAxis.js"],d["Core/Options.js"],d["Core/Color/Palette.js"],d["Core/Scrollbar.js"],d["Core/Series/Series.js"],d["Core/Series/SeriesRegistry.js"],d["Core/Utilities.js"]],
function(d,B,x,n,w,D,A,v,C,a,p){x=x.parse;var h=n.hasTouch,l=n.isTouchDevice,e=D.defaultOptions,c=p.addEvent,k=p.clamp,G=p.correctFloat,m=p.defined,q=p.destroyObjectProperties,f=p.erase,H=p.extend,N=p.find,M=p.isArray,J=p.isNumber,L=p.merge,E=p.pick,F=p.removeEvent,y=p.splat,r=function(b){for(var g=[],u=1;u<arguments.length;u++)g[u-1]=arguments[u];g=[].filter.call(g,J);if(g.length)return Math[b].apply(0,g)};D="undefined"===typeof a.seriesTypes.areaspline?"line":"areaspline";H(e,{navigator:{height:40,
margin:25,maskInside:!0,handles:{width:7,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:A.neutralColor5,borderColor:A.neutralColor40},maskFill:x(A.highlightColor60).setOpacity(.3).get(),outlineColor:A.neutralColor20,outlineWidth:1,series:{type:D,fillOpacity:.05,lineWidth:1,compare:null,dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,smoothed:!0,units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",
[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{overscroll:0,className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:A.neutralColor10,gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:A.neutralColor40},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",
gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:null},tickLength:0,tickWidth:0}}});n.Renderer.prototype.symbols["navigator-handle"]=function(b,g,u,z,a){b=(a&&a.width||0)/2;g=Math.round(b/3)+.5;a=a&&a.height||0;return[["M",-b-1,.5],["L",b,.5],["L",b,a+.5],["L",-b-1,a+.5],["L",-b-1,.5],["M",-g,4],["L",-g,a-3],["M",g-1,4],["L",g-1,a-3]]};var b=function(){function b(g){this.zoomedMin=this.zoomedMax=this.yAxis=this.xAxis=this.top=this.size=
this.shades=this.rendered=this.range=this.outlineHeight=this.outline=this.opposite=this.navigatorSize=this.navigatorSeries=this.navigatorOptions=this.navigatorGroup=this.navigatorEnabled=this.left=this.height=this.handles=this.chart=this.baseSeries=void 0;this.init(g)}b.prototype.drawHandle=function(g,b,z,a){var u=this.navigatorOptions.handles.height;this.handles[b][a](z?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(g,10)+.5-u)}:{translateX:Math.round(this.left+
parseInt(g,10)),translateY:Math.round(this.top+this.height/2-u/2-1)})};b.prototype.drawOutline=function(g,b,z,a){var u=this.navigatorOptions.maskInside,t=this.outline.strokeWidth(),r=t/2,I=t%2/2;t=this.outlineHeight;var f=this.scrollbarHeight||0,y=this.size,c=this.left-f,k=this.top;z?(c-=r,z=k+b+I,b=k+g+I,I=[["M",c+t,k-f-I],["L",c+t,z],["L",c,z],["L",c,b],["L",c+t,b],["L",c+t,k+y+f]],u&&I.push(["M",c+t,z-r],["L",c+t,b+r])):(g+=c+f-I,b+=c+f-I,k+=r,I=[["M",c,k],["L",g,k],["L",g,k+t],["L",b,k+t],["L",
b,k],["L",c+y+2*f,k]],u&&I.push(["M",g-r,k],["L",b+r,k]));this.outline[a]({d:I})};b.prototype.drawMasks=function(g,b,z,a){var u=this.left,t=this.top,r=this.height;if(z){var I=[u,u,u];var f=[t,t+g,t+b];var c=[r,r,r];var y=[g,b-g,this.size-b]}else I=[u,u+g,u+b],f=[t,t,t],c=[g,b-g,this.size-b],y=[r,r,r];this.shades.forEach(function(g,b){g[a]({x:I[b],y:f[b],width:c[b],height:y[b]})})};b.prototype.renderElements=function(){var g=this,b=g.navigatorOptions,z=b.maskInside,a=g.chart,t=a.renderer,r,f={cursor:a.inverted?
"ns-resize":"ew-resize"};g.navigatorGroup=r=t.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();[!z,z,!z].forEach(function(u,z){g.shades[z]=t.rect().addClass("highcharts-navigator-mask"+(1===z?"-inside":"-outside")).add(r);a.styledMode||g.shades[z].attr({fill:u?b.maskFill:"rgba(0,0,0,0)"}).css(1===z&&f)});g.outline=t.path().addClass("highcharts-navigator-outline").add(r);a.styledMode||g.outline.attr({"stroke-width":b.outlineWidth,stroke:b.outlineColor});b.handles.enabled&&[0,1].forEach(function(u){b.handles.inverted=
a.inverted;g.handles[u]=t.symbol(b.handles.symbols[u],-b.handles.width/2-1,0,b.handles.width,b.handles.height,b.handles);g.handles[u].attr({zIndex:7-u}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][u]).add(r);if(!a.styledMode){var z=b.handles;g.handles[u].attr({fill:z.backgroundColor,stroke:z.borderColor,"stroke-width":z.lineWidth}).css(f)}})};b.prototype.update=function(g){(this.series||[]).forEach(function(g){g.baseSeries&&delete g.baseSeries.navigatorSeries});
this.destroy();L(!0,this.chart.options.navigator,this.options,g);this.init(this.chart)};b.prototype.render=function(g,b,z,a){var u=this.chart,t=this.scrollbarHeight,r,f=this.xAxis,I=f.pointRange||0;var c=f.navigatorAxis.fake?u.xAxis[0]:f;var y=this.navigatorEnabled,e,l=this.rendered;var q=u.inverted;var H=u.xAxis[0].minRange,h=u.xAxis[0].options.maxRange;if(!this.hasDragged||m(z)){g=G(g-I/2);b=G(b+I/2);if(!J(g)||!J(b))if(l)z=0,a=E(f.width,c.width);else return;this.left=E(f.left,u.plotLeft+t+(q?u.plotWidth:
0));this.size=e=r=E(f.len,(q?u.plotHeight:u.plotWidth)-2*t);u=q?t:r+2*t;z=E(z,f.toPixels(g,!0));a=E(a,f.toPixels(b,!0));J(z)&&Infinity!==Math.abs(z)||(z=0,a=u);g=f.toValue(z,!0);b=f.toValue(a,!0);var F=Math.abs(G(b-g));F<H?this.grabbedLeft?z=f.toPixels(b-H-I,!0):this.grabbedRight&&(a=f.toPixels(g+H+I,!0)):m(h)&&G(F-I)>h&&(this.grabbedLeft?z=f.toPixels(b-h-I,!0):this.grabbedRight&&(a=f.toPixels(g+h+I,!0)));this.zoomedMax=k(Math.max(z,a),0,e);this.zoomedMin=k(this.fixedWidth?this.zoomedMax-this.fixedWidth:
Math.min(z,a),0,e);this.range=this.zoomedMax-this.zoomedMin;e=Math.round(this.zoomedMax);z=Math.round(this.zoomedMin);y&&(this.navigatorGroup.attr({visibility:"visible"}),l=l&&!this.hasDragged?"animate":"attr",this.drawMasks(z,e,q,l),this.drawOutline(z,e,q,l),this.navigatorOptions.handles.enabled&&(this.drawHandle(z,0,q,l),this.drawHandle(e,1,q,l)));this.scrollbar&&(q?(q=this.top-t,c=this.left-t+(y||!c.opposite?0:(c.titleOffset||0)+c.axisTitleMargin),t=r+2*t):(q=this.top+(y?this.height:-t),c=this.left-
t),this.scrollbar.position(c,q,u,t),this.scrollbar.setRange(this.zoomedMin/(r||1),this.zoomedMax/(r||1)));this.rendered=!0}};b.prototype.addMouseEvents=function(){var b=this,u=b.chart,z=u.container,a=[],t,r;b.mouseMoveHandler=t=function(g){b.onMouseMove(g)};b.mouseUpHandler=r=function(g){b.onMouseUp(g)};a=b.getPartsEvents("mousedown");a.push(c(u.renderTo,"mousemove",t),c(z.ownerDocument,"mouseup",r));h&&(a.push(c(u.renderTo,"touchmove",t),c(z.ownerDocument,"touchend",r)),a.concat(b.getPartsEvents("touchstart")));
b.eventsToUnbind=a;b.series&&b.series[0]&&a.push(c(b.series[0].xAxis,"foundExtremes",function(){u.navigator.modifyNavigatorAxisExtremes()}))};b.prototype.getPartsEvents=function(b){var g=this,z=[];["shades","handles"].forEach(function(u){g[u].forEach(function(a,t){z.push(c(a.element,b,function(b){g[u+"Mousedown"](b,t)}))})});return z};b.prototype.shadesMousedown=function(b,u){b=this.chart.pointer.normalize(b);var g=this.chart,a=this.xAxis,t=this.zoomedMin,r=this.left,f=this.size,c=this.range,y=b.chartX;
g.inverted&&(y=b.chartY,r=this.top);if(1===u)this.grabbedCenter=y,this.fixedWidth=c,this.dragOffset=y-t;else{b=y-r-c/2;if(0===u)b=Math.max(0,b);else if(2===u&&b+c>=f)if(b=f-c,this.reversedExtremes){b-=c;var k=this.getUnionExtremes().dataMin}else var e=this.getUnionExtremes().dataMax;b!==t&&(this.fixedWidth=c,u=a.navigatorAxis.toFixedRange(b,b+c,k,e),m(u.min)&&g.xAxis[0].setExtremes(Math.min(u.min,u.max),Math.max(u.min,u.max),!0,null,{trigger:"navigator"}))}};b.prototype.handlesMousedown=function(b,
u){this.chart.pointer.normalize(b);b=this.chart;var g=b.xAxis[0],a=this.reversedExtremes;0===u?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=a?g.min:g.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=a?g.max:g.min);b.fixedRange=null};b.prototype.onMouseMove=function(b){var g=this,a=g.chart,t=g.left,r=g.navigatorSize,f=g.range,c=g.dragOffset,y=a.inverted;b.touches&&0===b.touches[0].pageX||(b=a.pointer.normalize(b),a=b.chartX,y&&(t=g.top,a=
b.chartY),g.grabbedLeft?(g.hasDragged=!0,g.render(0,0,a-t,g.otherHandlePos)):g.grabbedRight?(g.hasDragged=!0,g.render(0,0,g.otherHandlePos,a-t)):g.grabbedCenter&&(g.hasDragged=!0,a<c?a=c:a>r+c-f&&(a=r+c-f),g.render(0,0,a-c,a-c+f)),g.hasDragged&&g.scrollbar&&E(g.scrollbar.options.liveRedraw,n.svg&&!l&&!this.chart.isBoosting)&&(b.DOMType=b.type,setTimeout(function(){g.onMouseUp(b)},0)))};b.prototype.onMouseUp=function(b){var g=this.chart,a=this.xAxis,t=this.scrollbar,r=b.DOMEvent||b,f=g.inverted,c=
this.rendered&&!this.hasDragged?"animate":"attr";if(this.hasDragged&&(!t||!t.hasDragged)||"scrollbar"===b.trigger){t=this.getUnionExtremes();if(this.zoomedMin===this.otherHandlePos)var y=this.fixedExtreme;else if(this.zoomedMax===this.otherHandlePos)var k=this.fixedExtreme;this.zoomedMax===this.size&&(k=this.reversedExtremes?t.dataMin:t.dataMax);0===this.zoomedMin&&(y=this.reversedExtremes?t.dataMax:t.dataMin);a=a.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,y,k);m(a.min)&&g.xAxis[0].setExtremes(Math.min(a.min,
a.max),Math.max(a.min,a.max),!0,this.hasDragged?!1:null,{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:r})}"mousemove"!==b.DOMType&&"touchmove"!==b.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null);this.navigatorEnabled&&J(this.zoomedMin)&&J(this.zoomedMax)&&(g=Math.round(this.zoomedMin),b=Math.round(this.zoomedMax),this.shades&&this.drawMasks(g,b,f,c),this.outline&&this.drawOutline(g,
b,f,c),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(g,0,f,c),this.drawHandle(b,1,f,c)))};b.prototype.removeEvents=function(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(b){b()}),this.eventsToUnbind=void 0);this.removeBaseSeriesEvents()};b.prototype.removeBaseSeriesEvents=function(){var b=this.baseSeries||[];this.navigatorEnabled&&b[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&b.forEach(function(b){F(b,"updatedData",
this.updatedDataHandler)},this),b[0].xAxis&&F(b[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))};b.prototype.init=function(b){var g=b.options,a=g.navigator,t=a.enabled,f=g.scrollbar,y=f.enabled;g=t?a.height:0;var k=y?f.height:0;this.handles=[];this.shades=[];this.chart=b;this.setBaseSeries();this.height=g;this.scrollbarHeight=k;this.scrollbarEnabled=y;this.navigatorEnabled=t;this.navigatorOptions=a;this.scrollbarOptions=f;this.outlineHeight=g+k;this.opposite=E(a.opposite,!(t||!b.inverted));
var e=this;t=e.baseSeries;f=b.xAxis.length;y=b.yAxis.length;var m=t&&t[0]&&t[0].xAxis||b.xAxis[0]||{options:{}};b.isDirtyBox=!0;e.navigatorEnabled?(e.xAxis=new d(b,L({breaks:m.options.breaks,ordinal:m.options.ordinal},a.xAxis,{id:"navigator-x-axis",yAxis:"navigator-y-axis",isX:!0,type:"datetime",index:f,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:0,maxPadding:0,zoomEnabled:!1},b.inverted?{offsets:[k,0,-k,0],width:g}:{offsets:[0,-k,0,k],height:g})),e.yAxis=new d(b,
L(a.yAxis,{id:"navigator-y-axis",alignTicks:!1,offset:0,index:y,isInternal:!0,reversed:E(a.yAxis&&a.yAxis.reversed,b.yAxis[0]&&b.yAxis[0].reversed,!1),zoomEnabled:!1},b.inverted?{width:g}:{height:g})),t||a.series.data?e.updateNavigatorSeries(!1):0===b.series.length&&(e.unbindRedraw=c(b,"beforeRedraw",function(){0<b.series.length&&!e.series&&(e.setBaseSeries(),e.unbindRedraw())})),e.reversedExtremes=b.inverted&&!e.xAxis.reversed||!b.inverted&&e.xAxis.reversed,e.renderElements(),e.addMouseEvents()):
(e.xAxis={chart:b,navigatorAxis:{fake:!0},translate:function(g,a){var u=b.xAxis[0],t=u.getExtremes(),z=u.len-2*k,f=r("min",u.options.min,t.dataMin);u=r("max",u.options.max,t.dataMax)-f;return a?g*u/z+f:z*(g-f)/u},toPixels:function(b){return this.translate(b)},toValue:function(b){return this.translate(b,!0)}},e.xAxis.navigatorAxis.axis=e.xAxis,e.xAxis.navigatorAxis.toFixedRange=w.AdditionsClass.prototype.toFixedRange.bind(e.xAxis.navigatorAxis));b.options.scrollbar.enabled&&(b.scrollbar=e.scrollbar=
new v(b.renderer,L(b.options.scrollbar,{margin:e.navigatorEnabled?0:10,vertical:b.inverted}),b),c(e.scrollbar,"changed",function(g){var a=e.size,u=a*this.to;a*=this.from;e.hasDragged=e.scrollbar.hasDragged;e.render(0,0,a,u);(b.options.scrollbar.liveRedraw||"mousemove"!==g.DOMType&&"touchmove"!==g.DOMType)&&setTimeout(function(){e.onMouseUp(g)})}));e.addBaseSeriesEvents();e.addChartEvents()};b.prototype.getUnionExtremes=function(b){var g=this.chart.xAxis[0],a=this.xAxis,t=a.options,f=g.options,c;b&&
null===g.dataMin||(c={dataMin:E(t&&t.min,r("min",f.min,g.dataMin,a.dataMin,a.min)),dataMax:E(t&&t.max,r("max",f.max,g.dataMax,a.dataMax,a.max))});return c};b.prototype.setBaseSeries=function(b,a){var g=this.chart,u=this.baseSeries=[];b=b||g.options&&g.options.navigator.baseSeries||(g.series.length?N(g.series,function(b){return!b.options.isInternal}).index:0);(g.series||[]).forEach(function(g,a){g.options.isInternal||!g.options.showInNavigator&&(a!==b&&g.options.id!==b||!1===g.options.showInNavigator)||
u.push(g)});this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,a)};b.prototype.updateNavigatorSeries=function(b,a){var g=this,u=g.chart,t=g.baseSeries,r,f,c=g.navigatorOptions.series,k,m={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:"navigator-x-axis",yAxis:"navigator-y-axis",showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},q=g.series=(g.series||[]).filter(function(b){var a=b.baseSeries;return 0>t.indexOf(a)?(a&&(F(a,
"updatedData",g.updatedDataHandler),delete a.navigatorSeries),b.chart&&b.destroy(),!1):!0});t&&t.length&&t.forEach(function(b){var z=b.navigatorSeries,y=H({color:b.color,visible:b.visible},M(c)?e.navigator.series:c);z&&!1===g.navigatorOptions.adaptToUpdatedData||(m.name="Navigator "+t.length,r=b.options||{},k=r.navigatorOptions||{},f=L(r,m,y,k),f.pointRange=E(y.pointRange,k.pointRange,e.plotOptions[f.type||"line"].pointRange),y=k.data||y.data,g.hasNavigatorData=g.hasNavigatorData||!!y,f.data=y||r.data&&
r.data.slice(0),z&&z.options?z.update(f,a):(b.navigatorSeries=u.initSeries(f),b.navigatorSeries.baseSeries=b,q.push(b.navigatorSeries)))});if(c.data&&(!t||!t.length)||M(c))g.hasNavigatorData=!1,c=y(c),c.forEach(function(b,a){m.name="Navigator "+(q.length+1);f=L(e.navigator.series,{color:u.series[a]&&!u.series[a].options.isInternal&&u.series[a].color||u.options.colors[a]||u.options.colors[0]},m,b);f.data=b.data;f.data&&(g.hasNavigatorData=!0,q.push(u.initSeries(f)))});b&&this.addBaseSeriesEvents()};
b.prototype.addBaseSeriesEvents=function(){var b=this,a=b.baseSeries||[];a[0]&&a[0].xAxis&&c(a[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes);a.forEach(function(g){c(g,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)});c(g,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)});!1!==this.navigatorOptions.adaptToUpdatedData&&g.xAxis&&c(g,"updatedData",this.updatedDataHandler);c(g,"remove",function(){this.navigatorSeries&&(f(b.series,this.navigatorSeries),
m(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)})},this)};b.prototype.getBaseSeriesMin=function(b){return this.baseSeries.reduce(function(b,g){return Math.min(b,g.xData?g.xData[0]:b)},b)};b.prototype.modifyNavigatorAxisExtremes=function(){var b=this.xAxis,a;"undefined"!==typeof b.getExtremes&&(!(a=this.getUnionExtremes(!0))||a.dataMin===b.min&&a.dataMax===b.max||(b.min=a.dataMin,b.max=a.dataMax))};b.prototype.modifyBaseAxisExtremes=function(){var b=this.chart.navigator,
a=this.getExtremes(),t=a.dataMin,r=a.dataMax;a=a.max-a.min;var f=b.stickToMin,c=b.stickToMax,y=E(this.options.overscroll,0),k=b.series&&b.series[0],e=!!this.setExtremes;if(!this.eventArgs||"rangeSelectorButton"!==this.eventArgs.trigger){if(f){var m=t;var q=m+a}c&&(q=r+y,f||(m=Math.max(t,q-a,b.getBaseSeriesMin(k&&k.xData?k.xData[0]:-Number.MAX_VALUE))));e&&(f||c)&&J(m)&&(this.min=this.userMin=m,this.max=this.userMax=q)}b.stickToMin=b.stickToMax=null};b.prototype.updatedDataHandler=function(){var b=
this.chart.navigator,a=this.navigatorSeries,t=b.getBaseSeriesMin(this.xData[0]);b.stickToMax=b.reversedExtremes?0===Math.round(b.zoomedMin):Math.round(b.zoomedMax)>=Math.round(b.size);b.stickToMin=J(this.xAxis.min)&&this.xAxis.min<=t&&(!this.chart.fixedRange||!b.stickToMax);a&&!b.hasNavigatorData&&(a.options.pointStart=this.xData[0],a.setData(this.options.data,!1,null,!1))};b.prototype.addChartEvents=function(){this.eventsToUnbind||(this.eventsToUnbind=[]);this.eventsToUnbind.push(c(this.chart,"redraw",
function(){var b=this.navigator,a=b&&(b.baseSeries&&b.baseSeries[0]&&b.baseSeries[0].xAxis||this.xAxis[0]);a&&b.render(a.min,a.max)}),c(this.chart,"getMargins",function(){var b=this.navigator,a=b.opposite?"plotTop":"marginBottom";this.inverted&&(a=b.opposite?"marginRight":"plotLeft");this[a]=(this[a]||0)+(b.navigatorEnabled||!this.inverted?b.outlineHeight:0)+b.navigatorOptions.margin}))};b.prototype.destroy=function(){this.removeEvents();this.xAxis&&(f(this.chart.xAxis,this.xAxis),f(this.chart.axes,
this.xAxis));this.yAxis&&(f(this.chart.yAxis,this.yAxis),f(this.chart.axes,this.yAxis));(this.series||[]).forEach(function(b){b.destroy&&b.destroy()});"series xAxis yAxis shades outline scrollbarTrack scrollbarRifles scrollbarGroup scrollbar navigatorGroup rendered".split(" ").forEach(function(b){this[b]&&this[b].destroy&&this[b].destroy();this[b]=null},this);[this.handles].forEach(function(b){q(b)},this)};return b}();n.Navigator||(n.Navigator=b,w.compose(d),c(B,"beforeShowResetZoom",function(){var b=
this.options,a=b.navigator,u=b.rangeSelector;if((a&&a.enabled||u&&u.enabled)&&(!l&&"x"===b.chart.zoomType||l&&"x"===b.chart.pinchType))return!1}),c(B,"beforeRender",function(){var a=this.options;if(a.navigator.enabled||a.scrollbar.enabled)this.scroller=this.navigator=new b(this)}),c(B,"afterSetChartSize",function(){var b=this.legend,a=this.navigator;if(a){var u=b&&b.options;var r=a.xAxis;var f=a.yAxis;var c=a.scrollbarHeight;this.inverted?(a.left=a.opposite?this.chartWidth-c-a.height:this.spacing[3]+
c,a.top=this.plotTop+c):(a.left=this.plotLeft+c,a.top=a.navigatorOptions.top||this.chartHeight-a.height-c-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(u&&"bottom"===u.verticalAlign&&"proximate"!==u.layout&&u.enabled&&!u.floating?b.legendHeight+E(u.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0));r&&f&&(this.inverted?r.options.left=f.options.left=a.left:r.options.top=f.options.top=a.top,r.setAxisSize(),f.setAxisSize())}}),c(B,"update",function(b){var a=
b.options.navigator||{},t=b.options.scrollbar||{};this.navigator||this.scroller||!a.enabled&&!t.enabled||(L(!0,this.options.navigator,a),L(!0,this.options.scrollbar,t),delete b.options.navigator,delete b.options.scrollbar)}),c(B,"afterUpdate",function(a){this.navigator||this.scroller||!this.options.navigator.enabled&&!this.options.scrollbar.enabled||(this.scroller=this.navigator=new b(this),E(a.redraw,!0)&&this.redraw(a.animation))}),c(B,"afterAddSeries",function(){this.navigator&&this.navigator.setBaseSeries(null,
!1)}),c(C,"afterUpdate",function(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}),B.prototype.callbacks.push(function(b){var a=b.navigator;a&&b.xAxis[0]&&(b=b.xAxis[0].getExtremes(),a.render(b.min,b.max))}));n.Navigator=b;return n.Navigator});K(d,"Core/Axis/OrdinalAxis.js",[d["Core/Axis/Axis.js"],d["Core/Globals.js"],d["Core/Series/Series.js"],d["Core/Utilities.js"],d["Core/Chart/Chart.js"]],function(d,B,x,n,w){var D=n.addEvent,A=n.css,v=n.defined,C=
n.error,a=n.pick,p=n.timeUnits,h;(function(l){var e=function(){function c(a){this.index={};this.axis=a}c.prototype.beforeSetTickPositions=function(){var c=this.axis,e=c.ordinal,m=[],q,f=!1,l=c.getExtremes(),h=l.min,p=l.max,d,v=c.isXAxis&&!!c.options.breaks;l=c.options.ordinal;var E=Number.MAX_VALUE,F=c.chart.options.chart.ignoreHiddenSeries,y;if(l||v){c.series.forEach(function(b,g){q=[];if(!(F&&!1===b.visible||!1===b.takeOrdinalPosition&&!v)&&(m=m.concat(b.processedXData),r=m.length,m.sort(function(b,
a){return b-a}),E=Math.min(E,a(b.closestPointRange,E)),r)){for(g=0;g<r-1;)m[g]!==m[g+1]&&q.push(m[g+1]),g++;q[0]!==m[0]&&q.unshift(m[0]);m=q}b.isSeriesBoosting&&(y=!0)});y&&(m.length=0);var r=m.length;if(2<r){var b=m[1]-m[0];for(d=r-1;d--&&!f;)m[d+1]-m[d]!==b&&(f=!0);!c.options.keepOrdinalPadding&&(m[0]-h>b||p-m[m.length-1]>b)&&(f=!0)}else c.options.overscroll&&(2===r?E=m[1]-m[0]:1===r?(E=c.options.overscroll,m=[m[0],m[0]+E]):E=e.overscrollPointsRange);f?(c.options.overscroll&&(e.overscrollPointsRange=
E,m=m.concat(e.getOverscrollPositions())),e.positions=m,b=c.ordinal2lin(Math.max(h,m[0]),!0),d=Math.max(c.ordinal2lin(Math.min(p,m[m.length-1]),!0),1),e.slope=p=(p-h)/(d-b),e.offset=h-b*p):(e.overscrollPointsRange=a(c.closestPointRange,e.overscrollPointsRange),e.positions=c.ordinal.slope=e.offset=void 0)}c.isOrdinal=l&&f;e.groupIntervalFactor=null};c.prototype.getExtendedPositions=function(){var a=this,c=a.axis,e=c.constructor.prototype,q=c.chart,f=c.series[0].currentDataGrouping,l=a.index,h=f?f.count+
f.unitName:"raw",p=c.options.overscroll,d=c.getExtremes(),v;l||(l=a.index={});if(!l[h]){var E={series:[],chart:q,getExtremes:function(){return{min:d.dataMin,max:d.dataMax+p}},options:{ordinal:!0},ordinal:{},ordinal2lin:e.ordinal2lin,val2lin:e.val2lin};E.ordinal.axis=E;c.series.forEach(function(c){v={xAxis:E,xData:c.xData.slice(),chart:q,destroyGroupedData:B.noop,getProcessedData:x.prototype.getProcessedData};v.xData=v.xData.concat(a.getOverscrollPositions());v.options={dataGrouping:f?{enabled:!0,
forced:!0,approximation:"open",units:[[f.unitName,[f.count]]]}:{enabled:!1}};c.processData.apply(v);E.series.push(v)});c.ordinal.beforeSetTickPositions.apply({axis:E});l[h]=E.ordinal.positions}return l[h]};c.prototype.getGroupIntervalFactor=function(a,c,e){e=e.processedXData;var k=e.length,f=[];var m=this.groupIntervalFactor;if(!m){for(m=0;m<k-1;m++)f[m]=e[m+1]-e[m];f.sort(function(a,c){return a-c});f=f[Math.floor(k/2)];a=Math.max(a,e[0]);c=Math.min(c,e[k-1]);this.groupIntervalFactor=m=k*f/(c-a)}return m};
c.prototype.getOverscrollPositions=function(){var a=this.axis,c=a.options.overscroll,e=this.overscrollPointsRange,l=[],f=a.dataMax;if(v(e))for(l.push(f);f<=a.dataMax+c;)f+=e,l.push(f);return l};c.prototype.postProcessTickInterval=function(a){var c=this.axis,e=this.slope;return e?c.options.breaks?c.closestPointRange||a:a/(e/c.closestPointRange):a};return c}();l.Composition=e;l.compose=function(a,e,h){a.keepProps.push("ordinal");var c=a.prototype;a.prototype.getTimeTicks=function(a,c,e,k,m,l,h){void 0===
m&&(m=[]);void 0===l&&(l=0);var f=0,q,y,r={},b=[],t=-Number.MAX_VALUE,g=this.options.tickPixelInterval,u=this.chart.time,z=[];if(!this.options.ordinal&&!this.options.breaks||!m||3>m.length||"undefined"===typeof c)return u.getTimeTicks.apply(u,arguments);var I=m.length;for(q=0;q<I;q++){var d=q&&m[q-1]>e;m[q]<c&&(f=q);if(q===I-1||m[q+1]-m[q]>5*l||d){if(m[q]>t){for(y=u.getTimeTicks(a,m[f],m[q],k);y.length&&y[0]<=t;)y.shift();y.length&&(t=y[y.length-1]);z.push(b.length);b=b.concat(y)}f=q+1}if(d)break}if(y){y=
y.info;if(h&&y.unitRange<=p.hour){q=b.length-1;for(f=1;f<q;f++)if(u.dateFormat("%d",b[f])!==u.dateFormat("%d",b[f-1])){r[b[f]]="day";var H=!0}H&&(r[b[0]]="day");y.higherRanks=r}y.segmentStarts=z;b.info=y}else C(12,!1,this.chart);if(h&&v(g)){f=z=b.length;H=[];var O;for(u=[];f--;)q=this.translate(b[f]),O&&(u[f]=O-q),H[f]=O=q;u.sort();u=u[Math.floor(u.length/2)];u<.6*g&&(u=null);f=b[z-1]>e?z-1:z;for(O=void 0;f--;)q=H[f],z=Math.abs(O-q),O&&z<.8*g&&(null===u||z<.8*u)?(r[b[f]]&&!r[b[f+1]]?(z=f+1,O=q):z=
f,b.splice(z,1)):O=q}return b};c.lin2val=function(a,c){var f=this.ordinal,e=f.positions;if(e){var m=f.slope,k=f.offset;f=e.length-1;if(c)if(0>a)a=e[0];else if(a>f)a=e[f];else{f=Math.floor(a);var l=a-f}else for(;f--;)if(c=m*f+k,a>=c){m=m*(f+1)+k;l=(a-c)/(m-c);break}return"undefined"!==typeof l&&"undefined"!==typeof e[f]?e[f]+(l?l*(e[f+1]-e[f]):0):a}return a};c.val2lin=function(a,c){var f=this.ordinal,e=f.positions;if(e){var m=e.length,k;for(k=m;k--;)if(e[k]===a){var l=k;break}for(k=m-1;k--;)if(a>e[k]||
0===k){a=(a-e[k])/(e[k+1]-e[k]);l=k+a;break}c=c?l:f.slope*(l||0)+f.offset}else c=a;return c};c.ordinal2lin=c.val2lin;D(a,"afterInit",function(){this.ordinal||(this.ordinal=new l.Composition(this))});D(a,"foundExtremes",function(){this.isXAxis&&v(this.options.overscroll)&&this.max===this.dataMax&&(!this.chart.mouseIsDown||this.isInternal)&&(!this.eventArgs||this.eventArgs&&"navigator"!==this.eventArgs.trigger)&&(this.max+=this.options.overscroll,!this.isInternal&&v(this.userMin)&&(this.min+=this.options.overscroll))});
D(a,"afterSetScale",function(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)});D(a,"initialAxisTranslation",function(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))});D(e,"pan",function(a){var c=this.xAxis[0],e=c.options.overscroll,k=a.originalEvent.chartX,m=this.options.chart&&this.options.chart.panning,l=!1;if(m&&"y"!==m.type&&c.options.ordinal&&
c.series.length){var q=this.mouseDownX,h=c.getExtremes(),d=h.dataMax,y=h.min,r=h.max,b=this.hoverPoints,t=c.closestPointRange||c.ordinal&&c.ordinal.overscrollPointsRange;q=(q-k)/(c.translationSlope*(c.ordinal.slope||t));var g={ordinal:{positions:c.ordinal.getExtendedPositions()}};t=c.lin2val;var u=c.val2lin;if(!g.ordinal.positions)l=!0;else if(1<Math.abs(q)){b&&b.forEach(function(b){b.setState()});if(0>q){b=g;var z=c.ordinal.positions?c:g}else b=c.ordinal.positions?c:g,z=g;g=z.ordinal.positions;d>
g[g.length-1]&&g.push(d);this.fixedRange=r-y;q=c.navigatorAxis.toFixedRange(null,null,t.apply(b,[u.apply(b,[y,!0])+q,!0]),t.apply(z,[u.apply(z,[r,!0])+q,!0]));q.min>=Math.min(h.dataMin,y)&&q.max<=Math.max(d,r)+e&&c.setExtremes(q.min,q.max,!0,!1,{trigger:"pan"});this.mouseDownX=k;A(this.container,{cursor:"move"})}}else l=!0;l||m&&/y/.test(m.type)?e&&(c.max=c.dataMax+e):a.preventDefault()});D(h,"updatedData",function(){var a=this.xAxis;a&&a.options.ordinal&&delete a.ordinal.index})}})(h||(h={}));h.compose(d,
w,x);return h});K(d,"Core/Axis/BrokenAxis.js",[d["Core/Axis/Axis.js"],d["Core/Series/Series.js"],d["Extensions/Stacking.js"],d["Core/Utilities.js"]],function(d,B,x,n){var w=n.addEvent,D=n.find,A=n.fireEvent,v=n.isArray,C=n.isNumber,a=n.pick,p=function(){function h(a){this.hasBreaks=!1;this.axis=a}h.isInBreak=function(a,e){var c=a.repeat||Infinity,k=a.from,l=a.to-a.from;e=e>=k?(e-k)%c:c-(k-e)%c;return a.inclusive?e<=l:e<l&&0!==e};h.lin2Val=function(a){var e=this.brokenAxis;e=e&&e.breakArray;if(!e)return a;
var c;for(c=0;c<e.length;c++){var k=e[c];if(k.from>=a)break;else k.to<a?a+=k.len:h.isInBreak(k,a)&&(a+=k.len)}return a};h.val2Lin=function(a){var e=this.brokenAxis;e=e&&e.breakArray;if(!e)return a;var c=a,k;for(k=0;k<e.length;k++){var l=e[k];if(l.to<=a)c-=l.len;else if(l.from>=a)break;else if(h.isInBreak(l,a)){c-=a-l.from;break}}return c};h.prototype.findBreakAt=function(a,e){return D(e,function(c){return c.from<a&&a<c.to})};h.prototype.isInAnyBreak=function(l,e){var c=this.axis,k=c.options.breaks,
d=k&&k.length,m;if(d){for(;d--;)if(h.isInBreak(k[d],l)){var q=!0;m||(m=a(k[d].showPoints,!c.isXAxis))}var f=q&&e?q&&!m:q}return f};h.prototype.setBreaks=function(l,e){var c=this,k=c.axis,p=v(l)&&!!l.length;k.isDirty=c.hasBreaks!==p;c.hasBreaks=p;k.options.breaks=k.userOptions.breaks=l;k.forceRedraw=!0;k.series.forEach(function(a){a.isDirty=!0});p||k.val2lin!==h.val2Lin||(delete k.val2lin,delete k.lin2val);p&&(k.userOptions.ordinal=!1,k.lin2val=h.lin2Val,k.val2lin=h.val2Lin,k.setExtremes=function(a,
e,f,k,l){if(c.hasBreaks){for(var m,h=this.options.breaks;m=c.findBreakAt(a,h);)a=m.to;for(;m=c.findBreakAt(e,h);)e=m.from;e<a&&(e=a)}d.prototype.setExtremes.call(this,a,e,f,k,l)},k.setAxisTranslation=function(){d.prototype.setAxisTranslation.call(this);c.unitLength=null;if(c.hasBreaks){var e=k.options.breaks||[],l=[],f=[],p=0,v,w=k.userMin||k.min,C=k.userMax||k.max,n=a(k.pointRangePadding,0),x;e.forEach(function(a){v=a.repeat||Infinity;h.isInBreak(a,w)&&(w+=a.to%v-w%v);h.isInBreak(a,C)&&(C-=C%v-a.from%
v)});e.forEach(function(a){y=a.from;for(v=a.repeat||Infinity;y-v>w;)y-=v;for(;y<w;)y+=v;for(x=y;x<C;x+=v)l.push({value:x,move:"in"}),l.push({value:x+(a.to-a.from),move:"out",size:a.breakSize})});l.sort(function(a,b){return a.value===b.value?("in"===a.move?0:1)-("in"===b.move?0:1):a.value-b.value});var F=0;var y=w;l.forEach(function(a){F+="in"===a.move?1:-1;1===F&&"in"===a.move&&(y=a.value);0===F&&(f.push({from:y,to:a.value,len:a.value-y-(a.size||0)}),p+=a.value-y-(a.size||0))});k.breakArray=c.breakArray=
f;c.unitLength=C-w-p+n;A(k,"afterBreaks");k.staticScale?k.transA=k.staticScale:c.unitLength&&(k.transA*=(C-k.min+n)/c.unitLength);n&&(k.minPixelPadding=k.transA*k.minPointOffset);k.min=w;k.max=C}});a(e,!0)&&k.chart.redraw()};return h}();n=function(){function h(){}h.compose=function(l,e){l.keepProps.push("brokenAxis");var c=B.prototype;c.drawBreaks=function(c,e){var k=this,l=k.points,f,h,d,p;if(c&&c.brokenAxis&&c.brokenAxis.hasBreaks){var v=c.brokenAxis;e.forEach(function(e){f=v&&v.breakArray||[];
h=c.isXAxis?c.min:a(k.options.threshold,c.min);l.forEach(function(k){p=a(k["stack"+e.toUpperCase()],k[e]);f.forEach(function(a){if(C(h)&&C(p)){d=!1;if(h<a.from&&p>a.to||h>a.from&&p<a.from)d="pointBreak";else if(h<a.from&&p>a.from&&p<a.to||h>a.from&&p>a.to&&p<a.from)d="pointInBreak";d&&A(c,d,{point:k,brk:a})}})})})}};c.gappedPath=function(){var a=this.currentDataGrouping,c=a&&a.gapSize;a=this.options.gapSize;var e=this.points.slice(),l=e.length-1,f=this.yAxis,h;if(a&&0<l)for("value"!==this.options.gapUnit&&
(a*=this.basePointRange),c&&c>a&&c>=this.basePointRange&&(a=c),h=void 0;l--;)h&&!1!==h.visible||(h=e[l+1]),c=e[l],!1!==h.visible&&!1!==c.visible&&(h.x-c.x>a&&(h=(c.x+h.x)/2,e.splice(l+1,0,{isNull:!0,x:h}),f.stacking&&this.options.stacking&&(h=f.stacking.stacks[this.stackKey][h]=new x(f,f.options.stackLabels,!1,h,this.stack),h.total=0)),h=c);return this.getGraphPath(e)};w(l,"init",function(){this.brokenAxis||(this.brokenAxis=new p(this))});w(l,"afterInit",function(){"undefined"!==typeof this.brokenAxis&&
this.brokenAxis.setBreaks(this.options.breaks,!1)});w(l,"afterSetTickPositions",function(){var a=this.brokenAxis;if(a&&a.hasBreaks){var c=this.tickPositions,e=this.tickPositions.info,l=[],f;for(f=0;f<c.length;f++)a.isInAnyBreak(c[f])||l.push(c[f]);this.tickPositions=l;this.tickPositions.info=e}});w(l,"afterSetOptions",function(){this.brokenAxis&&this.brokenAxis.hasBreaks&&(this.options.ordinal=!1)});w(e,"afterGeneratePoints",function(){var a=this.options.connectNulls,c=this.points,e=this.xAxis,l=
this.yAxis;if(this.isDirty)for(var f=c.length;f--;){var h=c[f],d=!(null===h.y&&!1===a)&&(e&&e.brokenAxis&&e.brokenAxis.isInAnyBreak(h.x,!0)||l&&l.brokenAxis&&l.brokenAxis.isInAnyBreak(h.y,!0));h.visible=d?!1:!1!==h.options.visible}});w(e,"afterRender",function(){this.drawBreaks(this.xAxis,["x"]);this.drawBreaks(this.yAxis,a(this.pointArrayMap,["y"]))})};return h}();n.compose(d,B);return n});K(d,"masters/modules/broken-axis.src.js",[],function(){});K(d,"Extensions/DataGrouping.js",[d["Core/Axis/Axis.js"],
d["Core/Axis/DateTimeAxis.js"],d["Core/Globals.js"],d["Core/Options.js"],d["Core/Series/Point.js"],d["Core/Series/Series.js"],d["Core/Tooltip.js"],d["Core/Utilities.js"]],function(d,B,x,n,w,D,A,v){var C=D.prototype,a=v.addEvent,p=v.arrayMax,h=v.arrayMin,l=v.correctFloat,e=v.defined,c=v.error,k=v.extend,G=v.format,m=v.isNumber,q=v.merge,f=v.pick;"";var H=x.approximations={sum:function(a){var c=a.length;if(!c&&a.hasNulls)var b=null;else if(c)for(b=0;c--;)b+=a[c];return b},average:function(a){var c=
a.length;a=H.sum(a);m(a)&&c&&(a=l(a/c));return a},averages:function(){var a=[];[].forEach.call(arguments,function(c){a.push(H.average(c))});return"undefined"===typeof a[0]?void 0:a},open:function(a){return a.length?a[0]:a.hasNulls?null:void 0},high:function(a){return a.length?p(a):a.hasNulls?null:void 0},low:function(a){return a.length?h(a):a.hasNulls?null:void 0},close:function(a){return a.length?a[a.length-1]:a.hasNulls?null:void 0},ohlc:function(a,c,b,t){a=H.open(a);c=H.high(c);b=H.low(b);t=H.close(t);
if(m(a)||m(c)||m(b)||m(t))return[a,c,b,t]},range:function(a,c){a=H.low(a);c=H.high(c);if(m(a)||m(c))return[a,c];if(null===a&&null===c)return null}};v=function(a,c,b,t){var g=this,u=g.data,f=g.options&&g.options.data,r=[],l=[],k=[],h=a.length,y=!!c,d=[],p=g.pointArrayMap,v=p&&p.length,F=["x"].concat(p||["y"]),w=0,A=0,C;t="function"===typeof t?t:H[t]?H[t]:H[g.getDGApproximation&&g.getDGApproximation()||"average"];v?p.forEach(function(){d.push([])}):d.push([]);var x=v||1;for(C=0;C<=h&&!(a[C]>=b[0]);C++);
for(C;C<=h;C++){for(;"undefined"!==typeof b[w+1]&&a[C]>=b[w+1]||C===h;){var n=b[w];g.dataGroupInfo={start:g.cropStart+A,length:d[0].length};var D=t.apply(g,d);g.pointClass&&!e(g.dataGroupInfo.options)&&(g.dataGroupInfo.options=q(g.pointClass.prototype.optionsToObject.call({series:g},g.options.data[g.cropStart+A])),F.forEach(function(b){delete g.dataGroupInfo.options[b]}));"undefined"!==typeof D&&(r.push(n),l.push(D),k.push(g.dataGroupInfo));A=C;for(n=0;n<x;n++)d[n].length=0,d[n].hasNulls=!1;w+=1;
if(C===h)break}if(C===h)break;if(p)for(n=g.cropStart+C,D=u&&u[n]||g.pointClass.prototype.applyOptions.apply({series:g},[f[n]]),n=0;n<v;n++){var B=D[p[n]];m(B)?d[n].push(B):null===B&&(d[n].hasNulls=!0)}else n=y?c[C]:null,m(n)?d[0].push(n):null===n&&(d[0].hasNulls=!0)}return{groupedXData:r,groupedYData:l,groupMap:k}};var N={approximations:H,groupData:v},M=C.processData,J=C.generatePoints,L={groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%A, %b %e, %H:%M:%S.%L","%A, %b %e, %H:%M:%S.%L","-%H:%M:%S.%L"],
second:["%A, %b %e, %H:%M:%S","%A, %b %e, %H:%M:%S","-%H:%M:%S"],minute:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],hour:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],day:["%A, %b %e, %Y","%A, %b %e","-%A, %b %e, %Y"],week:["Week from %A, %b %e, %Y","%A, %b %e","-%A, %b %e, %Y"],month:["%B %Y","%B","-%B %Y"],year:["%Y","%Y","-%Y"]}},E={line:{},spline:{},area:{},areaspline:{},arearange:{},column:{groupPixelWidth:10},columnrange:{groupPixelWidth:10},candlestick:{groupPixelWidth:10},ohlc:{groupPixelWidth:5}},
F=x.defaultDataGroupingUnits=[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]];C.getDGApproximation=function(){return this.is("arearange")?"range":this.is("ohlc")?"ohlc":this.is("column")?"sum":"average"};C.groupData=v;C.processData=function(){var a=this.chart,c=this.options.dataGrouping,b=!1!==this.allowDG&&c&&f(c.enabled,a.options.isStock),t=this.visible||!a.options.chart.ignoreHiddenSeries,
g,u=this.currentDataGrouping,z=!1;this.forceCrop=b;this.groupPixelWidth=null;this.hasProcessed=!0;b&&!this.requireSorting&&(this.requireSorting=z=!0);b=!1===M.apply(this,arguments)||!b;z&&(this.requireSorting=!1);if(!b){this.destroyGroupedData();b=c.groupAll?this.xData:this.processedXData;var l=c.groupAll?this.yData:this.processedYData,h=a.plotSizeX;a=this.xAxis;var k=a.options.ordinal,m=this.groupPixelWidth=a.getGroupPixelWidth&&a.getGroupPixelWidth();if(m){this.isDirty=g=!0;this.points=null;z=a.getExtremes();
var d=z.min;z=z.max;k=k&&a.ordinal&&a.ordinal.getGroupIntervalFactor(d,z,this)||1;m=m*(z-d)/h*k;h=a.getTimeTicks(B.AdditionsClass.prototype.normalizeTimeTickInterval(m,c.units||F),Math.min(d,b[0]),Math.max(z,b[b.length-1]),a.options.startOfWeek,b,this.closestPointRange);l=C.groupData.apply(this,[b,l,h,c.approximation]);b=l.groupedXData;k=l.groupedYData;var p=0;if(c.smoothed&&b.length){var q=b.length-1;for(b[q]=Math.min(b[q],z);q--&&0<q;)b[q]+=m/2;b[0]=Math.max(b[0],d)}for(q=1;q<h.length;q++)h.info.segmentStarts&&
-1!==h.info.segmentStarts.indexOf(q)||(p=Math.max(h[q]-h[q-1],p));d=h.info;d.gapSize=p;this.closestPointRange=h.info.totalRange;this.groupMap=l.groupMap;if(e(b[0])&&b[0]<a.min&&t){if(!e(a.options.min)&&a.min<=a.dataMin||a.min===a.dataMin)a.min=Math.min(b[0],a.min);a.dataMin=Math.min(b[0],a.dataMin)}c.groupAll&&(c=this.cropData(b,k,a.min,a.max,1),b=c.xData,k=c.yData);this.processedXData=b;this.processedYData=k}else this.groupMap=null;this.hasGroupedData=g;this.currentDataGrouping=d;this.preventGraphAnimation=
(u&&u.totalRange)!==(d&&d.totalRange)}};C.destroyGroupedData=function(){this.groupedData&&(this.groupedData.forEach(function(a,c){a&&(this.groupedData[c]=a.destroy?a.destroy():null)},this),this.groupedData.length=0)};C.generatePoints=function(){J.apply(this);this.destroyGroupedData();this.groupedData=this.hasGroupedData?this.points:null};a(w,"update",function(){if(this.dataGroup)return c(24,!1,this.series.chart),!1});a(A,"headerFormatter",function(a){var c=this.chart,b=c.time,t=a.labelConfig,g=t.series,
u=g.tooltipOptions,e=g.options.dataGrouping,f=u.xDateFormat,h=g.xAxis,l=u[(a.isFooter?"footer":"header")+"Format"];if(h&&"datetime"===h.options.type&&e&&m(t.key)){var d=g.currentDataGrouping;e=e.dateTimeLabelFormats||L.dateTimeLabelFormats;if(d)if(u=e[d.unitName],1===d.count)f=u[0];else{f=u[1];var p=u[2]}else!f&&e&&(f=this.getXDateFormat(t,u,h));f=b.dateFormat(f,t.key);p&&(f+=b.dateFormat(p,t.key+d.totalRange-1));g.chart.styledMode&&(l=this.styledModeFormat(l));a.text=G(l,{point:k(t.point,{key:f}),
series:g},c);a.preventDefault()}});a(D,"destroy",C.destroyGroupedData);a(D,"afterSetOptions",function(a){a=a.options;var c=this.type,b=this.chart.options.plotOptions,t=n.defaultOptions.plotOptions[c].dataGrouping,g=this.useCommonDataGrouping&&L;if(E[c]||g)t||(t=q(L,E[c])),a.dataGrouping=q(g,t,b.series&&b.series.dataGrouping,b[c].dataGrouping,this.userOptions.dataGrouping)});a(d,"afterSetScale",function(){this.series.forEach(function(a){a.hasProcessed=!1})});d.prototype.getGroupPixelWidth=function(){var a=
this.series,c=a.length,b,t=0,g=!1,u;for(b=c;b--;)(u=a[b].options.dataGrouping)&&(t=Math.max(t,f(u.groupPixelWidth,L.groupPixelWidth)));for(b=c;b--;)(u=a[b].options.dataGrouping)&&a[b].hasProcessed&&(c=(a[b].processedXData||a[b].data).length,a[b].groupPixelWidth||c>this.chart.plotSizeX/t||c&&u.forced)&&(g=!0);return g?t:0};d.prototype.setDataGrouping=function(a,c){var b;c=f(c,!0);a||(a={forced:!1,units:null});if(this instanceof d)for(b=this.series.length;b--;)this.series[b].update({dataGrouping:a},
!1);else this.chart.options.series.forEach(function(b){b.dataGrouping=a},!1);this.ordinal&&(this.ordinal.slope=void 0);c&&this.chart.redraw()};x.dataGrouping=N;"";return N});K(d,"Series/OHLC/OHLCPoint.js",[d["Core/Series/SeriesRegistry.js"]],function(d){var B=this&&this.__extends||function(){var d=function(n,w){d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,n){d.__proto__=n}||function(d,n){for(var v in n)n.hasOwnProperty(v)&&(d[v]=n[v])};return d(n,w)};return function(n,w){function x(){this.constructor=
n}d(n,w);n.prototype=null===w?Object.create(w):(x.prototype=w.prototype,new x)}}();return function(d){function n(){var n=null!==d&&d.apply(this,arguments)||this;n.close=void 0;n.high=void 0;n.low=void 0;n.open=void 0;n.options=void 0;n.plotClose=void 0;n.plotOpen=void 0;n.series=void 0;return n}B(n,d);n.prototype.getClassName=function(){return d.prototype.getClassName.call(this)+(this.open<this.close?" highcharts-point-up":" highcharts-point-down")};n.prototype.resolveUpColor=function(){this.open<
this.close&&!this.options.color&&this.series.options.upColor&&(this.color=this.series.options.upColor)};n.prototype.resolveColor=function(){d.prototype.resolveColor.call(this);this.resolveUpColor()};n.prototype.getZone=function(){var n=d.prototype.getZone.call(this);this.resolveUpColor();return n};return n}(d.seriesTypes.column.prototype.pointClass)});K(d,"Series/OHLC/OHLCSeries.js",[d["Series/OHLC/OHLCPoint.js"],d["Core/Series/SeriesRegistry.js"],d["Core/Utilities.js"]],function(d,B,x){var n=this&&
this.__extends||function(){var d=function(v,a){d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,h){a.__proto__=h}||function(a,h){for(var l in h)h.hasOwnProperty(l)&&(a[l]=h[l])};return d(v,a)};return function(v,a){function p(){this.constructor=v}d(v,a);v.prototype=null===a?Object.create(a):(p.prototype=a.prototype,new p)}}(),w=B.seriesTypes.column,D=x.extend,A=x.merge;x=function(d){function v(){var a=null!==d&&d.apply(this,arguments)||this;a.data=void 0;a.options=void 0;a.points=
void 0;a.yData=void 0;return a}n(v,d);v.prototype.drawPoints=function(){var a=this,d=a.chart,h=function(a,e,c){var d=a[0];a=a[1];"number"===typeof d[2]&&(d[2]=Math.max(c+e,d[2]));"number"===typeof a[2]&&(a[2]=Math.min(c-e,a[2]))};a.points.forEach(function(l){var e=l.graphic,c=!e;if("undefined"!==typeof l.plotY){e||(l.graphic=e=d.renderer.path().add(a.group));d.styledMode||e.attr(a.pointAttribs(l,l.selected&&"select"));var k=e.strokeWidth();var p=k%2/2;var m=Math.round(l.plotX)-p;var q=Math.round(l.shapeArgs.width/
2);var f=[["M",m,Math.round(l.yBottom)],["L",m,Math.round(l.plotHigh)]];if(null!==l.open){var v=Math.round(l.plotOpen)+p;f.push(["M",m,v],["L",m-q,v]);h(f,k/2,v)}null!==l.close&&(v=Math.round(l.plotClose)+p,f.push(["M",m,v],["L",m+q,v]),h(f,k/2,v));e[c?"attr":"animate"]({d:f}).addClass(l.getClassName(),!0)}})};v.prototype.init=function(){d.prototype.init.apply(this,arguments);this.options.stacking=void 0};v.prototype.pointAttribs=function(a,p){p=d.prototype.pointAttribs.call(this,a,p);var h=this.options;
delete p.fill;!a.options.color&&h.upColor&&a.open<a.close&&(p.stroke=h.upColor);return p};v.prototype.toYData=function(a){return[a.open,a.high,a.low,a.close]};v.prototype.translate=function(){var a=this,p=a.yAxis,h=!!a.modifyValue,l=["plotOpen","plotHigh","plotLow","plotClose","yBottom"];d.prototype.translate.apply(a);a.points.forEach(function(e){[e.open,e.high,e.low,e.close,e.low].forEach(function(c,d){null!==c&&(h&&(c=a.modifyValue(c)),e[l[d]]=p.toPixels(c,!0))});e.tooltipPos[1]=e.plotHigh+p.pos-
a.chart.plotTop})};v.defaultOptions=A(w.defaultOptions,{lineWidth:1,tooltip:{pointFormat:'<span style="color:{point.color}">\u25cf</span> <b> {series.name}</b><br/>Open: {point.open}<br/>High: {point.high}<br/>Low: {point.low}<br/>Close: {point.close}<br/>'},threshold:null,states:{hover:{lineWidth:3}},stickyTracking:!0});return v}(w);D(x.prototype,{animate:null,directTouch:!1,pointArrayMap:["open","high","low","close"],pointAttrToOptions:{stroke:"color","stroke-width":"lineWidth"},pointValKey:"close"});
x.prototype.pointClass=d;B.registerSeriesType("ohlc",x);"";return x});K(d,"Series/Candlestick/CandlestickSeries.js",[d["Core/Options.js"],d["Core/Color/Palette.js"],d["Core/Series/SeriesRegistry.js"],d["Core/Utilities.js"]],function(d,B,x,n){var w=this&&this.__extends||function(){var a=function(d,h){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,e){a.__proto__=e}||function(a,e){for(var c in e)e.hasOwnProperty(c)&&(a[c]=e[c])};return a(d,h)};return function(d,h){function l(){this.constructor=
d}a(d,h);d.prototype=null===h?Object.create(h):(l.prototype=h.prototype,new l)}}(),D=d.defaultOptions;d=x.seriesTypes;var A=d.column,v=d.ohlc,C=n.merge;n=function(a){function d(){var d=null!==a&&a.apply(this,arguments)||this;d.data=void 0;d.options=void 0;d.points=void 0;return d}w(d,a);d.prototype.pointAttribs=function(a,d){var e=A.prototype.pointAttribs.call(this,a,d),c=this.options,k=a.open<a.close,h=c.lineColor||this.color,l=a.color||this.color;e["stroke-width"]=c.lineWidth;e.fill=a.options.color||
(k?c.upColor||l:l);e.stroke=a.options.lineColor||(k?c.upLineColor||h:h);d&&(a=c.states[d],e.fill=a.color||e.fill,e.stroke=a.lineColor||e.stroke,e["stroke-width"]=a.lineWidth||e["stroke-width"]);return e};d.prototype.drawPoints=function(){var a=this,d=a.chart,e=a.yAxis.reversed;a.points.forEach(function(c){var k=c.graphic,l=!k;if("undefined"!==typeof c.plotY){k||(c.graphic=k=d.renderer.path().add(a.group));a.chart.styledMode||k.attr(a.pointAttribs(c,c.selected&&"select")).shadow(a.options.shadow);
var h=k.strokeWidth()%2/2;var q=Math.round(c.plotX)-h;var f=c.plotOpen;var p=c.plotClose;var v=Math.min(f,p);f=Math.max(f,p);var n=Math.round(c.shapeArgs.width/2);p=e?f!==c.yBottom:Math.round(v)!==Math.round(c.plotHigh);var A=e?Math.round(v)!==Math.round(c.plotHigh):f!==c.yBottom;v=Math.round(v)+h;f=Math.round(f)+h;h=[];h.push(["M",q-n,f],["L",q-n,v],["L",q+n,v],["L",q+n,f],["Z"],["M",q,v],["L",q,p?Math.round(e?c.yBottom:c.plotHigh):v],["M",q,f],["L",q,A?Math.round(e?c.plotHigh:c.yBottom):f]);k[l?
"attr":"animate"]({d:h}).addClass(c.getClassName(),!0)}})};d.defaultOptions=C(v.defaultOptions,D.plotOptions,{states:{hover:{lineWidth:2}},tooltip:D.plotOptions.ohlc.tooltip,threshold:null,lineColor:B.neutralColor100,lineWidth:1,upColor:B.backgroundColor,stickyTracking:!0});return d}(v);x.registerSeriesType("candlestick",n);"";return n});K(d,"Series/Flags/FlagsPoint.js",[d["Core/Series/SeriesRegistry.js"],d["Core/Utilities.js"]],function(d,B){var x=this&&this.__extends||function(){var d=function(n,
A){d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,n){d.__proto__=n}||function(d,n){for(var a in n)n.hasOwnProperty(a)&&(d[a]=n[a])};return d(n,A)};return function(n,A){function v(){this.constructor=n}d(n,A);n.prototype=null===A?Object.create(A):(v.prototype=A.prototype,new v)}}(),n=B.isNumber;return function(d){function w(){var n=null!==d&&d.apply(this,arguments)||this;n.options=void 0;n.series=void 0;return n}x(w,d);w.prototype.isValid=function(){return n(this.y)||"undefined"===
typeof this.y};return w}(d.seriesTypes.column.prototype.pointClass)});K(d,"Mixins/OnSeries.js",[d["Series/Column/ColumnSeries.js"],d["Core/Series/Series.js"],d["Core/Utilities.js"]],function(d,B,x){var n=d.prototype,w=B.prototype,D=x.defined,A=x.stableSort;return{getPlotBox:function(){return w.getPlotBox.call(this.options.onSeries&&this.chart.get(this.options.onSeries)||this)},translate:function(){n.translate.apply(this);var d=this,w=d.options,a=d.chart,p=d.points,h=p.length-1,l,e=w.onSeries;e=e&&
a.get(e);w=w.onKey||"y";var c=e&&e.options.step,k=e&&e.points,x=k&&k.length,m=a.inverted,q=d.xAxis,f=d.yAxis,B=0,N;if(e&&e.visible&&x){B=(e.pointXOffset||0)+(e.barW||0)/2;a=e.currentDataGrouping;var M=k[x-1].x+(a?a.totalRange:0);A(p,function(a,c){return a.x-c.x});for(w="plot"+w[0].toUpperCase()+w.substr(1);x--&&p[h];){var J=k[x];a=p[h];a.y=J.y;if(J.x<=a.x&&"undefined"!==typeof J[w]){if(a.x<=M&&(a.plotY=J[w],J.x<a.x&&!c&&(N=k[x+1])&&"undefined"!==typeof N[w])){var L=(a.x-J.x)/(N.x-J.x);a.plotY+=L*
(N[w]-J[w]);a.y+=L*(N.y-J.y)}h--;x++;if(0>h)break}}}p.forEach(function(a,c){a.plotX+=B;if("undefined"===typeof a.plotY||m)0<=a.plotX&&a.plotX<=q.len?m?(a.plotY=q.translate(a.x,0,1,0,1),a.plotX=D(a.y)?f.translate(a.y,0,0,0,1):0):a.plotY=(q.opposite?0:d.yAxis.len)+q.offset:a.shapeArgs={};if((l=p[c-1])&&l.plotX===a.plotX){"undefined"===typeof l.stackIndex&&(l.stackIndex=0);var e=l.stackIndex+1}a.stackIndex=e});this.onSeries=e}}});K(d,"Series/Flags/FlagsSymbols.js",[d["Core/Globals.js"],d["Core/Renderer/SVG/SVGRenderer.js"]],
function(d,B){function x(d){D[d+"pin"]=function(n,w,a,p,h){var l=h&&h.anchorX;h=h&&h.anchorY;"circle"===d&&p>a&&(n-=Math.round((p-a)/2),a=p);var e=D[d](n,w,a,p);if(l&&h){var c=l;"circle"===d?c=n+a/2:(n=e[0],a=e[1],"M"===n[0]&&"L"===a[0]&&(c=(n[1]+a[1])/2));e.push(["M",c,w>h?w:w+p],["L",l,h]);e=e.concat(D.circle(l-1,h-1,2,2))}return e}}var n=d.Renderer,w=d.VMLRenderer,D=B.prototype.symbols;D.flag=function(d,n,w,a,p){var h=p&&p.anchorX||d;p=p&&p.anchorY||n;var l=D.circle(h-1,p-1,2,2);l.push(["M",h,
p],["L",d,n+a],["L",d,n],["L",d+w,n],["L",d+w,n+a],["L",d,n+a],["Z"]);return l};x("circle");x("square");n===w&&["circlepin","flag","squarepin"].forEach(function(d){w.prototype.symbols[d]=D[d]});return D});K(d,"Series/Flags/FlagsSeries.js",[d["Series/Flags/FlagsPoint.js"],d["Core/Globals.js"],d["Mixins/OnSeries.js"],d["Core/Color/Palette.js"],d["Core/Series/SeriesRegistry.js"],d["Core/Renderer/SVG/SVGElement.js"],d["Core/Utilities.js"]],function(d,B,x,n,w,D,A){var v=this&&this.__extends||function(){var a=
function(c,e){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,c){a.__proto__=c}||function(a,c){for(var e in c)c.hasOwnProperty(e)&&(a[e]=c[e])};return a(c,e)};return function(c,e){function d(){this.constructor=c}a(c,e);c.prototype=null===e?Object.create(e):(d.prototype=e.prototype,new d)}}(),C=B.noop,a=w.series,p=w.seriesTypes.column,h=A.addEvent,l=A.defined,e=A.extend,c=A.merge,k=A.objectEach,G=A.wrap;A=function(e){function d(){var a=null!==e&&e.apply(this,arguments)||this;a.data=
void 0;a.options=void 0;a.points=void 0;return a}v(d,e);d.prototype.animate=function(a){a&&this.setClip()};d.prototype.drawPoints=function(){var a=this.points,e=this.chart,d=e.renderer,h=e.inverted,m=this.options,n=m.y,p,q=this.yAxis,y={},r=[];for(p=a.length;p--;){var b=a[p];var t=(h?b.plotY:b.plotX)>this.xAxis.len;var g=b.plotX;var u=b.stackIndex;var z=b.options.shape||m.shape;var I=b.plotY;"undefined"!==typeof I&&(I=b.plotY+n-("undefined"!==typeof u&&u*m.stackDistance));b.anchorX=u?void 0:b.plotX;
var v=u?void 0:b.plotY;var w="flag"!==z;u=b.graphic;"undefined"!==typeof I&&0<=g&&!t?(u||(u=b.graphic=d.label("",null,null,z,null,null,m.useHTML).addClass("highcharts-point").add(this.markerGroup),b.graphic.div&&(b.graphic.div.point=b),u.isNew=!0),u.attr({align:w?"center":"left",width:m.width,height:m.height,"text-align":m.textAlign}),e.styledMode||u.attr(this.pointAttribs(b)).css(c(m.style,b.style)).shadow(m.shadow),0<g&&(g-=u.strokeWidth()%2),z={y:I,anchorY:v},m.allowOverlapX&&(z.x=g,z.anchorX=
b.anchorX),u.attr({text:b.options.title||m.title||"A"})[u.isNew?"attr":"animate"](z),m.allowOverlapX||(y[b.plotX]?y[b.plotX].size=Math.max(y[b.plotX].size,u.width):y[b.plotX]={align:w?.5:0,size:u.width,target:g,anchorX:g}),b.tooltipPos=[g,I+q.pos-e.plotTop]):u&&(b.graphic=u.destroy())}m.allowOverlapX||(k(y,function(a){a.plotX=a.anchorX;r.push(a)}),B.distribute(r,h?q.len:this.xAxis.len,100),a.forEach(function(a){var b=a.graphic&&y[a.plotX];b&&(a.graphic[a.graphic.isNew?"attr":"animate"]({x:b.pos+b.align*
b.size,anchorX:a.anchorX}),l(b.pos)?a.graphic.isNew=!1:(a.graphic.attr({x:-9999,anchorX:-9999}),a.graphic.isNew=!0))}));m.useHTML&&G(this.markerGroup,"on",function(a){return D.prototype.on.apply(a.apply(this,[].slice.call(arguments,1)),[].slice.call(arguments,1))})};d.prototype.drawTracker=function(){var a=this.points;e.prototype.drawTracker.call(this);a.forEach(function(c){var e=c.graphic;e&&h(e.element,"mouseover",function(){0<c.stackIndex&&!c.raised&&(c._y=e.y,e.attr({y:c._y-8}),c.raised=!0);a.forEach(function(a){a!==
c&&a.raised&&a.graphic&&(a.graphic.attr({y:a._y}),a.raised=!1)})})})};d.prototype.pointAttribs=function(a,c){var e=this.options,d=a&&a.color||this.color,f=e.lineColor,h=a&&a.lineWidth;a=a&&a.fillColor||e.fillColor;c&&(a=e.states[c].fillColor,f=e.states[c].lineColor,h=e.states[c].lineWidth);return{fill:a||d,stroke:f||d,"stroke-width":h||e.lineWidth||0}};d.prototype.setClip=function(){a.prototype.setClip.apply(this,arguments);!1!==this.options.clip&&this.sharedClipKey&&this.markerGroup.clip(this.chart[this.sharedClipKey])};
d.defaultOptions=c(p.defaultOptions,{pointRange:0,allowOverlapX:!1,shape:"flag",stackDistance:12,textAlign:"center",tooltip:{pointFormat:"{point.text}<br/>"},threshold:null,y:-30,fillColor:n.backgroundColor,lineWidth:1,states:{hover:{lineColor:n.neutralColor100,fillColor:n.highlightColor20}},style:{fontSize:"11px",fontWeight:"bold"}});return d}(p);e(A.prototype,{allowDG:!1,buildKDTree:C,forceCrop:!0,getPlotBox:x.getPlotBox,init:a.prototype.init,invertGroups:C,invertible:!1,noSharedTooltip:!0,pointClass:d,
sorted:!1,takeOrdinalPosition:!1,trackerGroups:["markerGroup"],translate:x.translate});w.registerSeriesType("flags",A);"";"";return A});K(d,"Extensions/RangeSelector.js",[d["Core/Axis/Axis.js"],d["Core/Chart/Chart.js"],d["Core/Globals.js"],d["Core/Options.js"],d["Core/Color/Palette.js"],d["Core/Renderer/SVG/SVGElement.js"],d["Core/Utilities.js"]],function(d,B,x,n,w,D,A){function v(a){if(-1!==a.indexOf("%L"))return"text";var b="aAdewbBmoyY".split("").some(function(b){return-1!==a.indexOf("%"+b)}),
c="HkIlMS".split("").some(function(b){return-1!==a.indexOf("%"+b)});return b&&c?"datetime-local":b?"date":c?"time":"text"}var C=n.defaultOptions,a=A.addEvent,p=A.createElement,h=A.css,l=A.defined,e=A.destroyObjectProperties,c=A.discardElement,k=A.extend,G=A.find,m=A.fireEvent,q=A.isNumber,f=A.merge,H=A.objectEach,K=A.pad,M=A.pick,J=A.pInt,L=A.splat;k(C,{rangeSelector:{allButtonsEnabled:!1,buttons:void 0,buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,
height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%b %e, %Y",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:w.highlightColor80,cursor:"pointer"},labelStyle:{color:w.neutralColor60}}});k(C.lang,{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"\u2192"});
var E=function(){function r(a){this.buttons=void 0;this.buttonOptions=r.prototype.defaultButtons;this.initialButtonGroupWidth=0;this.options=void 0;this.chart=a;this.init(a)}r.prototype.clickButton=function(b,c){var g=this.chart,e=this.buttonOptions[b],t=g.xAxis[0],f=g.scroller&&g.scroller.getUnionExtremes()||t||{},r=f.dataMin,h=f.dataMax,k=t&&Math.round(Math.min(t.max,M(h,t.max))),p=e.type;f=e._range;var n,y=e.dataGrouping;if(null!==r&&null!==h){g.fixedRange=f;y&&(this.forcedDataGrouping=!0,d.prototype.setDataGrouping.call(t||
{chart:this.chart},y,!1),this.frozenStates=e.preserveDataGrouping);if("month"===p||"year"===p)if(t){p={range:e,max:k,chart:g,dataMin:r,dataMax:h};var v=t.minFromRange.call(p);q(p.newMax)&&(k=p.newMax)}else f=e;else if(f)v=Math.max(k-f,r),k=Math.min(v+f,h);else if("ytd"===p)if(t)"undefined"===typeof h&&(r=Number.MAX_VALUE,h=Number.MIN_VALUE,g.series.forEach(function(a){a=a.xData;r=Math.min(a[0],r);h=Math.max(a[a.length-1],h)}),c=!1),k=this.getYTDExtremes(h,r,g.time.useUTC),v=n=k.min,k=k.max;else{this.deferredYTDClick=
b;return}else"all"===p&&t&&(v=r,k=h);l(v)&&(v+=e._offsetMin);l(k)&&(k+=e._offsetMax);this.setSelected(b);this.dropdown&&(this.dropdown.selectedIndex=b+1);if(t)t.setExtremes(v,k,M(c,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:e});else{var w=L(g.options.xAxis)[0];var F=w.range;w.range=f;var x=w.min;w.min=n;a(g,"load",function(){w.range=F;w.min=x})}m(this,"afterBtnClick")}};r.prototype.setSelected=function(a){this.selected=this.options.selected=a};r.prototype.init=function(b){var c=
this,g=b.options.rangeSelector,e=g.buttons||c.defaultButtons.slice(),d=g.selected,f=function(){var a=c.minInput,b=c.maxInput;a&&a.blur&&m(a,"blur");b&&b.blur&&m(b,"blur")};c.chart=b;c.options=g;c.buttons=[];c.buttonOptions=e;this.eventsToUnbind=[];this.eventsToUnbind.push(a(b.container,"mousedown",f));this.eventsToUnbind.push(a(b,"resize",f));e.forEach(c.computeButtonRange);"undefined"!==typeof d&&e[d]&&this.clickButton(d,!1);this.eventsToUnbind.push(a(b,"load",function(){b.xAxis&&b.xAxis[0]&&a(b.xAxis[0],
"setExtremes",function(a){this.max-this.min!==b.fixedRange&&"rangeSelectorButton"!==a.trigger&&"updatedData"!==a.trigger&&c.forcedDataGrouping&&!c.frozenStates&&this.setDataGrouping(!1,!1)})}))};r.prototype.updateButtonStates=function(){var a=this,c=this.chart,g=this.dropdown,e=c.xAxis[0],d=Math.round(e.max-e.min),f=!e.hasVisibleSeries,r=c.scroller&&c.scroller.getUnionExtremes()||e,h=r.dataMin,k=r.dataMax;c=a.getYTDExtremes(k,h,c.time.useUTC);var l=c.min,m=c.max,p=a.selected,n=q(p),y=a.options.allButtonsEnabled,
v=a.buttons;a.buttonOptions.forEach(function(b,c){var t=b._range,u=b.type,r=b.count||1,z=v[c],q=0,I=b._offsetMax-b._offsetMin;b=c===p;var w=t>k-h,F=t<e.minRange,x=!1,P=!1;t=t===d;("month"===u||"year"===u)&&d+36E5>=864E5*{month:28,year:365}[u]*r-I&&d-36E5<=864E5*{month:31,year:366}[u]*r+I?t=!0:"ytd"===u?(t=m-l+I===d,x=!b):"all"===u&&(t=e.max-e.min>=k-h,P=!b&&n&&t);u=!y&&(w||F||P||f);r=b&&t||t&&!n&&!x||b&&a.frozenStates;u?q=3:r&&(n=!0,q=2);z.state!==q&&(z.setState(q),g&&(g.options[c+1].disabled=u,2===
q&&(g.selectedIndex=c+1)),0===q&&p===c&&a.setSelected())})};r.prototype.computeButtonRange=function(a){var b=a.type,c=a.count||1,e={millisecond:1,second:1E3,minute:6E4,hour:36E5,day:864E5,week:6048E5};if(e[b])a._range=e[b]*c;else if("month"===b||"year"===b)a._range=864E5*{month:30,year:365}[b]*c;a._offsetMin=M(a.offsetMin,0);a._offsetMax=M(a.offsetMax,0);a._range+=a._offsetMax-a._offsetMin};r.prototype.getInputValue=function(a){a="min"===a?this.minInput:this.maxInput;var b=this.chart.options.rangeSelector,
c=this.chart.time;return a?("text"===a.type&&b.inputDateParser||this.defaultInputDateParser)(a.value,c.useUTC,c):0};r.prototype.setInputValue=function(a,c){var b=this.options,e=this.chart.time,d="min"===a?this.minInput:this.maxInput;a="min"===a?this.minDateBox:this.maxDateBox;if(d){var t=d.getAttribute("data-hc-time");t=l(t)?Number(t):void 0;l(c)&&(l(t)&&d.setAttribute("data-hc-time-previous",t),d.setAttribute("data-hc-time",c),t=c);d.value=e.dateFormat(this.inputTypeFormats[d.type]||b.inputEditDateFormat,
t);a&&a.attr({text:e.dateFormat(b.inputDateFormat,t)})}};r.prototype.setInputExtremes=function(a,c,e){if(a="min"===a?this.minInput:this.maxInput){var b=this.inputTypeFormats[a.type],g=this.chart.time;b&&(c=g.dateFormat(b,c),a.min!==c&&(a.min=c),e=g.dateFormat(b,e),a.max!==e&&(a.max=e))}};r.prototype.showInput=function(a){var b="min"===a?this.minDateBox:this.maxDateBox;if((a="min"===a?this.minInput:this.maxInput)&&b&&this.inputGroup){var c="text"===a.type,e=this.inputGroup,d=e.translateX;e=e.translateY;
h(a,{width:c?b.width-2+"px":"auto",height:c?b.height-2+"px":"auto",border:"2px solid silver"});c?h(a,{left:d+b.x+"px",top:e+"px"}):h(a,{left:Math.min(Math.round(b.x+d-(a.offsetWidth-b.width)/2),this.chart.chartWidth-a.offsetWidth)+"px",top:e-(a.offsetHeight-b.height)/2+"px"})}};r.prototype.hideInput=function(a){(a="min"===a?this.minInput:this.maxInput)&&h(a,{top:"-9999em",border:0,width:"1px",height:"1px"})};r.prototype.defaultInputDateParser=function(a,c,e){var b=a.split("/").join("-").split(" ").join("T");
-1===b.indexOf("T")&&(b+="T00:00");if(c)b+="Z";else{var g;if(g=x.isSafari)g=b,g=!(6<g.length&&(g.lastIndexOf("-")===g.length-6||g.lastIndexOf("+")===g.length-6));g&&(g=(new Date(b)).getTimezoneOffset()/60,b+=0>=g?"+"+K(-g)+":00":"-"+K(g)+":00")}b=Date.parse(b);q(b)||(a=a.split("-"),b=Date.UTC(J(a[0]),J(a[1])-1,J(a[2])));e&&c&&(b+=e.getTimezoneOffset(b));return b};r.prototype.drawInput=function(a){function b(){var b=r.getInputValue(a),e=c.xAxis[0],g=c.scroller&&c.scroller.xAxis?c.scroller.xAxis:e,
d=g.dataMin;g=g.dataMax;var t=r.maxInput,f=r.minInput;b!==Number(A.getAttribute("data-hc-time-previous"))&&q(b)&&(A.setAttribute("data-hc-time-previous",b),y&&t&&q(d)?b>Number(t.getAttribute("data-hc-time"))?b=void 0:b<d&&(b=d):f&&q(g)&&(b<Number(f.getAttribute("data-hc-time"))?b=void 0:b>g&&(b=g)),"undefined"!==typeof b&&e.setExtremes(y?b:e.min,y?e.max:b,void 0,void 0,{trigger:"rangeSelectorInput"}))}var c=this.chart,e=this.div,d=this.inputGroup,r=this,l=c.renderer.style||{},m=c.renderer,n=c.options.rangeSelector,
y="min"===a,F=C.lang[y?"rangeSelectorFrom":"rangeSelectorTo"];F=m.label(F,0).addClass("highcharts-range-label").attr({padding:F?2:0}).add(d);m=m.label("",0).addClass("highcharts-range-input").attr({padding:2,width:n.inputBoxWidth,height:n.inputBoxHeight,"text-align":"center"}).on("click",function(){r.showInput(a);r[a+"Input"].focus()});c.styledMode||m.attr({stroke:n.inputBoxBorderColor,"stroke-width":1});m.add(d);var A=p("input",{name:a,className:"highcharts-range-selector"},void 0,e);A.setAttribute("type",
v(n.inputDateFormat||"%b %e, %Y"));c.styledMode||(F.css(f(l,n.labelStyle)),m.css(f({color:w.neutralColor80},l,n.inputStyle)),h(A,k({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:l.fontSize,fontFamily:l.fontFamily,top:"-9999em"},n.inputStyle)));A.onfocus=function(){r.showInput(a)};A.onblur=function(){A===x.doc.activeElement&&b();r.hideInput(a);r.setInputValue(a);A.blur()};var B=!1;A.onchange=function(){b();B||(r.hideInput(a),
A.blur())};A.onkeypress=function(a){13===a.keyCode&&b()};A.onkeydown=function(){B=!0};A.onkeyup=function(){B=!1};return{dateBox:m,input:A,label:F}};r.prototype.getPosition=function(){var a=this.chart,c=a.options.rangeSelector;a="top"===c.verticalAlign?a.plotTop-a.axisOffset[0]:0;return{buttonTop:a+c.buttonPosition.y,inputTop:a+c.inputPosition.y-10}};r.prototype.getYTDExtremes=function(a,c,e){var b=this.chart.time,g=new b.Date(a),d=b.get("FullYear",g);e=e?b.Date.UTC(d,0,1):+new b.Date(d,0,1);c=Math.max(c,
e);g=g.getTime();return{max:Math.min(a||g,g),min:c}};r.prototype.render=function(a,c){var b=this.chart,e=b.renderer,d=b.container,f=b.options,t=f.rangeSelector,r=M(f.chart.style&&f.chart.style.zIndex,0)+1;f=t.inputEnabled;if(!1!==t.enabled){this.rendered||(this.group=e.g("range-selector-group").attr({zIndex:7}).add(),this.div=p("div",void 0,{position:"relative",height:0,zIndex:r}),this.buttonOptions.length&&this.renderButtons(),d.parentNode&&d.parentNode.insertBefore(this.div,d),f&&(this.inputGroup=
e.g("input-group").add(this.group),e=this.drawInput("min"),this.minDateBox=e.dateBox,this.minLabel=e.label,this.minInput=e.input,e=this.drawInput("max"),this.maxDateBox=e.dateBox,this.maxLabel=e.label,this.maxInput=e.input));if(f&&(this.setInputValue("min",a),this.setInputValue("max",c),a=b.scroller&&b.scroller.getUnionExtremes()||b.xAxis[0]||{},l(a.dataMin)&&l(a.dataMax)&&(b=b.xAxis[0].minRange||0,this.setInputExtremes("min",a.dataMin,Math.min(a.dataMax,this.getInputValue("max"))-b),this.setInputExtremes("max",
Math.max(a.dataMin,this.getInputValue("min"))+b,a.dataMax)),this.inputGroup)){var h=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(function(a){a&&a.width&&(a.attr({x:h}),h+=a.width+t.inputSpacing)})}this.alignElements();this.rendered=!0}};r.prototype.renderButtons=function(){var b=this,c=this.buttons,e=this.options,d=C.lang,r=this.chart.renderer,h=f(e.buttonTheme),k=h&&h.states,l=h.width||28;delete h.width;this.buttonGroup=r.g("range-selector-buttons").add(this.group);var n=
this.dropdown=p("select",void 0,{position:"absolute",width:"1px",height:"1px",padding:0,border:0,top:"-9999em",cursor:"pointer",opacity:.0001},this.div);a(n,"touchstart",function(){n.style.fontSize="16px"});[[x.isMS?"mouseover":"mouseenter"],[x.isMS?"mouseout":"mouseleave"],["change","click"]].forEach(function(e){var d=e[0],g=e[1];a(n,d,function(){var a=c[b.currentButtonIndex()];a&&m(a.element,g||d)})});this.zoomText=r.text(d.rangeSelectorZoom,0,15).add(this.buttonGroup);this.chart.styledMode||(this.zoomText.css(e.labelStyle),
h["stroke-width"]=M(h["stroke-width"],0));p("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,n);this.buttonOptions.forEach(function(a,e){p("option",{textContent:a.title||a.text},void 0,n);c[e]=r.button(a.text,0,0,function(c){var d=a.events&&a.events.click,g;d&&(g=d.call(a,c));!1!==g&&b.clickButton(e);b.isActive=!0},h,k&&k.hover,k&&k.select,k&&k.disabled).attr({"text-align":"center",width:l}).add(b.buttonGroup);a.title&&c[e].attr("title",a.title)})};r.prototype.alignElements=function(){var a=
this,c=this.buttonGroup,e=this.buttons,d=this.chart,f=this.group,r=this.inputGroup,h=this.options,k=this.zoomText,l=d.options,m=l.exporting&&!1!==l.exporting.enabled&&l.navigation&&l.navigation.buttonOptions;l=h.buttonPosition;var n=h.inputPosition,p=h.verticalAlign,q=function(b,c){return m&&a.titleCollision(d)&&"top"===p&&"right"===c.align&&c.y-b.getBBox().height-12<(m.y||0)+(m.height||0)+d.spacing[0]?-40:0},y=d.plotLeft;if(f&&l&&n){var v=l.x-d.spacing[3];if(c){this.positionButtons();if(!this.initialButtonGroupWidth){var w=
0;k&&(w+=k.getBBox().width+5);e.forEach(function(a,b){w+=a.width;b!==e.length-1&&(w+=h.buttonSpacing)});this.initialButtonGroupWidth=w}y-=d.spacing[3];this.updateButtonStates();k=q(c,l);this.alignButtonGroup(k);f.placed=c.placed=d.hasLoaded}c=0;r&&(c=q(r,n),"left"===n.align?v=y:"right"===n.align&&(v=-Math.max(d.axisOffset[1],-c)),r.align({y:n.y,width:r.getBBox().width,align:n.align,x:n.x+v-2},!0,d.spacingBox),r.placed=d.hasLoaded);this.handleCollision(c);f.align({verticalAlign:p},!0,d.spacingBox);
r=f.alignAttr.translateY;c=f.getBBox().height+20;q=0;"bottom"===p&&(q=(q=d.legend&&d.legend.options)&&"bottom"===q.verticalAlign&&q.enabled&&!q.floating?d.legend.legendHeight+M(q.margin,10):0,c=c+q-20,q=r-c-(h.floating?0:h.y)-(d.titleOffset?d.titleOffset[2]:0)-10);if("top"===p)h.floating&&(q=0),d.titleOffset&&d.titleOffset[0]&&(q=d.titleOffset[0]),q+=d.margin[0]-d.spacing[0]||0;else if("middle"===p)if(n.y===l.y)q=r;else if(n.y||l.y)q=0>n.y||0>l.y?q-Math.min(n.y,l.y):r-c;f.translate(h.x,h.y+Math.floor(q));
l=this.minInput;n=this.maxInput;r=this.dropdown;h.inputEnabled&&l&&n&&(l.style.marginTop=f.translateY+"px",n.style.marginTop=f.translateY+"px");r&&(r.style.marginTop=f.translateY+"px")}};r.prototype.alignButtonGroup=function(a,c){var b=this.chart,e=this.buttonGroup,d=this.options.buttonPosition,f=b.plotLeft-b.spacing[3],t=d.x-b.spacing[3];"right"===d.align?t+=a-f:"center"===d.align&&(t-=f/2);e&&e.align({y:d.y,width:M(c,this.initialButtonGroupWidth),align:d.align,x:t},!0,b.spacingBox)};r.prototype.positionButtons=
function(){var a=this.buttons,c=this.chart,e=this.options,d=this.zoomText,f=c.hasLoaded?"animate":"attr",r=e.buttonPosition,h=c.plotLeft,k=h;d&&"hidden"!==d.visibility&&(d[f]({x:M(h+r.x,h)}),k+=r.x+d.getBBox().width+5);this.buttonOptions.forEach(function(b,c){if("hidden"!==a[c].visibility)a[c][f]({x:k}),k+=a[c].width+e.buttonSpacing;else a[c][f]({x:h})})};r.prototype.handleCollision=function(a){var b=this,c=this.chart,e=this.buttonGroup,d=this.inputGroup,f=this.options,r=f.buttonPosition,h=f.dropdown,
k=f.inputPosition;f=function(){var a=0;b.buttons.forEach(function(b){b=b.getBBox();b.width>a&&(a=b.width)});return a};var l=function(b){if(d&&e){var c=d.alignAttr.translateX+d.alignOptions.x-a+d.getBBox().x+2,g=d.alignOptions.width,f=e.alignAttr.translateX+e.getBBox().x;return f+b>c&&c+g>f&&r.y<k.y+d.getBBox().height}return!1},m=function(){d&&e&&d.attr({translateX:d.alignAttr.translateX+(c.axisOffset[1]>=-a?0:-a),translateY:d.alignAttr.translateY+e.getBBox().height+10})};if(e){if("always"===h){this.collapseButtons(a);
l(f())&&m();return}"never"===h&&this.expandButtons()}d&&e?k.align===r.align||l(this.initialButtonGroupWidth+20)?"responsive"===h?(this.collapseButtons(a),l(f())&&m()):m():"responsive"===h&&this.expandButtons():e&&"responsive"===h&&(this.initialButtonGroupWidth>c.plotWidth?this.collapseButtons(a):this.expandButtons())};r.prototype.collapseButtons=function(a){var b,c=this.buttons,e=this.buttonOptions,d=this.dropdown,f=this.options,r=this.zoomText,h=function(a){return{text:a?a+" \u25be":"\u25be",width:"auto",
paddingLeft:8,paddingRight:8}};r&&r.hide();var k=!1;e.forEach(function(a,b){b=c[b];2!==b.state?b.hide():(b.show(),b.attr(h(a.text)),k=!0)});k||(d&&(d.selectedIndex=0),c[0].show(),c[0].attr(h(null===(b=this.zoomText)||void 0===b?void 0:b.textStr)));b=f.buttonPosition.align;this.positionButtons();"right"!==b&&"center"!==b||this.alignButtonGroup(a,c[this.currentButtonIndex()].getBBox().width);this.showDropdown()};r.prototype.expandButtons=function(){var a=this.buttons,c=this.buttonOptions,e=this.options,
d=this.zoomText;this.hideDropdown();d&&d.show();c.forEach(function(b,c){c=a[c];c.show();c.attr({text:b.text,width:e.buttonTheme.width||28,paddingLeft:"unset",paddingRight:"unset"});2>c.state&&c.setState(0)});this.positionButtons()};r.prototype.currentButtonIndex=function(){var a=this.dropdown;return a&&0<a.selectedIndex?a.selectedIndex-1:0};r.prototype.showDropdown=function(){var a=this.buttonGroup,c=this.buttons,e=this.chart,d=this.dropdown;if(a&&d){var f=a.translateX;a=a.translateY;c=c[this.currentButtonIndex()].getBBox();
h(d,{left:e.plotLeft+f+"px",top:a+.5+"px",width:c.width+"px",height:c.height+"px"});this.hasVisibleDropdown=!0}};r.prototype.hideDropdown=function(){var a=this.dropdown;a&&(h(a,{top:"-9999em",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)};r.prototype.getHeight=function(){var a=this.options,c=this.group,e=a.y,d=a.buttonPosition.y,f=a.inputPosition.y;if(a.height)return a.height;this.alignElements();a=c?c.getBBox(!0).height+13+e:0;c=Math.min(f,d);if(0>f&&0>d||0<f&&0<d)a+=Math.abs(c);return a};
r.prototype.titleCollision=function(a){return!(a.options.title.text||a.options.subtitle.text)};r.prototype.update=function(a){var b=this.chart;f(!0,b.options.rangeSelector,a);this.destroy();this.init(b);this.render()};r.prototype.destroy=function(){var a=this,d=a.minInput,g=a.maxInput;a.eventsToUnbind&&(a.eventsToUnbind.forEach(function(a){return a()}),a.eventsToUnbind=void 0);e(a.buttons);d&&(d.onfocus=d.onblur=d.onchange=null);g&&(g.onfocus=g.onblur=g.onchange=null);H(a,function(b,e){b&&"chart"!==
e&&(b instanceof D?b.destroy():b instanceof window.HTMLElement&&c(b));b!==r.prototype[e]&&(a[e]=null)},this)};return r}();E.prototype.defaultButtons=[{type:"month",count:1,text:"1m",title:"View 1 month"},{type:"month",count:3,text:"3m",title:"View 3 months"},{type:"month",count:6,text:"6m",title:"View 6 months"},{type:"ytd",text:"YTD",title:"View year to date"},{type:"year",count:1,text:"1y",title:"View 1 year"},{type:"all",text:"All",title:"View all"}];E.prototype.inputTypeFormats={"datetime-local":"%Y-%m-%dT%H:%M:%S",
date:"%Y-%m-%d",time:"%H:%M:%S"};d.prototype.minFromRange=function(){var a=this.range,b=a.type,c=this.max,e=this.chart.time,d=function(a,c){var d="year"===b?"FullYear":"Month",f=new e.Date(a),g=e.get(d,f);e.set(d,f,g+c);g===e.get(d,f)&&e.set("Date",f,0);return f.getTime()-a};if(q(a)){var f=c-a;var h=a}else f=c+d(c,-a.count),this.chart&&(this.chart.fixedRange=c-f);var k=M(this.dataMin,Number.MIN_VALUE);q(f)||(f=k);f<=k&&(f=k,"undefined"===typeof h&&(h=d(f,a.count)),this.newMax=Math.min(f+h,this.dataMax));
q(c)||(f=void 0);return f};if(!x.RangeSelector){var F=[],y=function(c){function b(){d&&(e=c.xAxis[0].getExtremes(),h=c.legend,k=null===d||void 0===d?void 0:d.options.verticalAlign,q(e.min)&&d.render(e.min,e.max),h.display&&"top"===k&&k===h.options.verticalAlign&&(r=f(c.spacingBox),r.y="vertical"===h.options.layout?c.plotTop:r.y+d.getHeight(),h.group.placed=!1,h.align(r)))}var e,d=c.rangeSelector,h,r,k;d&&(G(F,function(a){return a[0]===c})||F.push([c,[a(c.xAxis[0],"afterSetExtremes",function(a){d&&
d.render(a.min,a.max)}),a(c,"redraw",b)]]),b())};a(B,"afterGetContainer",function(){var a;if(null===(a=this.options.rangeSelector)||void 0===a?0:a.enabled)this.rangeSelector=new E(this)});a(B,"beforeRender",function(){var a=this.axes,b=this.rangeSelector;b&&(q(b.deferredYTDClick)&&(b.clickButton(b.deferredYTDClick),delete b.deferredYTDClick),a.forEach(function(a){a.updateNames();a.setScale()}),this.getAxisMargins(),b.render(),a=b.options.verticalAlign,b.options.floating||("bottom"===a?this.extraBottomMargin=
!0:"middle"!==a&&(this.extraTopMargin=!0)))});a(B,"update",function(a){var b=a.options.rangeSelector;a=this.rangeSelector;var c=this.extraBottomMargin,e=this.extraTopMargin;b&&b.enabled&&!l(a)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=a=new E(this));this.extraTopMargin=this.extraBottomMargin=!1;a&&(y(this),b=b&&b.verticalAlign||a.options&&a.options.verticalAlign,a.options.floating||("bottom"===b?this.extraBottomMargin=!0:"middle"!==b&&(this.extraTopMargin=
!0)),this.extraBottomMargin!==c||this.extraTopMargin!==e)&&(this.isDirtyBox=!0)});a(B,"render",function(){var a=this.rangeSelector;a&&!a.options.floating&&(a.render(),a=a.options.verticalAlign,"bottom"===a?this.extraBottomMargin=!0:"middle"!==a&&(this.extraTopMargin=!0))});a(B,"getMargins",function(){var a=this.rangeSelector;a&&(a=a.getHeight(),this.extraTopMargin&&(this.plotTop+=a),this.extraBottomMargin&&(this.marginBottom+=a))});B.prototype.callbacks.push(y);a(B,"destroy",function(){for(var a=
0;a<F.length;a++){var b=F[a];if(b[0]===this){b[1].forEach(function(a){return a()});F.splice(a,1);break}}});x.RangeSelector=E}return x.RangeSelector});K(d,"Core/Chart/StockChart.js",[d["Core/Axis/Axis.js"],d["Core/Chart/Chart.js"],d["Core/Globals.js"],d["Core/Color/Palette.js"],d["Core/Series/Point.js"],d["Core/Series/Series.js"],d["Core/Renderer/SVG/SVGRenderer.js"],d["Core/Utilities.js"]],function(d,B,x,n,w,D,A,v){function C(a,c,e){var b=M(a)||a.nodeName,d=arguments[b?1:0],f=d,h=d.series,k=H(),l,
r=L(d.navigator&&d.navigator.enabled,k.navigator.enabled,!0);d.xAxis=E(d.xAxis||{}).map(function(a,b){return J({minPadding:0,maxPadding:0,overscroll:0,ordinal:!0,title:{text:null},labels:{overflow:"justify"},showLastLabel:!0},k.xAxis,k.xAxis&&k.xAxis[b],a,{type:"datetime",categories:null},r?{startOnTick:!1,endOnTick:!1}:null)});d.yAxis=E(d.yAxis||{}).map(function(a,b){l=L(a.opposite,!0);return J({labels:{y:-2},opposite:l,showLastLabel:!(!a.categories&&"category"!==a.type),title:{text:null}},k.yAxis,
k.yAxis&&k.yAxis[b],a)});d.series=null;d=J({chart:{panning:{enabled:!0,type:"x"},pinchType:"x"},navigator:{enabled:r},scrollbar:{enabled:L(k.scrollbar.enabled,!0)},rangeSelector:{enabled:L(k.rangeSelector.enabled,!0)},title:{text:null},tooltip:{split:L(k.tooltip.split,!0),crosshairs:!0},legend:{enabled:!1}},d,{isStock:!0});d.series=f.series=h;return b?new B(a,d,e):new B(d,c)}var a=w.prototype.tooltipFormatter,p=D.prototype,h=p.init,l=p.processData;p=v.addEvent;var e=v.arrayMax,c=v.arrayMin,k=v.clamp,
G=v.defined,m=v.extend,q=v.find,f=v.format,H=v.getOptions,K=v.isNumber,M=v.isString,J=v.merge,L=v.pick,E=v.splat;p(D,"setOptions",function(a){var c;this.chart.options.isStock&&(this.is("column")||this.is("columnrange")?c={borderWidth:0,shadow:!1}:this.is("scatter")||this.is("sma")||(c={marker:{enabled:!1,radius:2}}),c&&(a.plotOptions[this.type]=J(a.plotOptions[this.type],c)))});p(d,"autoLabelAlign",function(a){var c=this.chart,d=this.options;c=c._labelPanes=c._labelPanes||{};var b=this.options.labels;
this.chart.options.isStock&&"yAxis"===this.coll&&(d=d.top+","+d.height,!c[d]&&b.enabled&&(15===b.x&&(b.x=0),"undefined"===typeof b.align&&(b.align="right"),c[d]=this,a.align="right",a.preventDefault()))});p(d,"destroy",function(){var a=this.chart,c=this.options&&this.options.top+","+this.options.height;c&&a._labelPanes&&a._labelPanes[c]===this&&delete a._labelPanes[c]});p(d,"getPlotLinePath",function(a){function c(a){var c="xAxis"===a?"yAxis":"xAxis";a=d.options[c];return K(a)?[e[c][a]]:M(a)?[e.get(a)]:
b.map(function(a){return a[c]})}var d=this,b=this.isLinked&&!this.series?this.linkedParent.series:this.series,e=d.chart,f=e.renderer,h=d.left,l=d.top,m,n,p,v,w=[],x=[],A=a.translatedValue,F=a.value,B=a.force;if(e.options.isStock&&!1!==a.acrossPanes&&"xAxis"===d.coll||"yAxis"===d.coll){a.preventDefault();x=c(d.coll);var D=d.isXAxis?e.yAxis:e.xAxis;D.forEach(function(a){if(G(a.options.id)?-1===a.options.id.indexOf("navigator"):1){var b=a.isXAxis?"yAxis":"xAxis";b=G(a.options[b])?e[b][a.options[b]]:
e[b][0];d===b&&x.push(a)}});var C=x.length?[]:[d.isXAxis?e.yAxis[0]:e.xAxis[0]];x.forEach(function(a){-1!==C.indexOf(a)||q(C,function(b){return b.pos===a.pos&&b.len===a.len})||C.push(a)});var E=L(A,d.translate(F,null,null,a.old));K(E)&&(d.horiz?C.forEach(function(a){var b;n=a.pos;v=n+a.len;m=p=Math.round(E+d.transB);"pass"!==B&&(m<h||m>h+d.width)&&(B?m=p=k(m,h,h+d.width):b=!0);b||w.push(["M",m,n],["L",p,v])}):C.forEach(function(a){var b;m=a.pos;p=m+a.len;n=v=Math.round(l+d.height-E);"pass"!==B&&(n<
l||n>l+d.height)&&(B?n=v=k(n,l,l+d.height):b=!0);b||w.push(["M",m,n],["L",p,v])}));a.path=0<w.length?f.crispPolyLine(w,a.lineWidth||1):null}});A.prototype.crispPolyLine=function(a,c){for(var d=0;d<a.length;d+=2){var b=a[d],e=a[d+1];b[1]===e[1]&&(b[1]=e[1]=Math.round(b[1])-c%2/2);b[2]===e[2]&&(b[2]=e[2]=Math.round(b[2])+c%2/2)}return a};p(d,"afterHideCrosshair",function(){this.crossLabel&&(this.crossLabel=this.crossLabel.hide())});p(d,"afterDrawCrosshair",function(a){var c,d;if(G(this.crosshair.label)&&
this.crosshair.label.enabled&&this.cross){var b=this.chart,e=this.logarithmic,g=this.options.crosshair.label,h=this.horiz,k=this.opposite,l=this.left,p=this.top,q=this.crossLabel,v=g.format,w="",x="inside"===this.options.tickPosition,A=!1!==this.crosshair.snap,B=0,F=a.e||this.cross&&this.cross.e,C=a.point;a=this.min;var D=this.max;e&&(a=e.lin2log(a),D=e.lin2log(D));e=h?"center":k?"right"===this.labelAlign?"right":"left":"left"===this.labelAlign?"left":"center";q||(q=this.crossLabel=b.renderer.label(null,
null,null,g.shape||"callout").addClass("highcharts-crosshair-label"+(this.series[0]&&" highcharts-color-"+this.series[0].colorIndex)).attr({align:g.align||e,padding:L(g.padding,8),r:L(g.borderRadius,3),zIndex:2}).add(this.labelGroup),b.styledMode||q.attr({fill:g.backgroundColor||this.series[0]&&this.series[0].color||n.neutralColor60,stroke:g.borderColor||"","stroke-width":g.borderWidth||0}).css(m({color:n.backgroundColor,fontWeight:"normal",fontSize:"11px",textAlign:"center"},g.style)));h?(e=A?C.plotX+
l:F.chartX,p+=k?0:this.height):(e=k?this.width+l:0,p=A?C.plotY+p:F.chartY);v||g.formatter||(this.dateTime&&(w="%b %d, %Y"),v="{value"+(w?":"+w:"")+"}");w=A?C[this.isXAxis?"x":"y"]:this.toValue(h?F.chartX:F.chartY);q.attr({text:v?f(v,{value:w},b):g.formatter.call(this,w),x:e,y:p,visibility:w<a||w>D?"hidden":"visible"});g=q.getBBox();if(K(q.y))if(h){if(x&&!k||!x&&k)p=q.y-g.height}else p=q.y-g.height/2;h?(c=l-g.x,d=l+this.width-g.x):(c="left"===this.labelAlign?l:0,d="right"===this.labelAlign?l+this.width:
b.chartWidth);q.translateX<c&&(B=c-q.translateX);q.translateX+g.width>=d&&(B=-(q.translateX+g.width-d));q.attr({x:e+B,y:p,anchorX:h?e:this.opposite?0:b.chartWidth,anchorY:h?this.opposite?b.chartHeight:0:p+g.height/2})}});D.prototype.init=function(){h.apply(this,arguments);this.initCompare(this.options.compare)};D.prototype.setCompare=function(a){this.initCompare(a);this.userOptions.compare=a};D.prototype.initCompare=function(a){this.modifyValue="value"===a||"percent"===a?function(c,d){var b=this.compareValue;
return"undefined"!==typeof c&&"undefined"!==typeof b?(c="value"===a?c-b:c/b*100-(100===this.options.compareBase?0:100),d&&(d.change=c),c):0}:null;this.chart.hasRendered&&(this.isDirty=!0)};D.prototype.processData=function(a){var c,d=-1,b=!0===this.options.compareStart?0:1;l.apply(this,arguments);if(this.xAxis&&this.processedYData){var e=this.processedXData;var f=this.processedYData;var h=f.length;this.pointArrayMap&&(d=this.pointArrayMap.indexOf(this.options.pointValKey||this.pointValKey||"y"));for(c=
0;c<h-b;c++){var k=f[c]&&-1<d?f[c][d]:f[c];if(K(k)&&e[c+b]>=this.xAxis.min&&0!==k){this.compareValue=k;break}}}};p(D,"afterGetExtremes",function(a){a=a.dataExtremes;if(this.modifyValue&&a){var d=[this.modifyValue(a.dataMin),this.modifyValue(a.dataMax)];a.dataMin=c(d);a.dataMax=e(d)}});d.prototype.setCompare=function(a,c){this.isXAxis||(this.series.forEach(function(c){c.setCompare(a)}),L(c,!0)&&this.chart.redraw())};w.prototype.tooltipFormatter=function(c){var d=this.series.chart.numberFormatter;c=
c.replace("{point.change}",(0<this.change?"+":"")+d(this.change,L(this.series.tooltipOptions.changeDecimals,2)));return a.apply(this,[c])};p(D,"render",function(){var a=this.chart;if(!(a.is3d&&a.is3d()||a.polar)&&this.xAxis&&!this.xAxis.isRadial){var c=this.yAxis.len;if(this.xAxis.axisLine){var d=a.plotTop+a.plotHeight-this.yAxis.pos-this.yAxis.len,b=Math.floor(this.xAxis.axisLine.strokeWidth()/2);0<=d&&(c-=Math.max(b-d,0))}this.clipBox||!this.isDirty||this.isDirtyData?a[this.sharedClipKey]&&(a[this.sharedClipKey].animate({width:this.xAxis.len,
height:c}),a[this.sharedClipKey+"m"]&&a[this.sharedClipKey+"m"].animate({width:this.xAxis.len})):(this.clipBox=J(a.clipBox),this.clipBox.width=this.xAxis.len,this.clipBox.height=c)}});p(B,"update",function(a){a=a.options;"scrollbar"in a&&this.navigator&&(J(!0,this.options.scrollbar,a.scrollbar),this.navigator.update({},!1),delete a.scrollbar)});x.StockChart=x.stockChart=C;"";return C});K(d,"masters/modules/stock.src.js",[],function(){})});
//# sourceMappingURL=stock.js.map