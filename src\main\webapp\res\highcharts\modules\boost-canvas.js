/*
 Highcharts JS v9.0.1 (2021-02-15)

 Boost module

 (c) 2010-2021 Highsoft AS
 Author: Torstein Honsi

 License: www.highcharts.com/license
*/
(function(c){"object"===typeof module&&module.exports?(c["default"]=c,module.exports=c):"function"===typeof define&&define.amd?define("highcharts/modules/boost-canvas",["highcharts"],function(m){c(m);c.Highcharts=m;return c}):c("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(c){function m(c,m,q,z){c.hasOwnProperty(m)||(c[m]=z.apply(null,q))}c=c?c._modules:{};m(c,"Extensions/BoostCanvas.js",[c["Core/Chart/Chart.js"],c["Core/Color/Color.js"],c["Core/Globals.js"],c["Core/Color/Palette.js"],
c["Core/Series/Series.js"],c["Core/Series/SeriesRegistry.js"],c["Core/Utilities.js"]],function(c,m,q,z,A,B,r){var da=m.parse,C=q.doc,ea=q.noop,k=B.seriesTypes,D=r.addEvent,x=r.extend,fa=r.fireEvent,ha=r.isNumber,ia=r.merge,ja=r.pick,w=r.wrap,N;return function(){q.seriesTypes.heatmap&&w(q.seriesTypes.heatmap.prototype,"drawPoints",function(){var a=this.chart,b=this.getContext(),f=this.chart.inverted,c=this.xAxis,d=this.yAxis;b?(this.points.forEach(function(e){var g=e.plotY;"undefined"===typeof g||
isNaN(g)||null===e.y||(g=e.shapeArgs,e=a.styledMode?e.series.colorAttribs(e):e.series.pointAttribs(e),b.fillStyle=e.fill,f?b.fillRect(d.len-g.y+c.left,c.len-g.x+d.top,-g.height,-g.width):b.fillRect(g.x+c.left,g.y+d.top,g.width,g.height))}),this.canvasToSVG()):this.chart.showLoading("Your browser doesn't support HTML5 canvas, <br>please use a modern browser")});x(A.prototype,{getContext:function(){var a=this.chart,b=a.chartWidth,f=a.chartHeight,c=a.seriesGroup||this.group,d=this,m=function(a,d,f,b,
c,e,g){a.call(this,f,d,b,c,e,g)};a.isChartSeriesBoosting()&&(d=a,c=a.seriesGroup);var g=d.ctx;d.canvas||(d.canvas=C.createElement("canvas"),d.renderTarget=a.renderer.image("",0,0,b,f).addClass("highcharts-boost-canvas").add(c),d.ctx=g=d.canvas.getContext("2d"),a.inverted&&["moveTo","lineTo","rect","arc"].forEach(function(a){w(g,a,m)}),d.boostCopy=function(){d.renderTarget.attr({href:d.canvas.toDataURL("image/png")})},d.boostClear=function(){g.clearRect(0,0,d.canvas.width,d.canvas.height);d===this&&
d.renderTarget.attr({href:""})},d.boostClipRect=a.renderer.clipRect(),d.renderTarget.clip(d.boostClipRect));d.canvas.width!==b&&(d.canvas.width=b);d.canvas.height!==f&&(d.canvas.height=f);d.renderTarget.attr({x:0,y:0,width:b,height:f,style:"pointer-events: none",href:""});d.boostClipRect.attr(a.getBoostClipRect(d));return g},canvasToSVG:function(){this.chart.isChartSeriesBoosting()?this.boostClear&&this.boostClear():(this.boostCopy||this.chart.boostCopy)&&(this.boostCopy||this.chart.boostCopy)()},
cvsLineTo:function(a,b,f){a.lineTo(b,f)},renderCanvas:function(){var a=this,b=a.options,f=a.chart,c=this.xAxis,d=this.yAxis,k=(f.options.boost||{}).timeRendering||!1,g=0,w=a.processedXData,A=a.processedYData,O=b.data,l=c.getExtremes(),E=l.min,F=l.max;l=d.getExtremes();var B=l.min,C=l.max,P={},G,ka=!!a.sampling,H=b.marker&&b.marker.radius,Q=this.cvsDrawPoint,I=b.lineWidth?this.cvsLineTo:void 0,R=H&&1>=H?this.cvsMarkerSquare:this.cvsMarkerCircle,la=this.cvsStrokeBatch||1E3,ma=!1!==b.enableMouseTracking,
S;l=b.threshold;var u=d.getThreshold(l),T=ha(l),U=u,na=this.fill,V=a.pointArrayMap&&"low,high"===a.pointArrayMap.join(","),W=!!b.stacking,X=a.cropStart||0;l=f.options.loading;var oa=a.requireSorting,Y,pa=b.connectNulls,Z=!w,J,K,v,y,L,t=W?a.data:w||O,qa=a.fillOpacity?(new m(a.color)).setOpacity(ja(b.fillOpacity,.75)).get():a.color,aa=function(){na?(n.fillStyle=qa,n.fill()):(n.strokeStyle=a.color,n.lineWidth=b.lineWidth,n.stroke())},ba=function(d,b,c,e){0===g&&(n.beginPath(),I&&(n.lineJoin="round"));
f.scroller&&"highcharts-navigator-series"===a.options.className?(b+=f.scroller.top,c&&(c+=f.scroller.top)):b+=f.plotTop;d+=f.plotLeft;Y?n.moveTo(d,b):Q?Q(n,d,b,c,S):I?I(n,d,b):R&&R.call(a,n,d,b,H,e);g+=1;g===la&&(aa(),g=0);S={clientX:d,plotY:b,yBottom:c}},ra="x"===b.findNearestPointBy,ca=this.xData||this.options.xData||this.processedXData||!1,M=function(a,b,e){L=ra?a:a+","+b;ma&&!P[L]&&(P[L]=!0,f.inverted&&(a=c.len-a,b=d.len-b),sa.push({x:ca?ca[X+e]:!1,clientX:a,plotX:a,plotY:b,i:X+e}))};this.renderTarget&&
this.renderTarget.attr({href:""});(this.points||this.graph)&&this.destroyGraphics();a.plotGroup("group","series",a.visible?"visible":"hidden",b.zIndex,f.seriesGroup);a.markerGroup=a.group;D(a,"destroy",function(){a.markerGroup=null});var sa=this.points=[];var n=this.getContext();a.buildKDTree=ea;this.boostClear&&this.boostClear();this.visible&&(99999<O.length&&(f.options.loading=ia(l,{labelStyle:{backgroundColor:da(z.backgroundColor).setOpacity(.75).get(),padding:"1em",borderRadius:"0.5em"},style:{backgroundColor:"none",
opacity:1}}),r.clearTimeout(N),f.showLoading("Drawing..."),f.options.loading=l),k&&console.time("canvas rendering"),q.eachAsync(t,function(b,e){var g=!1,m=!1,k=!1,l=!1,n="undefined"===typeof f.index,r=!0;if(!n){if(Z){var p=b[0];var h=b[1];t[e+1]&&(k=t[e+1][0]);t[e-1]&&(l=t[e-1][0])}else p=b,h=A[e],t[e+1]&&(k=t[e+1]),t[e-1]&&(l=t[e-1]);k&&k>=E&&k<=F&&(g=!0);l&&l>=E&&l<=F&&(m=!0);if(V){Z&&(h=b.slice(1,3));var q=h[0];h=h[1]}else W&&(p=b.x,h=b.stackY,q=h-b.y);b=null===h;oa||(r=h>=B&&h<=C);if(!b&&(p>=
E&&p<=F&&r||g||m))if(p=Math.round(c.toPixels(p,!0)),ka){if("undefined"===typeof v||p===G){V||(q=h);if("undefined"===typeof y||h>K)K=h,y=e;if("undefined"===typeof v||q<J)J=q,v=e}p!==G&&("undefined"!==typeof v&&(h=d.toPixels(K,!0),u=d.toPixels(J,!0),ba(p,T?Math.min(h,U):h,T?Math.max(u,U):u,e),M(p,h,y),u!==h&&M(p,u,v)),v=y=void 0,G=p)}else h=Math.round(d.toPixels(h,!0)),ba(p,h,u,e),M(p,h,e);Y=b&&!pa;0===e%5E4&&(a.boostCopy||a.chart.boostCopy)&&(a.boostCopy||a.chart.boostCopy)()}return!n},function(){var b=
f.loadingDiv,d=f.loadingShown;aa();a.canvasToSVG();k&&console.timeEnd("canvas rendering");fa(a,"renderedCanvas");d&&(x(b.style,{transition:"opacity 250ms",opacity:0}),f.loadingShown=!1,N=setTimeout(function(){b.parentNode&&b.parentNode.removeChild(b);f.loadingDiv=f.loadingSpan=null},250));delete a.buildKDTree;a.buildKDTree()},f.renderer.forExport?Number.MAX_VALUE:void 0))}});k.scatter.prototype.cvsMarkerCircle=function(a,b,c,e){a.moveTo(b,c);a.arc(b,c,e,0,2*Math.PI,!1)};k.scatter.prototype.cvsMarkerSquare=
function(a,b,c,e){a.rect(b-e,c-e,2*e,2*e)};k.scatter.prototype.fill=!0;k.bubble&&(k.bubble.prototype.cvsMarkerCircle=function(a,b,c,e,d){a.moveTo(b,c);a.arc(b,c,this.radii&&this.radii[d],0,2*Math.PI,!1)},k.bubble.prototype.cvsStrokeBatch=1);x(k.area.prototype,{cvsDrawPoint:function(a,b,c,e,d){d&&b!==d.clientX&&(a.moveTo(d.clientX,d.yBottom),a.lineTo(d.clientX,d.plotY),a.lineTo(b,c),a.lineTo(b,e))},fill:!0,fillOpacity:!0,sampling:!0});x(k.column.prototype,{cvsDrawPoint:function(a,b,c,e){a.rect(b-1,
c,1,e-c)},fill:!0,sampling:!0});c.prototype.callbacks.push(function(a){D(a,"predraw",function(){a.renderTarget&&a.renderTarget.attr({href:""});a.canvas&&a.canvas.getContext("2d").clearRect(0,0,a.canvas.width,a.canvas.height)});D(a,"render",function(){a.boostCopy&&a.boostCopy()})})}});m(c,"masters/modules/boost-canvas.src.js",[],function(){})});
//# sourceMappingURL=boost-canvas.js.map