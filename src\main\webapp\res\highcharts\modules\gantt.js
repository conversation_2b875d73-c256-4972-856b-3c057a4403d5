/*
 Highcharts Gantt JS v9.0.1 (2021-02-15)

 Gantt series

 (c) 2016-2021 Lars <PERSON>

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/modules/gantt",["highcharts"],function(F){a(F);a.Highcharts=F;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){function F(a,u,t,r){a.hasOwnProperty(u)||(a[u]=r.apply(null,t))}a=a?a._modules:{};F(a,"Series/XRange/XRangePoint.js",[a["Core/Series/Point.js"],a["Core/Series/SeriesRegistry.js"]],function(a,u){var t=this&&this.__extends||
function(){var a=function(n,h){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(h,a){h.__proto__=a}||function(h,a){for(var D in a)a.hasOwnProperty(D)&&(h[D]=a[D])};return a(n,h)};return function(n,h){function r(){this.constructor=n}a(n,h);n.prototype=null===h?Object.create(h):(r.prototype=h.prototype,new r)}}();return function(r){function n(){var h=null!==r&&r.apply(this,arguments)||this;h.options=void 0;h.series=void 0;h.tooltipDateKeys=["x","x2"];return h}t(n,r);n.getColorByCategory=
function(h,a){var n=h.options.colors||h.chart.options.colors;h=a.y%(n?n.length:h.chart.options.chart.colorCount);return{colorIndex:h,color:n&&n[h]}};n.prototype.resolveColor=function(){var h=this.series;if(h.options.colorByPoint&&!this.options.color){var a=n.getColorByCategory(h,this);h.chart.styledMode||(this.color=a.color);this.options.colorIndex||(this.colorIndex=a.colorIndex)}else this.color||(this.color=h.color)};n.prototype.init=function(){a.prototype.init.apply(this,arguments);this.y||(this.y=
0);return this};n.prototype.setState=function(){a.prototype.setState.apply(this,arguments);this.series.drawPoint(this,this.series.getAnimationVerb())};n.prototype.getLabelConfig=function(){var h=a.prototype.getLabelConfig.call(this),n=this.series.yAxis.categories;h.x2=this.x2;h.yCategory=this.yCategory=n&&n[this.y];return h};n.prototype.isValid=function(){return"number"===typeof this.x&&"number"===typeof this.x2};return n}(u.seriesTypes.column.prototype.pointClass)});F(a,"Series/XRange/XRangeComposition.js",
[a["Core/Axis/Axis.js"],a["Core/Utilities.js"]],function(a,u){var t=u.addEvent,r=u.pick;t(a,"afterGetSeriesExtremes",function(){var a=this.series,h;if(this.isXAxis){var t=r(this.dataMax,-Number.MAX_VALUE);a.forEach(function(a){a.x2Data&&a.x2Data.forEach(function(a){a>t&&(t=a,h=!0)})});h&&(this.dataMax=t)}})});F(a,"Series/XRange/XRangeSeries.js",[a["Core/Globals.js"],a["Core/Color/Color.js"],a["Core/Series/SeriesRegistry.js"],a["Core/Utilities.js"],a["Series/XRange/XRangePoint.js"]],function(a,u,t,
r,n){var h=this&&this.__extends||function(){var c=function(d,b){c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(b,d){b.__proto__=d}||function(b,d){for(var c in d)d.hasOwnProperty(c)&&(b[c]=d[c])};return c(d,b)};return function(d,b){function J(){this.constructor=d}c(d,b);d.prototype=null===b?Object.create(b):(J.prototype=b.prototype,new J)}}(),A=u.parse,B=t.series,D=t.seriesTypes.column,p=D.prototype,w=r.clamp,k=r.correctFloat,f=r.defined;u=r.extend;var v=r.find,e=r.isNumber,g=r.isObject,
m=r.merge,q=r.pick;r=function(c){function d(){var b=null!==c&&c.apply(this,arguments)||this;b.data=void 0;b.options=void 0;b.points=void 0;return b}h(d,c);d.prototype.init=function(){D.prototype.init.apply(this,arguments);this.options.stacking=void 0};d.prototype.getColumnMetrics=function(){function b(){d.series.forEach(function(b){var d=b.xAxis;b.xAxis=b.yAxis;b.yAxis=d})}var d=this.chart;b();var c=p.getColumnMetrics.call(this);b();return c};d.prototype.cropData=function(b,d,c,g){d=B.prototype.cropData.call(this,
this.x2Data,d,c,g);d.xData=b.slice(d.start,d.end);return d};d.prototype.findPointIndex=function(b){var d=this.cropped,c=this.cropStart,g=this.points,q=b.id;if(q)var m=(m=v(g,function(b){return b.id===q}))?m.index:void 0;"undefined"===typeof m&&(m=(m=v(g,function(d){return d.x===b.x&&d.x2===b.x2&&!d.touched}))?m.index:void 0);d&&e(m)&&e(c)&&m>=c&&(m-=c);return m};d.prototype.translatePoint=function(b){var d,c,p=this.xAxis,v=this.yAxis,k=this.columnMetrics,C=this.options,a=C.minPointLength||0,E=(null===
(d=b.shapeArgs)||void 0===d?NaN:d.width)/2,y=this.pointXOffset=k.offset;d=b.plotX;var z=q(b.x2,b.x+(b.len||0)),l=p.translate(z,0,0,0,1);z=Math.abs(l-d);var x=this.chart.inverted,G=q(C.borderWidth,1)%2/2,O=k.offset,N=Math.round(k.width);a&&(a-=z,0>a&&(a=0),d-=a/2,l+=a/2);d=Math.max(d,-10);l=w(l,-10,p.len+10);f(b.options.pointWidth)&&(O-=(Math.ceil(b.options.pointWidth)-N)/2,N=Math.ceil(b.options.pointWidth));C.pointPlacement&&e(b.plotY)&&v.categories&&(b.plotY=v.translate(b.y,0,1,0,1,C.pointPlacement));
b.shapeArgs={x:Math.floor(Math.min(d,l))+G,y:Math.floor(b.plotY+O)+G,width:Math.round(Math.abs(l-d)),height:N,r:this.options.borderRadius};x?b.tooltipPos[1]+=y+E:b.tooltipPos[0]-=E+y-(null===(c=b.shapeArgs)||void 0===c?NaN:c.width)/2;c=b.shapeArgs.x;C=c+b.shapeArgs.width;0>c||C>p.len?(c=w(c,0,p.len),C=w(C,0,p.len),E=C-c,b.dlBox=m(b.shapeArgs,{x:c,width:C-c,centerX:E?E/2:null})):b.dlBox=null;c=b.tooltipPos;C=x?1:0;E=x?0:1;k=this.columnMetrics?this.columnMetrics.offset:-k.width/2;c[C]=x?c[C]+b.shapeArgs.width/
2:c[C]+(p.reversed?-1:0)*b.shapeArgs.width;c[E]=w(c[E]+(x?-1:1)*k,0,v.len-1);if(k=b.partialFill)g(k)&&(k=k.amount),e(k)||(k=0),v=b.shapeArgs,b.partShapeArgs={x:v.x,y:v.y,width:v.width,height:v.height,r:this.options.borderRadius},d=Math.max(Math.round(z*k+b.plotX-d),0),b.clipRectArgs={x:p.reversed?v.x+z-d:v.x,y:v.y,width:d,height:v.height}};d.prototype.translate=function(){p.translate.apply(this,arguments);this.points.forEach(function(b){this.translatePoint(b)},this)};d.prototype.drawPoint=function(b,
d){var c=this.options,J=this.chart.renderer,e=b.graphic,f=b.shapeType,p=b.shapeArgs,v=b.partShapeArgs,E=b.clipRectArgs,y=b.partialFill,z=c.stacking&&!c.borderRadius,l=b.state,x=c.states[l||"normal"]||{},G="undefined"===typeof l?"attr":d;l=this.pointAttribs(b,l);x=q(this.chart.options.chart.animation,x.animation);if(b.isNull||!1===b.visible)e&&(b.graphic=e.destroy());else{if(e)e.rect[d](p);else b.graphic=e=J.g("point").addClass(b.getClassName()).add(b.group||this.group),e.rect=J[f](m(p)).addClass(b.getClassName()).addClass("highcharts-partfill-original").add(e);
v&&(e.partRect?(e.partRect[d](m(v)),e.partialClipRect[d](m(E))):(e.partialClipRect=J.clipRect(E.x,E.y,E.width,E.height),e.partRect=J[f](v).addClass("highcharts-partfill-overlay").add(e).clip(e.partialClipRect)));this.chart.styledMode||(e.rect[d](l,x).shadow(c.shadow,null,z),v&&(g(y)||(y={}),g(c.partialFill)&&(y=m(c.partialFill,y)),b=y.fill||A(l.fill).brighten(-.3).get()||A(b.color||this.color).brighten(-.3).get(),l.fill=b,e.partRect[G](l,x).shadow(c.shadow,null,z)))}};d.prototype.drawPoints=function(){var b=
this,d=b.getAnimationVerb();b.points.forEach(function(c){b.drawPoint(c,d)})};d.prototype.getAnimationVerb=function(){return this.chart.pointCount<(this.options.animationLimit||250)?"animate":"attr"};d.prototype.isPointInside=function(b){var d=b.shapeArgs,g=b.plotX,q=b.plotY;return d?"undefined"!==typeof g&&"undefined"!==typeof q&&0<=q&&q<=this.yAxis.len&&0<=d.x+d.width&&g<=this.xAxis.len:c.prototype.isPointInside.apply(this,arguments)};d.defaultOptions=m(D.defaultOptions,{colorByPoint:!0,dataLabels:{formatter:function(){var b=
this.point.partialFill;g(b)&&(b=b.amount);if(e(b)&&0<b)return k(100*b)+"%"},inside:!0,verticalAlign:"middle"},tooltip:{headerFormat:'<span style="font-size: 10px">{point.x} - {point.x2}</span><br/>',pointFormat:'<span style="color:{point.color}">\u25cf</span> {series.name}: <b>{point.yCategory}</b><br/>'},borderRadius:3,pointRange:0});return d}(D);u(r.prototype,{type:"xrange",parallelArrays:["x","x2","y"],requireSorting:!1,animate:B.prototype.animate,cropShoulder:1,getExtremesFromAll:!0,autoIncrement:a.noop,
buildKDTree:a.noop,pointClass:n});t.registerSeriesType("xrange",r);"";return r});F(a,"Series/Gantt/GanttPoint.js",[a["Core/Series/SeriesRegistry.js"],a["Core/Utilities.js"]],function(a,u){var t=this&&this.__extends||function(){var a=function(h,n){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,h){a.__proto__=h}||function(a,h){for(var p in h)h.hasOwnProperty(p)&&(a[p]=h[p])};return a(h,n)};return function(h,n){function r(){this.constructor=h}a(h,n);h.prototype=null===n?Object.create(n):
(r.prototype=n.prototype,new r)}}(),r=u.pick;return function(a){function h(){var h=null!==a&&a.apply(this,arguments)||this;h.options=void 0;h.series=void 0;return h}t(h,a);h.setGanttPointAliases=function(a){function h(h,p){"undefined"!==typeof p&&(a[h]=p)}h("x",r(a.start,a.x));h("x2",r(a.end,a.x2));h("partialFill",r(a.completed,a.partialFill))};h.prototype.applyOptions=function(n,r){n=a.prototype.applyOptions.call(this,n,r);h.setGanttPointAliases(n);return n};h.prototype.isValid=function(){return("number"===
typeof this.start||"number"===typeof this.x)&&("number"===typeof this.end||"number"===typeof this.x2||this.milestone)};return h}(a.seriesTypes.xrange.prototype.pointClass)});F(a,"Gantt/Tree.js",[a["Core/Utilities.js"]],function(a){var u=a.extend,t=a.isNumber,r=a.pick,n=function(a,h){var n=a.reduce(function(p,a){var k=r(a.parent,"");"undefined"===typeof p[k]&&(p[k]=[]);p[k].push(a);return p},{});Object.keys(n).forEach(function(p,a){var k=n[p];""!==p&&-1===h.indexOf(p)&&(k.forEach(function(f){a[""].push(f)}),
delete a[p])});return n},h=function(a,n,D,p,w,k){var f=0,v=0,e=k&&k.after,g=k&&k.before;n={data:p,depth:D-1,id:a,level:D,parent:n};var m,q;"function"===typeof g&&g(n,k);g=(w[a]||[]).map(function(c){var d=h(c.id,a,D+1,c,w,k),b=c.start;c=!0===c.milestone?b:c.end;m=!t(m)||b<m?b:m;q=!t(q)||c>q?c:q;f=f+1+d.descendants;v=Math.max(d.height+1,v);return d});p&&(p.start=r(p.start,m),p.end=r(p.end,q));u(n,{children:g,descendants:f,height:v});"function"===typeof e&&e(n,k);return n};return{getListOfParents:n,
getNode:h,getTree:function(a,r){var D=a.map(function(a){return a.id});a=n(a,D);return h("",null,1,null,a,r)}}});F(a,"Core/Axis/TreeGridTick.js",[a["Core/Color/Palette.js"],a["Core/Utilities.js"]],function(a,u){var t=u.addEvent,r=u.isObject,n=u.isNumber,h=u.pick,A=u.wrap,B;(function(D){function p(){this.treeGrid||(this.treeGrid=new e(this))}function w(g,e){g=g.treeGrid;var q=!g.labelIcon,c=e.renderer,d=e.xy,b=e.options,J=b.width,m=b.height,f=d.x-J/2-b.padding;d=d.y-m/2;var v=e.collapsed?90:180,p=e.show&&
n(d),k=g.labelIcon;k||(g.labelIcon=k=c.path(c.symbols[b.type](b.x,b.y,J,m)).addClass("highcharts-label-icon").add(e.group));p||k.attr({y:-9999});c.styledMode||k.attr({"stroke-width":1,fill:h(e.color,a.neutralColor60)}).css({cursor:"pointer",stroke:b.lineColor,strokeWidth:b.lineWidth});k[q?"attr":"animate"]({translateX:f,translateY:d,rotation:v})}function k(g,e,q,c,d,b,J,f,a){var m=h(this.options&&this.options.labels,b);b=this.pos;var I=this.axis,v="treegrid"===I.options.type;g=g.apply(this,[e,q,c,
d,m,J,f,a]);v&&(e=m&&r(m.symbol,!0)?m.symbol:{},m=m&&n(m.indentation)?m.indentation:0,b=(b=(I=I.treeGrid.mapOfPosToGridNode)&&I[b])&&b.depth||1,g.x+=e.width+2*e.padding+(b-1)*m);return g}function f(g){var e=this,q=e.pos,c=e.axis,d=e.label,b=c.treeGrid.mapOfPosToGridNode,J=c.options,f=h(e.options&&e.options.labels,J&&J.labels),a=f&&r(f.symbol,!0)?f.symbol:{},v=(b=b&&b[q])&&b.depth;J="treegrid"===J.type;var p=-1<c.tickPositions.indexOf(q);q=c.chart.styledMode;J&&b&&d&&d.element&&d.addClass("highcharts-treegrid-node-level-"+
v);g.apply(e,Array.prototype.slice.call(arguments,1));J&&d&&d.element&&b&&b.descendants&&0<b.descendants&&(c=c.treeGrid.isCollapsed(b),w(e,{color:!q&&d.styles&&d.styles.color||"",collapsed:c,group:d.parentGroup,options:a,renderer:d.renderer,show:p,xy:d.xy}),a="highcharts-treegrid-node-"+(c?"expanded":"collapsed"),d.addClass("highcharts-treegrid-node-"+(c?"collapsed":"expanded")).removeClass(a),q||d.css({cursor:"pointer"}),[d,e.treeGrid.labelIcon].forEach(function(b){b&&!b.attachedTreeGridEvents&&
(t(b.element,"mouseover",function(){d.addClass("highcharts-treegrid-node-active");d.renderer.styledMode||d.css({textDecoration:"underline"})}),t(b.element,"mouseout",function(){var b=r(f.style)?f.style:{};d.removeClass("highcharts-treegrid-node-active");d.renderer.styledMode||d.css({textDecoration:b.textDecoration})}),t(b.element,"click",function(){e.treeGrid.toggleCollapse()}),b.attachedTreeGridEvents=!0)}))}var v=!1;D.compose=function(e){v||(t(e,"init",p),A(e.prototype,"getLabelPosition",k),A(e.prototype,
"renderLabel",f),e.prototype.collapse=function(e){this.treeGrid.collapse(e)},e.prototype.expand=function(e){this.treeGrid.expand(e)},e.prototype.toggleCollapse=function(e){this.treeGrid.toggleCollapse(e)},v=!0)};var e=function(){function e(e){this.tick=e}e.prototype.collapse=function(e){var g=this.tick,c=g.axis,d=c.brokenAxis;d&&c.treeGrid.mapOfPosToGridNode&&(g=c.treeGrid.collapse(c.treeGrid.mapOfPosToGridNode[g.pos]),d.setBreaks(g,h(e,!0)))};e.prototype.expand=function(e){var g=this.tick,c=g.axis,
d=c.brokenAxis;d&&c.treeGrid.mapOfPosToGridNode&&(g=c.treeGrid.expand(c.treeGrid.mapOfPosToGridNode[g.pos]),d.setBreaks(g,h(e,!0)))};e.prototype.toggleCollapse=function(e){var g=this.tick,c=g.axis,d=c.brokenAxis;d&&c.treeGrid.mapOfPosToGridNode&&(g=c.treeGrid.toggleCollapse(c.treeGrid.mapOfPosToGridNode[g.pos]),d.setBreaks(g,h(e,!0)))};return e}();D.Additions=e})(B||(B={}));return B});F(a,"Mixins/TreeSeries.js",[a["Core/Color/Color.js"],a["Core/Utilities.js"]],function(a,u){var t=u.extend,r=u.isArray,
n=u.isNumber,h=u.isObject,A=u.merge,B=u.pick;return{getColor:function(h,p){var w=p.index,k=p.mapOptionsToLevel,f=p.parentColor,v=p.parentColorIndex,e=p.series,g=p.colors,m=p.siblings,q=e.points,c=e.chart.options.chart,d;if(h){q=q[h.i];h=k[h.level]||{};if(k=q&&h.colorByPoint){var b=q.index%(g?g.length:c.colorCount);var J=g&&g[b]}if(!e.chart.styledMode){g=q&&q.options.color;c=h&&h.color;if(d=f)d=(d=h&&h.colorVariation)&&"brightness"===d.key?a.parse(f).brighten(w/m*d.to).get():f;d=B(g,c,J,d,e.color)}var I=
B(q&&q.options.colorIndex,h&&h.colorIndex,b,v,p.colorIndex)}return{color:d,colorIndex:I}},getLevelOptions:function(a){var p=null;if(h(a)){p={};var w=n(a.from)?a.from:1;var k=a.levels;var f={};var v=h(a.defaults)?a.defaults:{};r(k)&&(f=k.reduce(function(e,g){if(h(g)&&n(g.level)){var a=A({},g);var q="boolean"===typeof a.levelIsConstant?a.levelIsConstant:v.levelIsConstant;delete a.levelIsConstant;delete a.level;g=g.level+(q?0:w-1);h(e[g])?t(e[g],a):e[g]=a}return e},{}));k=n(a.to)?a.to:1;for(a=0;a<=k;a++)p[a]=
A({},v,h(f[a])?f[a]:{})}return p},setTreeValues:function k(a,w){var f=w.before,v=w.idRoot,e=w.mapIdToNode[v],g=w.points[a.i],m=g&&g.options||{},q=0,c=[];t(a,{levelDynamic:a.level-(("boolean"===typeof w.levelIsConstant?w.levelIsConstant:1)?0:e.level),name:B(g&&g.name,""),visible:v===a.id||("boolean"===typeof w.visible?w.visible:!1)});"function"===typeof f&&(a=f(a,w));a.children.forEach(function(d,b){var e=t({},w);t(e,{index:b,siblings:a.children.length,visible:a.visible});d=k(d,e);c.push(d);d.visible&&
(q+=d.val)});a.visible=0<q||a.visible;f=B(m.value,q);t(a,{children:c,childrenTotal:q,isLeaf:a.visible&&!q,val:f});return a},updateRootId:function(a){if(h(a)){var w=h(a.options)?a.options:{};w=B(a.rootNode,w.rootId,"");h(a.userOptions)&&(a.userOptions.rootId=w);a.rootNode=w}return w}}});F(a,"Core/Axis/GridAxis.js",[a["Core/Axis/Axis.js"],a["Core/Globals.js"],a["Core/Axis/Tick.js"],a["Core/Utilities.js"]],function(a,u,t,r){var n=r.addEvent,h=r.defined,A=r.erase,B=r.find,D=r.isArray,p=r.isNumber,w=r.merge,
k=r.pick,f=r.timeUnits,v=r.wrap,e=u.Chart,g=function(c){var d=c.options;d.labels||(d.labels={});d.labels.align=k(d.labels.align,"center");c.categories||(d.showLastLabel=!1);c.labelRotation=0;d.labels.rotation=0};"";a.prototype.getMaxLabelDimensions=function(c,d){var b={width:0,height:0};d.forEach(function(d){d=c[d];if(r.isObject(d,!0)){var e=r.isObject(d.label,!0)?d.label:{};d=e.getBBox?e.getBBox().height:0;e.textStr&&!p(e.textPxLength)&&(e.textPxLength=e.getBBox().width);var a=p(e.textPxLength)?
Math.round(e.textPxLength):0;e.textStr&&(a=Math.round(e.getBBox().width));b.height=Math.max(d,b.height);b.width=Math.max(a,b.width)}});return b};u.dateFormats.W=function(c){c=new this.Date(c);var d=(this.get("Day",c)+6)%7,b=new this.Date(c.valueOf());this.set("Date",b,this.get("Date",c)-d+3);d=new this.Date(this.get("FullYear",b),0,1);4!==this.get("Day",d)&&(this.set("Month",c,0),this.set("Date",c,1+(11-this.get("Day",d))%7));return(1+Math.floor((b.valueOf()-d.valueOf())/6048E5)).toString()};u.dateFormats.E=
function(c){return this.dateFormat("%a",c,!0).charAt(0)};n(e,"afterSetChartSize",function(){this.axes.forEach(function(c){(c.grid&&c.grid.columns||[]).forEach(function(d){d.setAxisSize();d.setAxisTranslation()})})});n(t,"afterGetLabelPosition",function(c){var d=this.label,b=this.axis,e=b.reversed,a=b.chart,g=b.options.grid||{},f=b.options.labels,v=f.align,m=q.Side[b.side],k=c.tickmarkOffset,E=b.tickPositions,y=this.pos-k;E=p(E[c.index+1])?E[c.index+1]-k:b.max+k;var z=b.tickSize("tick");k=z?z[0]:0;
z=z?z[1]/2:0;if(!0===g.enabled){if("top"===m){g=b.top+b.offset;var l=g-k}else"bottom"===m?(l=a.chartHeight-b.bottom+b.offset,g=l+k):(g=b.top+b.len-b.translate(e?E:y),l=b.top+b.len-b.translate(e?y:E));"right"===m?(m=a.chartWidth-b.right+b.offset,e=m+k):"left"===m?(e=b.left+b.offset,m=e-k):(m=Math.round(b.left+b.translate(e?E:y))-z,e=Math.round(b.left+b.translate(e?y:E))-z);this.slotWidth=e-m;c.pos.x="left"===v?m:"right"===v?e:m+(e-m)/2;c.pos.y=l+(g-l)/2;a=a.renderer.fontMetrics(f.style.fontSize,d.element);
d=d.getBBox().height;f.useHTML?c.pos.y+=a.b+-(d/2):(d=Math.round(d/a.h),c.pos.y+=(a.b-(a.h-a.f))/2+-((d-1)*a.h/2));c.pos.x+=b.horiz&&f.x||0}});var m=function(){function c(d){this.axis=d}c.prototype.isOuterAxis=function(){var d=this.axis,b=d.grid.columnIndex,c=d.linkedParent&&d.linkedParent.grid.columns||d.grid.columns,e=b?d.linkedParent:d,a=-1,g=0;d.chart[d.coll].forEach(function(b,c){b.side!==d.side||b.options.isInternal||(g=c,b===e&&(a=c))});return g===a&&(p(b)?c.length===b:!0)};c.prototype.renderBorder=
function(d){var b=this.axis,c=b.chart.renderer,e=b.options;d=c.path(d).addClass("highcharts-axis-line").add(b.axisBorder);c.styledMode||d.attr({stroke:e.lineColor,"stroke-width":e.lineWidth,zIndex:7});return d};return c}(),q=function(){function c(){}c.compose=function(d){a.keepProps.push("grid");v(d.prototype,"unsquish",c.wrapUnsquish);n(d,"init",c.onInit);n(d,"afterGetOffset",c.onAfterGetOffset);n(d,"afterGetTitlePosition",c.onAfterGetTitlePosition);n(d,"afterInit",c.onAfterInit);n(d,"afterRender",
c.onAfterRender);n(d,"afterSetAxisTranslation",c.onAfterSetAxisTranslation);n(d,"afterSetOptions",c.onAfterSetOptions);n(d,"afterSetOptions",c.onAfterSetOptions2);n(d,"afterSetScale",c.onAfterSetScale);n(d,"afterTickSize",c.onAfterTickSize);n(d,"trimTicks",c.onTrimTicks);n(d,"destroy",c.onDestroy)};c.onAfterGetOffset=function(){var d=this.grid;(d&&d.columns||[]).forEach(function(b){b.getOffset()})};c.onAfterGetTitlePosition=function(d){if(!0===(this.options.grid||{}).enabled){var b=this.axisTitle,
e=this.height,a=this.horiz,g=this.left,f=this.offset,q=this.opposite,m=this.options.title,v=void 0===m?{}:m;m=this.top;var E=this.width,y=this.tickSize(),z=b&&b.getBBox().width,l=v.x||0,x=v.y||0,G=k(v.margin,a?5:10);b=this.chart.renderer.fontMetrics(v.style&&v.style.fontSize,b).f;y=(a?m+e:g)+(a?1:-1)*(q?-1:1)*(y?y[0]/2:0)+(this.side===c.Side.bottom?b:0);d.titlePosition.x=a?g-z/2-G+l:y+(q?E:0)+f+l;d.titlePosition.y=a?y-(q?e:0)+(q?b:-b)/2+f+x:m-G+x}};c.onAfterInit=function(){var d=this.chart,b=this.options.grid;
b=void 0===b?{}:b;var c=this.userOptions;b.enabled&&(g(this),v(this,"labelFormatter",function(b){var d=this.axis,c=this.value,e=d.tickPositions,y=(d.isLinked?d.linkedParent:d).series[0],z=c===e[0];e=c===e[e.length-1];var l=y&&B(y.options.data,function(l){return l[d.isXAxis?"x":"y"]===c});if(l&&y.is("gantt")){var x=w(l);u.seriesTypes.gantt.prototype.pointClass.setGanttPointAliases(x)}this.isFirst=z;this.isLast=e;this.point=x;return b.call(this)}));if(b.columns)for(var e=this.grid.columns=[],q=this.grid.columnIndex=
0;++q<b.columns.length;){var f=w(c,b.columns[b.columns.length-q-1],{linkedTo:0,type:"category",scrollbar:{enabled:!1}});delete f.grid.columns;f=new a(this.chart,f);f.grid.isColumn=!0;f.grid.columnIndex=q;A(d.axes,f);A(d[this.coll],f);e.push(f)}};c.onAfterRender=function(){var d,b=this.grid,e=this.options;if(!0===(e.grid||{}).enabled){this.maxLabelDimensions=this.getMaxLabelDimensions(this.ticks,this.tickPositions);this.rightWall&&this.rightWall.destroy();if(this.grid&&this.grid.isOuterAxis()&&this.axisLine&&
(e=e.lineWidth)){e=this.getLinePath(e);var a=e[0],g=e[1],f=((this.tickSize("tick")||[1])[0]-1)*(this.side===c.Side.top||this.side===c.Side.left?-1:1);"M"===a[0]&&"L"===g[0]&&(this.horiz?(a[2]+=f,g[2]+=f):(a[1]+=f,g[1]+=f));!this.horiz&&this.chart.marginRight&&(a=[a,["L",this.left,a[2]]],f=["L",this.chart.chartWidth-this.chart.marginRight,this.toPixels(this.max+this.tickmarkOffset)],g=[["M",g[1],this.toPixels(this.max+this.tickmarkOffset)],f],this.grid.upperBorder||0===this.min%1||(this.grid.upperBorder=
this.grid.renderBorder(a)),this.grid.upperBorder&&this.grid.upperBorder.animate({d:a}),this.grid.lowerBorder||0===this.max%1||(this.grid.lowerBorder=this.grid.renderBorder(g)),this.grid.lowerBorder&&this.grid.lowerBorder.animate({d:g}));this.grid.axisLineExtra?this.grid.axisLineExtra.animate({d:e}):this.grid.axisLineExtra=this.grid.renderBorder(e);this.axisLine[this.showAxis?"show":"hide"](!0)}(b&&b.columns||[]).forEach(function(b){b.render()});!this.horiz&&this.chart.hasRendered&&(this.scrollbar||
(null===(d=this.linkedParent)||void 0===d?0:d.scrollbar))&&(d=this.max,b=this.tickmarkOffset,e=this.tickPositions[this.tickPositions.length-1],g=this.tickPositions[0],this.min-g>b?this.ticks[g].label.hide():this.ticks[g].label.show(),e-d>b?this.ticks[e].label.hide():this.ticks[e].label.show(),e-d<b&&0<e-d&&this.ticks[e].isLast?this.ticks[e].mark.hide():this.ticks[e-1]&&this.ticks[e-1].mark.show())}};c.onAfterSetAxisTranslation=function(){var d,b=this.tickPositions&&this.tickPositions.info,c=this.options,
e=this.userOptions.labels||{};(c.grid||{}).enabled&&(this.horiz?(this.series.forEach(function(b){b.options.pointRange=0}),b&&c.dateTimeLabelFormats&&c.labels&&!h(e.align)&&(!1===c.dateTimeLabelFormats[b.unitName].range||1<b.count)&&(c.labels.align="left",h(e.x)||(c.labels.x=3))):"treegrid"!==this.options.type&&(null===(d=this.grid)||void 0===d?0:d.columns)&&(this.minPointOffset=this.tickInterval))};c.onAfterSetOptions=function(d){var b=this.options;d=d.userOptions;var c=b&&r.isObject(b.grid,!0)?b.grid:
{};if(!0===c.enabled){var e=w(!0,{className:"highcharts-grid-axis "+(d.className||""),dateTimeLabelFormats:{hour:{list:["%H:%M","%H"]},day:{list:["%A, %e. %B","%a, %e. %b","%E"]},week:{list:["Week %W","W%W"]},month:{list:["%B","%b","%o"]}},grid:{borderWidth:1},labels:{padding:2,style:{fontSize:"13px"}},margin:0,title:{text:null,reserveSpace:!1,rotation:0},units:[["millisecond",[1,10,100]],["second",[1,10]],["minute",[1,5,15]],["hour",[1,6]],["day",[1]],["week",[1]],["month",[1]],["year",null]]},d);
"xAxis"===this.coll&&(h(d.linkedTo)&&!h(d.tickPixelInterval)&&(e.tickPixelInterval=350),h(d.tickPixelInterval)||!h(d.linkedTo)||h(d.tickPositioner)||h(d.tickInterval)||(e.tickPositioner=function(b,d){var c=this.linkedParent&&this.linkedParent.tickPositions&&this.linkedParent.tickPositions.info;if(c){var a,g=e.units;for(a=0;a<g.length;a++)if(g[a][0]===c.unitName){var q=a;break}if(g[q+1]){var y=g[q+1][0];var z=(g[q+1][1]||[1])[0]}else"year"===c.unitName&&(y="year",z=10*c.count);c=f[y];this.tickInterval=
c*z;return this.getTimeTicks({unitRange:c,count:z,unitName:y},b,d,this.options.startOfWeek)}}));w(!0,this.options,e);this.horiz&&(b.minPadding=k(d.minPadding,0),b.maxPadding=k(d.maxPadding,0));p(b.grid.borderWidth)&&(b.tickWidth=b.lineWidth=c.borderWidth)}};c.onAfterSetOptions2=function(d){d=(d=d.userOptions)&&d.grid||{};var b=d.columns;d.enabled&&b&&w(!0,this.options,b[b.length-1])};c.onAfterSetScale=function(){(this.grid.columns||[]).forEach(function(d){d.setScale()})};c.onAfterTickSize=function(d){var b=
a.defaultLeftAxisOptions,c=this.horiz,e=this.maxLabelDimensions,g=this.options.grid;g=void 0===g?{}:g;g.enabled&&e&&(b=2*Math.abs(b.labels.x),c=c?g.cellHeight||b+e.height:b+e.width,D(d.tickSize)?d.tickSize[0]=c:d.tickSize=[c,0])};c.onDestroy=function(d){var b=this.grid;(b.columns||[]).forEach(function(b){b.destroy(d.keepEvents)});b.columns=void 0};c.onInit=function(d){d=d.userOptions||{};var b=d.grid||{};b.enabled&&h(b.borderColor)&&(d.tickColor=d.lineColor=b.borderColor);this.grid||(this.grid=new m(this))};
c.onTrimTicks=function(){var d=this.options,b=this.categories,c=this.tickPositions,e=c[0],a=c[c.length-1],g=this.linkedParent&&this.linkedParent.min||this.min,f=this.linkedParent&&this.linkedParent.max||this.max,q=this.tickInterval;!0!==(d.grid||{}).enabled||b||!this.horiz&&!this.isLinked||(e<g&&e+q>g&&!d.startOnTick&&(c[0]=g),a>f&&a-q<f&&!d.endOnTick&&(c[c.length-1]=f))};c.wrapUnsquish=function(d){var b=this.options.grid;return!0===(void 0===b?{}:b).enabled&&this.categories?this.tickInterval:d.apply(this,
Array.prototype.slice.call(arguments,1))};return c}();(function(c){c=c.Side||(c.Side={});c[c.top=0]="top";c[c.right=1]="right";c[c.bottom=2]="bottom";c[c.left=3]="left"})(q||(q={}));q.compose(a);return q});F(a,"Core/Axis/BrokenAxis.js",[a["Core/Axis/Axis.js"],a["Core/Series/Series.js"],a["Extensions/Stacking.js"],a["Core/Utilities.js"]],function(a,u,t,r){var n=r.addEvent,h=r.find,A=r.fireEvent,B=r.isArray,D=r.isNumber,p=r.pick,w=function(){function k(a){this.hasBreaks=!1;this.axis=a}k.isInBreak=function(a,
v){var e=a.repeat||Infinity,g=a.from,f=a.to-a.from;v=v>=g?(v-g)%e:e-(g-v)%e;return a.inclusive?v<=f:v<f&&0!==v};k.lin2Val=function(a){var f=this.brokenAxis;f=f&&f.breakArray;if(!f)return a;var e;for(e=0;e<f.length;e++){var g=f[e];if(g.from>=a)break;else g.to<a?a+=g.len:k.isInBreak(g,a)&&(a+=g.len)}return a};k.val2Lin=function(a){var f=this.brokenAxis;f=f&&f.breakArray;if(!f)return a;var e=a,g;for(g=0;g<f.length;g++){var m=f[g];if(m.to<=a)e-=m.len;else if(m.from>=a)break;else if(k.isInBreak(m,a)){e-=
a-m.from;break}}return e};k.prototype.findBreakAt=function(a,k){return h(k,function(e){return e.from<a&&a<e.to})};k.prototype.isInAnyBreak=function(a,v){var e=this.axis,g=e.options.breaks,f=g&&g.length,q;if(f){for(;f--;)if(k.isInBreak(g[f],a)){var c=!0;q||(q=p(g[f].showPoints,!e.isXAxis))}var d=c&&v?c&&!q:c}return d};k.prototype.setBreaks=function(f,v){var e=this,g=e.axis,m=B(f)&&!!f.length;g.isDirty=e.hasBreaks!==m;e.hasBreaks=m;g.options.breaks=g.userOptions.breaks=f;g.forceRedraw=!0;g.series.forEach(function(e){e.isDirty=
!0});m||g.val2lin!==k.val2Lin||(delete g.val2lin,delete g.lin2val);m&&(g.userOptions.ordinal=!1,g.lin2val=k.lin2Val,g.val2lin=k.val2Lin,g.setExtremes=function(g,c,d,b,f){if(e.hasBreaks){for(var q,m=this.options.breaks;q=e.findBreakAt(g,m);)g=q.to;for(;q=e.findBreakAt(c,m);)c=q.from;c<g&&(c=g)}a.prototype.setExtremes.call(this,g,c,d,b,f)},g.setAxisTranslation=function(){a.prototype.setAxisTranslation.call(this);e.unitLength=null;if(e.hasBreaks){var f=g.options.breaks||[],c=[],d=[],b=0,m,v=g.userMin||
g.min,w=g.userMax||g.max,h=p(g.pointRangePadding,0),n;f.forEach(function(b){m=b.repeat||Infinity;k.isInBreak(b,v)&&(v+=b.to%m-v%m);k.isInBreak(b,w)&&(w-=w%m-b.from%m)});f.forEach(function(b){t=b.from;for(m=b.repeat||Infinity;t-m>v;)t-=m;for(;t<v;)t+=m;for(n=t;n<w;n+=m)c.push({value:n,move:"in"}),c.push({value:n+(b.to-b.from),move:"out",size:b.breakSize})});c.sort(function(b,d){return b.value===d.value?("in"===b.move?0:1)-("in"===d.move?0:1):b.value-d.value});var r=0;var t=v;c.forEach(function(c){r+=
"in"===c.move?1:-1;1===r&&"in"===c.move&&(t=c.value);0===r&&(d.push({from:t,to:c.value,len:c.value-t-(c.size||0)}),b+=c.value-t-(c.size||0))});g.breakArray=e.breakArray=d;e.unitLength=w-v-b+h;A(g,"afterBreaks");g.staticScale?g.transA=g.staticScale:e.unitLength&&(g.transA*=(w-g.min+h)/e.unitLength);h&&(g.minPixelPadding=g.transA*g.minPointOffset);g.min=v;g.max=w}});p(v,!0)&&g.chart.redraw()};return k}();r=function(){function a(){}a.compose=function(a,v){a.keepProps.push("brokenAxis");var e=u.prototype;
e.drawBreaks=function(e,a){var g=this,c=g.points,d,b,f,m;if(e&&e.brokenAxis&&e.brokenAxis.hasBreaks){var v=e.brokenAxis;a.forEach(function(a){d=v&&v.breakArray||[];b=e.isXAxis?e.min:p(g.options.threshold,e.min);c.forEach(function(c){m=p(c["stack"+a.toUpperCase()],c[a]);d.forEach(function(d){if(D(b)&&D(m)){f=!1;if(b<d.from&&m>d.to||b>d.from&&m<d.from)f="pointBreak";else if(b<d.from&&m>d.from&&m<d.to||b>d.from&&m>d.to&&m<d.from)f="pointInBreak";f&&A(e,f,{point:c,brk:d})}})})})}};e.gappedPath=function(){var e=
this.currentDataGrouping,a=e&&e.gapSize;e=this.options.gapSize;var f=this.points.slice(),c=f.length-1,d=this.yAxis,b;if(e&&0<c)for("value"!==this.options.gapUnit&&(e*=this.basePointRange),a&&a>e&&a>=this.basePointRange&&(e=a),b=void 0;c--;)b&&!1!==b.visible||(b=f[c+1]),a=f[c],!1!==b.visible&&!1!==a.visible&&(b.x-a.x>e&&(b=(a.x+b.x)/2,f.splice(c+1,0,{isNull:!0,x:b}),d.stacking&&this.options.stacking&&(b=d.stacking.stacks[this.stackKey][b]=new t(d,d.options.stackLabels,!1,b,this.stack),b.total=0)),
b=a);return this.getGraphPath(f)};n(a,"init",function(){this.brokenAxis||(this.brokenAxis=new w(this))});n(a,"afterInit",function(){"undefined"!==typeof this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)});n(a,"afterSetTickPositions",function(){var e=this.brokenAxis;if(e&&e.hasBreaks){var a=this.tickPositions,f=this.tickPositions.info,c=[],d;for(d=0;d<a.length;d++)e.isInAnyBreak(a[d])||c.push(a[d]);this.tickPositions=c;this.tickPositions.info=f}});n(a,"afterSetOptions",function(){this.brokenAxis&&
this.brokenAxis.hasBreaks&&(this.options.ordinal=!1)});n(v,"afterGeneratePoints",function(){var e=this.options.connectNulls,a=this.points,f=this.xAxis,c=this.yAxis;if(this.isDirty)for(var d=a.length;d--;){var b=a[d],v=!(null===b.y&&!1===e)&&(f&&f.brokenAxis&&f.brokenAxis.isInAnyBreak(b.x,!0)||c&&c.brokenAxis&&c.brokenAxis.isInAnyBreak(b.y,!0));b.visible=v?!1:!1!==b.options.visible}});n(v,"afterRender",function(){this.drawBreaks(this.xAxis,["x"]);this.drawBreaks(this.yAxis,p(this.pointArrayMap,["y"]))})};
return a}();r.compose(a,u);return r});F(a,"Core/Axis/TreeGridAxis.js",[a["Core/Axis/Axis.js"],a["Core/Axis/Tick.js"],a["Gantt/Tree.js"],a["Core/Axis/TreeGridTick.js"],a["Mixins/TreeSeries.js"],a["Core/Utilities.js"]],function(a,u,t,r,n,h){var A=n.getLevelOptions,B=h.addEvent,D=h.find,p=h.fireEvent,w=h.isArray,k=h.isNumber,f=h.isObject,v=h.isString,e=h.merge,g=h.pick,m=h.wrap,q;(function(c){function d(b,d){var c=b.collapseStart||0;b=b.collapseEnd||0;b>=d&&(c-=.5);return{from:c,to:b,showPoints:!1}}
function b(b,d,c){var l=[],x=[],e={},a={},y=-1,z="boolean"===typeof d?d:!1;b=t.getTree(b,{after:function(l){l=a[l.pos];var b=0,d=0;l.children.forEach(function(l){d+=(l.descendants||0)+1;b=Math.max((l.height||0)+1,b)});l.descendants=d;l.height=b;l.collapsed&&x.push(l)},before:function(b){var x=f(b.data,!0)?b.data:{},d=v(x.name)?x.name:"",c=e[b.parent];c=f(c,!0)?a[c.pos]:null;var G=function(l){return l.name===d},g;z&&f(c,!0)&&(g=D(c.children,G))?(G=g.pos,g.nodes.push(b)):G=y++;a[G]||(a[G]=g={depth:c?
c.depth+1:0,name:d,id:x.id,nodes:[b],children:[],pos:G},-1!==G&&l.push(d),f(c,!0)&&c.children.push(g));v(b.id)&&(e[b.id]=b);g&&!0===x.collapsed&&(g.collapsed=!0);b.pos=G}});a=function(l,b){var x=function(l,d,c){var e=d+(-1===d?0:b-1),a=(e-d)/2,y=d+a;l.nodes.forEach(function(l){var b=l.data;f(b,!0)&&(b.y=d+(b.seriesIndex||0),delete b.seriesIndex);l.pos=y});c[y]=l;l.pos=y;l.tickmarkOffset=a+.5;l.collapseStart=e+.5;l.children.forEach(function(l){x(l,e+1,c);e=(l.collapseEnd||0)-.5});l.collapseEnd=e+.5;
return c};return x(l["-1"],-1,{})}(a,c);return{categories:l,mapOfIdToNode:e,mapOfPosToGridNode:a,collapsedNodes:x,tree:b}}function a(d){d.target.axes.filter(function(b){return"treegrid"===b.options.type}).forEach(function(c){var a=c.options||{},l=a.labels,x=a.uniqueNames,y=0;a=a.max;if(!c.treeGrid.mapOfPosToGridNode||c.series.some(function(l){return!l.hasRendered||l.isDirtyData||l.isDirty})){var g=c.series.reduce(function(l,b){b.visible&&((b.options.data||[]).forEach(function(c){b.options.keys&&b.options.keys.length&&
(c=b.pointClass.prototype.optionsToObject.call({series:b},c),b.pointClass.setGanttPointAliases(c));f(c,!0)&&(c.seriesIndex=y,l.push(c))}),!0===x&&y++);return l},[]);if(a&&g.length<a)for(var q=g.length;q<=a;q++)g.push({name:q+"\u200b"});a=b(g,x||!1,!0===x?y:1);c.categories=a.categories;c.treeGrid.mapOfPosToGridNode=a.mapOfPosToGridNode;c.hasNames=!0;c.treeGrid.tree=a.tree;c.series.forEach(function(l){var b=(l.options.data||[]).map(function(b){w(b)&&l.options.keys&&l.options.keys.length&&g.forEach(function(l){0<=
b.indexOf(l.x)&&0<=b.indexOf(l.x2)&&(b=l)});return f(b,!0)?e(b):b});l.visible&&l.setData(b,!1)});c.treeGrid.mapOptionsToLevel=A({defaults:l,from:1,levels:l&&l.levels,to:c.treeGrid.tree&&c.treeGrid.tree.height});"beforeRender"===d.type&&(c.treeGrid.collapsedNodes=a.collapsedNodes)}})}function q(b,c){var d=this.treeGrid.mapOptionsToLevel||{},l=this.ticks,x=l[c],a;if("treegrid"===this.options.type&&this.treeGrid.mapOfPosToGridNode){var e=this.treeGrid.mapOfPosToGridNode[c];(d=d[e.depth])&&(a={labels:d});
x?(x.parameters.category=e.name,x.options=a,x.addLabel()):l[c]=new u(this,c,void 0,void 0,{category:e.name,tickmarkOffset:e.tickmarkOffset,options:a})}else b.apply(this,Array.prototype.slice.call(arguments,1))}function h(b){var c=this.options;c=(c=c&&c.labels)&&k(c.indentation)?c.indentation:0;var d=b.apply(this,Array.prototype.slice.call(arguments,1));if("treegrid"===this.options.type&&this.treeGrid.mapOfPosToGridNode){var l=this.treeGrid.mapOfPosToGridNode[-1].height||0;d.width+=c*(l-1)}return d}
function n(c,d,z){var l=this,x="treegrid"===z.type;l.treeGrid||(l.treeGrid=new M(l));x&&(B(d,"beforeRender",a),B(d,"beforeRedraw",a),B(d,"addSeries",function(c){c.options.data&&(c=b(c.options.data,z.uniqueNames||!1,1),l.treeGrid.collapsedNodes=(l.treeGrid.collapsedNodes||[]).concat(c.collapsedNodes))}),B(l,"foundExtremes",function(){l.treeGrid.collapsedNodes&&l.treeGrid.collapsedNodes.forEach(function(b){var c=l.treeGrid.collapse(b);l.brokenAxis&&(l.brokenAxis.setBreaks(c,!1),l.treeGrid.collapsedNodes&&
(l.treeGrid.collapsedNodes=l.treeGrid.collapsedNodes.filter(function(l){return b.collapseStart!==l.collapseStart||b.collapseEnd!==l.collapseEnd})))})}),B(l,"afterBreaks",function(){var b;"yAxis"===l.coll&&!l.staticScale&&(null===(b=l.chart.options.chart)||void 0===b?0:b.height)&&(l.isDirty=!0)}),z=e({grid:{enabled:!0},labels:{align:"left",levels:[{level:void 0},{level:1,style:{fontWeight:"bold"}}],symbol:{type:"triangle",x:-5,y:-5,height:10,width:10,padding:5}},uniqueNames:!1},z,{reversed:!0,grid:{columns:void 0}}));
c.apply(l,[d,z]);x&&(l.hasNames=!0,l.options.showLastLabel=!0)}function H(b){var c=this.options;"treegrid"===c.type?(this.min=g(this.userMin,c.min,this.dataMin),this.max=g(this.userMax,c.max,this.dataMax),p(this,"foundExtremes"),this.setAxisTranslation(),this.tickmarkOffset=.5,this.tickInterval=1,this.tickPositions=this.treeGrid.mapOfPosToGridNode?this.treeGrid.getTickPositions():[]):b.apply(this,Array.prototype.slice.call(arguments,1))}var C=!1;c.compose=function(b){C||(m(b.prototype,"generateTick",
q),m(b.prototype,"getMaxLabelDimensions",h),m(b.prototype,"init",n),m(b.prototype,"setTickInterval",H),r.compose(u),C=!0)};var M=function(){function b(b){this.axis=b}b.prototype.setCollapsedStatus=function(b){var c=this.axis,l=c.chart;c.series.forEach(function(c){var d=c.options.data;if(b.id&&d){var x=l.get(b.id);c=d[c.data.indexOf(x)];x&&c&&(x.collapsed=b.collapsed,c.collapsed=b.collapsed)}})};b.prototype.collapse=function(b){var c=this.axis,l=c.options.breaks||[],x=d(b,c.max);l.push(x);b.collapsed=
!0;c.treeGrid.setCollapsedStatus(b);return l};b.prototype.expand=function(b){var c=this.axis,l=c.options.breaks||[],x=d(b,c.max);b.collapsed=!1;c.treeGrid.setCollapsedStatus(b);return l.reduce(function(l,b){b.to===x.to&&b.from===x.from||l.push(b);return l},[])};b.prototype.getTickPositions=function(){var b=this.axis,c=Math.floor(b.min/b.tickInterval)*b.tickInterval,l=Math.ceil(b.max/b.tickInterval)*b.tickInterval;return Object.keys(b.treeGrid.mapOfPosToGridNode||{}).reduce(function(d,a){a=+a;!(a>=
c&&a<=l)||b.brokenAxis&&b.brokenAxis.isInAnyBreak(a)||d.push(a);return d},[])};b.prototype.isCollapsed=function(b){var c=this.axis,l=c.options.breaks||[],x=d(b,c.max);return l.some(function(b){return b.from===x.from&&b.to===x.to})};b.prototype.toggleCollapse=function(b){return this.isCollapsed(b)?this.expand(b):this.collapse(b)};return b}();c.Additions=M})(q||(q={}));a.prototype.utils={getNode:t.getNode};q.compose(a);return q});F(a,"Extensions/CurrentDateIndication.js",[a["Core/Axis/Axis.js"],a["Core/Color/Palette.js"],
a["Core/Utilities.js"],a["Core/Axis/PlotLineOrBand.js"]],function(a,u,t,r){var n=t.addEvent,h=t.merge;t=t.wrap;var A={currentDateIndicator:!0,color:u.highlightColor20,width:2,label:{format:"%a, %b %d %Y, %H:%M",formatter:function(a,h){return this.axis.chart.time.dateFormat(h,a)},rotation:0,style:{fontSize:"10px"}}};n(a,"afterSetOptions",function(){var a=this.options,n=a.currentDateIndicator;n&&(n="object"===typeof n?h(A,n):h(A),n.value=new Date,a.plotLines||(a.plotLines=[]),a.plotLines.push(n))});
n(r,"render",function(){this.label&&this.label.attr({text:this.getLabelText(this.options.label)})});t(r.prototype,"getLabelText",function(a,h){var p=this.options;return p.currentDateIndicator&&p.label&&"function"===typeof p.label.formatter?(p.value=new Date,p.label.formatter.call(this,p.value,p.label.format)):a.call(this,h)})});F(a,"Extensions/StaticScale.js",[a["Core/Axis/Axis.js"],a["Core/Chart/Chart.js"],a["Core/Utilities.js"]],function(a,u,t){var r=t.addEvent,n=t.defined,h=t.isNumber,A=t.pick;
r(a,"afterSetOptions",function(){var a=this.chart.options&&this.chart.options.chart;!this.horiz&&h(this.options.staticScale)&&(!a.height||a.scrollablePlotArea&&a.scrollablePlotArea.minHeight)&&(this.staticScale=this.options.staticScale)});u.prototype.adjustHeight=function(){"adjustHeight"!==this.redrawTrigger&&((this.axes||[]).forEach(function(a){var h=a.chart,p=!!h.initiatedScale&&h.options.animation,w=a.options.staticScale;if(a.staticScale&&n(a.min)){var k=A(a.brokenAxis&&a.brokenAxis.unitLength,
a.max+a.tickInterval-a.min)*w;k=Math.max(k,w);w=k-h.plotHeight;1<=Math.abs(w)&&(h.plotHeight=k,h.redrawTrigger="adjustHeight",h.setSize(void 0,h.chartHeight+w,p));a.series.forEach(function(a){(a=a.sharedClipKey&&h[a.sharedClipKey])&&a.attr({height:h.plotHeight})})}}),this.initiatedScale=!0);this.redrawTrigger=null};r(u,"render",u.prototype.adjustHeight)});F(a,"Extensions/ArrowSymbols.js",[a["Core/Renderer/SVG/SVGRenderer.js"]],function(a){a.prototype.symbols.arrow=function(a,t,r,n){return[["M",a,
t+n/2],["L",a+r,t],["L",a,t+n/2],["L",a+r,t+n]]};a.prototype.symbols["arrow-half"]=function(u,t,r,n){return a.prototype.symbols.arrow(u,t,r/2,n)};a.prototype.symbols["triangle-left"]=function(a,t,r,n){return[["M",a+r,t],["L",a,t+n/2],["L",a+r,t+n],["Z"]]};a.prototype.symbols["arrow-filled"]=a.prototype.symbols["triangle-left"];a.prototype.symbols["triangle-left-half"]=function(u,t,r,n){return a.prototype.symbols["triangle-left"](u,t,r/2,n)};a.prototype.symbols["arrow-filled-half"]=a.prototype.symbols["triangle-left-half"]});
F(a,"Gantt/Connection.js",[a["Core/Globals.js"],a["Core/Options.js"],a["Core/Series/Point.js"],a["Core/Utilities.js"]],function(a,u,t,r){function n(a){var e=a.shapeArgs;return e?{xMin:e.x,xMax:e.x+e.width,yMin:e.y,yMax:e.y+e.height}:(e=a.graphic&&a.graphic.getBBox())?{xMin:a.plotX-e.width/2,xMax:a.plotX+e.width/2,yMin:a.plotY-e.height/2,yMax:a.plotY+e.height/2}:null}"";var h=r.defined,A=r.error,B=r.extend,D=r.merge,p=r.objectEach,w=a.deg2rad,k=Math.max,f=Math.min;B(u.defaultOptions,{connectors:{type:"straight",
lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}});u=function(){function a(a,g,f){this.toPoint=this.pathfinder=this.graphics=this.fromPoint=this.chart=void 0;this.init(a,g,f)}a.prototype.init=function(a,g,f){this.fromPoint=a;this.toPoint=g;this.options=f;this.chart=a.series.chart;this.pathfinder=this.chart.pathfinder};a.prototype.renderPath=function(a,g,f){var e=this.chart,c=e.styledMode,d=
e.pathfinder,b=!e.options.chart.forExport&&!1!==f,m=this.graphics&&this.graphics.path;d.group||(d.group=e.renderer.g().addClass("highcharts-pathfinder-group").attr({zIndex:-1}).add(e.seriesGroup));d.group.translate(e.plotLeft,e.plotTop);m&&m.renderer||(m=e.renderer.path().add(d.group),c||m.attr({opacity:0}));m.attr(g);a={d:a};c||(a.opacity=1);m[b?"animate":"attr"](a,f);this.graphics=this.graphics||{};this.graphics.path=m};a.prototype.addMarker=function(a,g,f){var e=this.fromPoint.series.chart,c=e.pathfinder;
e=e.renderer;var d="start"===a?this.fromPoint:this.toPoint,b=d.getPathfinderAnchorPoint(g);if(g.enabled&&((f="start"===a?f[1]:f[f.length-2])&&"M"===f[0]||"L"===f[0])){f={x:f[1],y:f[2]};f=d.getRadiansToVector(f,b);b=d.getMarkerVector(f,g.radius,b);f=-f/w;if(g.width&&g.height){var k=g.width;var m=g.height}else k=m=2*g.radius;this.graphics=this.graphics||{};b={x:b.x-k/2,y:b.y-m/2,width:k,height:m,rotation:f,rotationOriginX:b.x,rotationOriginY:b.y};this.graphics[a]?this.graphics[a].animate(b):(this.graphics[a]=
e.symbol(g.symbol).addClass("highcharts-point-connecting-path-"+a+"-marker").attr(b).add(c.group),e.styledMode||this.graphics[a].attr({fill:g.color||this.fromPoint.color,stroke:g.lineColor,"stroke-width":g.lineWidth,opacity:0}).animate({opacity:1},d.series.options.animation))}};a.prototype.getPath=function(a){var e=this.pathfinder,f=this.chart,q=e.algorithms[a.type],c=e.chartObstacles;if("function"!==typeof q)return A('"'+a.type+'" is not a Pathfinder algorithm.'),{path:[],obstacles:[]};q.requiresObstacles&&
!c&&(c=e.chartObstacles=e.getChartObstacles(a),f.options.connectors.algorithmMargin=a.algorithmMargin,e.chartObstacleMetrics=e.getObstacleMetrics(c));return q(this.fromPoint.getPathfinderAnchorPoint(a.startMarker),this.toPoint.getPathfinderAnchorPoint(a.endMarker),D({chartObstacles:c,lineObstacles:e.lineObstacles||[],obstacleMetrics:e.chartObstacleMetrics,hardBounds:{xMin:0,xMax:f.plotWidth,yMin:0,yMax:f.plotHeight},obstacleOptions:{margin:a.algorithmMargin},startDirectionX:e.getAlgorithmStartDirection(a.startMarker)},
a))};a.prototype.render=function(){var a=this.fromPoint,g=a.series,m=g.chart,q=m.pathfinder,c=D(m.options.connectors,g.options.connectors,a.options.connectors,this.options),d={};m.styledMode||(d.stroke=c.lineColor||a.color,d["stroke-width"]=c.lineWidth,c.dashStyle&&(d.dashstyle=c.dashStyle));d["class"]="highcharts-point-connecting-path highcharts-color-"+a.colorIndex;c=D(d,c);h(c.marker.radius)||(c.marker.radius=f(k(Math.ceil((c.algorithmMargin||8)/2)-1,1),5));a=this.getPath(c);m=a.path;a.obstacles&&
(q.lineObstacles=q.lineObstacles||[],q.lineObstacles=q.lineObstacles.concat(a.obstacles));this.renderPath(m,d,g.options.animation);this.addMarker("start",D(c.marker,c.startMarker),m);this.addMarker("end",D(c.marker,c.endMarker),m)};a.prototype.destroy=function(){this.graphics&&(p(this.graphics,function(a){a.destroy()}),delete this.graphics)};return a}();a.Connection=u;B(t.prototype,{getPathfinderAnchorPoint:function(a){var e=n(this);switch(a.align){case "right":var g="xMax";break;case "left":g="xMin"}switch(a.verticalAlign){case "top":var f=
"yMin";break;case "bottom":f="yMax"}return{x:g?e[g]:(e.xMin+e.xMax)/2,y:f?e[f]:(e.yMin+e.yMax)/2}},getRadiansToVector:function(a,e){var g;h(e)||(g=n(this))&&(e={x:(g.xMin+g.xMax)/2,y:(g.yMin+g.yMax)/2});return Math.atan2(e.y-a.y,a.x-e.x)},getMarkerVector:function(a,e,g){var f=2*Math.PI,q=n(this),c=q.xMax-q.xMin,d=q.yMax-q.yMin,b=Math.atan2(d,c),k=!1;c/=2;var w=d/2,h=q.xMin+c;q=q.yMin+w;for(var p=h,v=q,r=1,t=1;a<-Math.PI;)a+=f;for(;a>Math.PI;)a-=f;f=Math.tan(a);a>-b&&a<=b?(t=-1,k=!0):a>b&&a<=Math.PI-
b?t=-1:a>Math.PI-b||a<=-(Math.PI-b)?(r=-1,k=!0):r=-1;k?(p+=r*c,v+=t*c*f):(p+=d/(2*f)*r,v+=t*w);g.x!==h&&(p=g.x);g.y!==q&&(v=g.y);return{x:p+e*Math.cos(a),y:v-e*Math.sin(a)}}});return u});F(a,"Gantt/PathfinderAlgorithms.js",[a["Core/Utilities.js"]],function(a){function u(a,k,f){f=f||0;var h=a.length-1;k-=1e-7;for(var e,g;f<=h;)if(e=h+f>>1,g=k-a[e].xMin,0<g)f=e+1;else if(0>g)h=e-1;else return e;return 0<f?f-1:0}function t(a,k){for(var f=u(a,k.x+1)+1;f--;){var h;if(h=a[f].xMax>=k.x)h=a[f],h=k.x<=h.xMax&&
k.x>=h.xMin&&k.y<=h.yMax&&k.y>=h.yMin;if(h)return f}return-1}function r(a){var k=[];if(a.length){k.push(["M",a[0].start.x,a[0].start.y]);for(var f=0;f<a.length;++f)k.push(["L",a[f].end.x,a[f].end.y])}return k}function n(a,k){a.yMin=D(a.yMin,k.yMin);a.yMax=B(a.yMax,k.yMax);a.xMin=D(a.xMin,k.xMin);a.xMax=B(a.xMax,k.xMax)}var h=a.extend,A=a.pick,B=Math.min,D=Math.max,p=Math.abs;a=h(function(a,k,f){function h(a,b,c,d,e){a={x:a.x,y:a.y};a[b]=c[d||b]+(e||0);return a}function e(a,b,c){var d=p(b[c]-a[c+"Min"])>
p(b[c]-a[c+"Max"]);return h(b,c,a,c+(d?"Max":"Min"),d?1:-1)}var g=[],m=A(f.startDirectionX,p(k.x-a.x)>p(k.y-a.y))?"x":"y",q=f.chartObstacles,c=t(q,a);f=t(q,k);if(-1<f){var d=q[f];f=e(d,k,m);d={start:f,end:k};var b=f}else b=k;-1<c&&(q=q[c],f=e(q,a,m),g.push({start:a,end:f}),f[m]>=a[m]===f[m]>=b[m]&&(m="y"===m?"x":"y",k=a[m]<k[m],g.push({start:f,end:h(f,m,q,m+(k?"Max":"Min"),k?1:-1)}),m="y"===m?"x":"y"));a=g.length?g[g.length-1].end:a;f=h(a,m,b);g.push({start:a,end:f});m=h(f,"y"===m?"x":"y",b);g.push({start:f,
end:m});g.push(d);return{path:r(g),obstacles:g}},{requiresObstacles:!0});return{fastAvoid:h(function(a,k,f){function h(a,b,l){var c,d=a.x<b.x?1:-1;if(a.x<b.x){var e=a;var y=b}else e=b,y=a;if(a.y<b.y){var f=a;var g=b}else f=b,g=a;for(c=0>d?B(u(C,y.x),C.length-1):0;C[c]&&(0<d&&C[c].xMin<=y.x||0>d&&C[c].xMax>=e.x);){if(C[c].xMin<=y.x&&C[c].xMax>=e.x&&C[c].yMin<=g.y&&C[c].yMax>=f.y)return l?{y:a.y,x:a.x<b.x?C[c].xMin-1:C[c].xMax+1,obstacle:C[c]}:{x:a.x,y:a.y<b.y?C[c].yMin-1:C[c].yMax+1,obstacle:C[c]};
c+=d}return b}function e(a,b,l,c,d){var x=d.soft,e=d.hard,y=c?"x":"y",f={x:b.x,y:b.y},g={x:b.x,y:b.y};d=a[y+"Max"]>=x[y+"Max"];x=a[y+"Min"]<=x[y+"Min"];var G=a[y+"Max"]>=e[y+"Max"];e=a[y+"Min"]<=e[y+"Min"];var z=p(a[y+"Min"]-b[y]),k=p(a[y+"Max"]-b[y]);l=10>p(z-k)?b[y]<l[y]:k<z;g[y]=a[y+"Min"];f[y]=a[y+"Max"];a=h(b,g,c)[y]!==g[y];b=h(b,f,c)[y]!==f[y];l=a?b?l:!0:b?!1:l;l=x?d?l:!0:d?!1:l;return e?G?l:!0:G?!1:l}function g(a,c,l){if(a.x===c.x&&a.y===c.y)return[];var d=l?"x":"y",y=f.obstacleOptions.margin;
var z={soft:{xMin:I,xMax:K,yMin:L,yMax:H},hard:f.hardBounds};var k=t(C,a);if(-1<k){k=C[k];z=e(k,a,c,l,z);n(k,f.hardBounds);var q=l?{y:a.y,x:k[z?"xMax":"xMin"]+(z?1:-1)}:{x:a.x,y:k[z?"yMax":"yMin"]+(z?1:-1)};var m=t(C,q);-1<m&&(m=C[m],n(m,f.hardBounds),q[d]=z?D(k[d+"Max"]-y+1,(m[d+"Min"]+k[d+"Max"])/2):B(k[d+"Min"]+y-1,(m[d+"Max"]+k[d+"Min"])/2),a.x===q.x&&a.y===q.y?(b&&(q[d]=z?D(k[d+"Max"],m[d+"Max"])+1:B(k[d+"Min"],m[d+"Min"])-1),b=!b):b=!1);a=[{start:a,end:q}]}else d=h(a,{x:l?c.x:a.x,y:l?a.y:c.y},
l),a=[{start:a,end:{x:d.x,y:d.y}}],d[l?"x":"y"]!==c[l?"x":"y"]&&(z=e(d.obstacle,d,c,!l,z),n(d.obstacle,f.hardBounds),z={x:l?d.x:d.obstacle[z?"xMax":"xMin"]+(z?1:-1),y:l?d.obstacle[z?"yMax":"yMin"]+(z?1:-1):d.y},l=!l,a=a.concat(g({x:d.x,y:d.y},z,l)));return a=a.concat(g(a[a.length-1].end,c,!l))}function m(a,b,l){var c=B(a.xMax-b.x,b.x-a.xMin)<B(a.yMax-b.y,b.y-a.yMin);l=e(a,b,l,c,{soft:f.hardBounds,hard:f.hardBounds});return c?{y:b.y,x:a[l?"xMax":"xMin"]+(l?1:-1)}:{x:b.x,y:a[l?"yMax":"yMin"]+(l?1:-1)}}
var q=A(f.startDirectionX,p(k.x-a.x)>p(k.y-a.y)),c=q?"x":"y",d=[],b=!1,w=f.obstacleMetrics,I=B(a.x,k.x)-w.maxWidth-10,K=D(a.x,k.x)+w.maxWidth+10,L=B(a.y,k.y)-w.maxHeight-10,H=D(a.y,k.y)+w.maxHeight+10,C=f.chartObstacles;var M=u(C,I);w=u(C,K);C=C.slice(M,w+1);if(-1<(w=t(C,k))){var E=m(C[w],k,a);d.push({end:k,start:E});k=E}for(;-1<(w=t(C,k));)M=0>k[c]-a[c],E={x:k.x,y:k.y},E[c]=C[w][M?c+"Max":c+"Min"]+(M?1:-1),d.push({end:k,start:E}),k=E;a=g(a,k,q);a=a.concat(d.reverse());return{path:r(a),obstacles:a}},
{requiresObstacles:!0}),straight:function(a,k){return{path:[["M",a.x,a.y],["L",k.x,k.y]],obstacles:[{start:a,end:k}]}},simpleConnect:a}});F(a,"Gantt/Pathfinder.js",[a["Gantt/Connection.js"],a["Core/Chart/Chart.js"],a["Core/Globals.js"],a["Core/Options.js"],a["Core/Series/Point.js"],a["Core/Utilities.js"],a["Gantt/PathfinderAlgorithms.js"]],function(a,u,t,r,n,h,A){function B(a){var b=a.shapeArgs;return b?{xMin:b.x,xMax:b.x+b.width,yMin:b.y,yMax:b.y+b.height}:(b=a.graphic&&a.graphic.getBBox())?{xMin:a.plotX-
b.width/2,xMax:a.plotX+b.width/2,yMin:a.plotY-b.height/2,yMax:a.plotY+b.height/2}:null}function D(a){for(var b=a.length,d=0,e,f,k=[],h=function(a,b,d){d=g(d,10);var e=a.yMax+d>b.yMin-d&&a.yMin-d<b.yMax+d,l=a.xMax+d>b.xMin-d&&a.xMin-d<b.xMax+d,x=e?a.xMin>b.xMax?a.xMin-b.xMax:b.xMin-a.xMax:Infinity,f=l?a.yMin>b.yMax?a.yMin-b.yMax:b.yMin-a.yMax:Infinity;return l&&e?d?h(a,b,Math.floor(d/2)):Infinity:c(x,f)};d<b;++d)for(e=d+1;e<b;++e)f=h(a[d],a[e]),80>f&&k.push(f);k.push(80);return q(Math.floor(k.sort(function(a,
b){return a-b})[Math.floor(k.length/10)]/2-1),1)}function p(a){if(a.options.pathfinder||a.series.reduce(function(a,b){b.options&&e(!0,b.options.connectors=b.options.connectors||{},b.options.pathfinder);return a||b.options&&b.options.pathfinder},!1))e(!0,a.options.connectors=a.options.connectors||{},a.options.pathfinder),f('WARNING: Pathfinder options have been renamed. Use "chart.connectors" or "series.connectors" instead.')}"";var w=h.addEvent,k=h.defined,f=h.error,v=h.extend,e=h.merge,g=h.pick,
m=h.splat,q=Math.max,c=Math.min;v(r.defaultOptions,{connectors:{type:"straight",lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}});var d=function(){function b(a){this.lineObstacles=this.group=this.connections=this.chartObstacleMetrics=this.chartObstacles=this.chart=void 0;this.init(a)}b.prototype.init=function(a){this.chart=a;this.connections=[];w(a,"redraw",function(){this.pathfinder.update()})};
b.prototype.update=function(b){var c=this.chart,d=this,e=d.connections;d.connections=[];c.series.forEach(function(b){b.visible&&!b.options.isInternal&&b.points.forEach(function(b){var l=b.options;l&&l.dependency&&(l.connect=l.dependency);var e;l=b.options&&b.options.connect&&m(b.options.connect);b.visible&&!1!==b.isInside&&l&&l.forEach(function(l){e=c.get("string"===typeof l?l:l.to);e instanceof n&&e.series.visible&&e.visible&&!1!==e.isInside&&d.connections.push(new a(b,e,"string"===typeof l?{}:l))})})});
for(var f=0,g,k,q=e.length,y=d.connections.length;f<q;++f){k=!1;for(g=0;g<y;++g)if(e[f].fromPoint===d.connections[g].fromPoint&&e[f].toPoint===d.connections[g].toPoint){d.connections[g].graphics=e[f].graphics;k=!0;break}k||e[f].destroy()}delete this.chartObstacles;delete this.lineObstacles;d.renderConnections(b)};b.prototype.renderConnections=function(a){a?this.chart.series.forEach(function(a){var b=function(){var b=a.chart.pathfinder;(b&&b.connections||[]).forEach(function(b){b.fromPoint&&b.fromPoint.series===
a&&b.render()});a.pathfinderRemoveRenderEvent&&(a.pathfinderRemoveRenderEvent(),delete a.pathfinderRemoveRenderEvent)};!1===a.options.animation?b():a.pathfinderRemoveRenderEvent=w(a,"afterAnimate",b)}):this.connections.forEach(function(a){a.render()})};b.prototype.getChartObstacles=function(a){for(var b=[],c=this.chart.series,d=g(a.algorithmMargin,0),e,f=0,q=c.length;f<q;++f)if(c[f].visible&&!c[f].options.isInternal)for(var h=0,y=c[f].points.length,z;h<y;++h)z=c[f].points[h],z.visible&&(z=B(z))&&
b.push({xMin:z.xMin-d,xMax:z.xMax+d,yMin:z.yMin-d,yMax:z.yMax+d});b=b.sort(function(a,b){return a.xMin-b.xMin});k(a.algorithmMargin)||(e=a.algorithmMargin=D(b),b.forEach(function(a){a.xMin-=e;a.xMax+=e;a.yMin-=e;a.yMax+=e}));return b};b.prototype.getObstacleMetrics=function(a){for(var b=0,c=0,d,e,f=a.length;f--;)d=a[f].xMax-a[f].xMin,e=a[f].yMax-a[f].yMin,b<d&&(b=d),c<e&&(c=e);return{maxHeight:c,maxWidth:b}};b.prototype.getAlgorithmStartDirection=function(a){var b="top"!==a.verticalAlign&&"bottom"!==
a.verticalAlign;return"left"!==a.align&&"right"!==a.align?b?void 0:!1:b?!0:void 0};return b}();d.prototype.algorithms=A;t.Pathfinder=d;v(n.prototype,{getPathfinderAnchorPoint:function(a){var b=B(this);switch(a.align){case "right":var c="xMax";break;case "left":c="xMin"}switch(a.verticalAlign){case "top":var d="yMin";break;case "bottom":d="yMax"}return{x:c?b[c]:(b.xMin+b.xMax)/2,y:d?b[d]:(b.yMin+b.yMax)/2}},getRadiansToVector:function(a,c){var b;k(c)||(b=B(this))&&(c={x:(b.xMin+b.xMax)/2,y:(b.yMin+
b.yMax)/2});return Math.atan2(c.y-a.y,a.x-c.x)},getMarkerVector:function(a,c,d){var b=2*Math.PI,e=B(this),f=e.xMax-e.xMin,g=e.yMax-e.yMin,k=Math.atan2(g,f),q=!1;f/=2;var y=g/2,z=e.xMin+f;e=e.yMin+y;for(var l=z,x=e,G=1,h=1;a<-Math.PI;)a+=b;for(;a>Math.PI;)a-=b;b=Math.tan(a);a>-k&&a<=k?(h=-1,q=!0):a>k&&a<=Math.PI-k?h=-1:a>Math.PI-k||a<=-(Math.PI-k)?(G=-1,q=!0):G=-1;q?(l+=G*f,x+=h*f*b):(l+=g/(2*b)*G,x+=h*y);d.x!==z&&(l=d.x);d.y!==e&&(x=d.y);return{x:l+c*Math.cos(a),y:x-c*Math.sin(a)}}});u.prototype.callbacks.push(function(a){!1!==
a.options.connectors.enabled&&(p(a),this.pathfinder=new d(this),this.pathfinder.update(!0))});return d});F(a,"Series/Gantt/GanttSeries.js",[a["Series/Gantt/GanttPoint.js"],a["Core/Series/SeriesRegistry.js"],a["Core/Utilities.js"]],function(a,u,t){var r=this&&this.__extends||function(){var a=function(k,f){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,e){a.__proto__=e}||function(a,e){for(var f in e)e.hasOwnProperty(f)&&(a[f]=e[f])};return a(k,f)};return function(k,f){function h(){this.constructor=
k}a(k,f);k.prototype=null===f?Object.create(f):(h.prototype=f.prototype,new h)}}(),n=u.series,h=u.seriesTypes.xrange,A=t.extend,B=t.isNumber,D=t.merge,p=t.splat;t=function(a){function k(){var f=null!==a&&a.apply(this,arguments)||this;f.data=void 0;f.options=void 0;f.points=void 0;return f}r(k,a);k.prototype.drawPoint=function(a,k){var e=this.options,f=this.chart.renderer,m=a.shapeArgs,q=a.plotY,c=a.graphic,d=a.selected&&"select",b=e.stacking&&!e.borderRadius;if(a.options.milestone)if(B(q)&&null!==
a.y&&!1!==a.visible){m=f.symbols.diamond(m.x,m.y,m.width,m.height);if(c)c[k]({d:m});else a.graphic=f.path(m).addClass(a.getClassName(),!0).add(a.group||this.group);this.chart.styledMode||a.graphic.attr(this.pointAttribs(a,d)).shadow(e.shadow,null,b)}else c&&(a.graphic=c.destroy());else h.prototype.drawPoint.call(this,a,k)};k.prototype.translatePoint=function(a){h.prototype.translatePoint.call(this,a);if(a.options.milestone){var f=a.shapeArgs;var e=f.height;a.shapeArgs={x:f.x-e/2,y:f.y,width:e,height:e}}};
k.defaultOptions=D(h.defaultOptions,{grouping:!1,dataLabels:{enabled:!0},tooltip:{headerFormat:'<span style="font-size: 10px">{series.name}</span><br/>',pointFormat:null,pointFormatter:function(){var a=this.series,k=a.chart.tooltip,e=a.xAxis,g=a.tooltipOptions.dateTimeLabelFormats,h=e.options.startOfWeek,q=a.tooltipOptions,c=q.xDateFormat,d=this.options.milestone,b="<b>"+(this.name||this.yCategory)+"</b>";if(q.pointFormat)return this.tooltipFormatter(q.pointFormat);c||(c=p(k.getDateFormat(e.closestPointRange,
this.start,h,g))[0]);k=a.chart.time.dateFormat(c,this.start);a=a.chart.time.dateFormat(c,this.end);b+="<br/>";return d?b+(k+"<br/>"):b+("Start: "+k+"<br/>End: ")+(a+"<br/>")}},connectors:{type:"simpleConnect",animation:{reversed:!0},startMarker:{enabled:!0,symbol:"arrow-filled",radius:4,fill:"#fa0",align:"left"},endMarker:{enabled:!1,align:"right"}}});return k}(h);A(t.prototype,{keyboardMoveVertical:!1,pointArrayMap:["start","end","y"],pointClass:a,setData:n.prototype.setData});u.registerSeriesType("gantt",
t);"";return t});F(a,"Core/Chart/GanttChart.js",[a["Core/Chart/Chart.js"],a["Core/Globals.js"],a["Core/Utilities.js"]],function(a,u,t){var r=t.getOptions,n=t.isArray,h=t.merge,A=t.splat;u.ganttChart=function(t,u,p){var w="string"===typeof t||t.nodeName,k=u.series,f=r(),v,e=u;u=arguments[w?1:0];n(u.xAxis)||(u.xAxis=[u.xAxis||{},{}]);u.xAxis=u.xAxis.map(function(a,e){1===e&&(v=0);return h(f.xAxis,{grid:{enabled:!0},opposite:!0,linkedTo:v},a,{type:"datetime"})});u.yAxis=A(u.yAxis||{}).map(function(a){return h(f.yAxis,
{grid:{enabled:!0},staticScale:50,reversed:!0,type:a.categories?a.type:"treegrid"},a)});u.series=null;u=h(!0,{chart:{type:"gantt"},title:{text:null},legend:{enabled:!1},navigator:{series:{type:"gantt"},yAxis:{type:"category"}}},u,{isGantt:!0});u.series=e.series=k;return w?new a(t,u,p):new a(u,u)}});F(a,"Core/Axis/ScrollbarAxis.js",[a["Core/Globals.js"],a["Core/Utilities.js"]],function(a,u){var t=u.addEvent,r=u.defined,n=u.pick;return function(){function h(){}h.compose=function(h,u){var A=function(a){var h=
n(a.options&&a.options.min,a.min),k=n(a.options&&a.options.max,a.max);return{axisMin:h,axisMax:k,scrollMin:r(a.dataMin)?Math.min(h,a.min,a.dataMin,n(a.threshold,Infinity)):h,scrollMax:r(a.dataMax)?Math.max(k,a.max,a.dataMax,n(a.threshold,-Infinity)):k}};t(h,"afterInit",function(){var h=this;h.options&&h.options.scrollbar&&h.options.scrollbar.enabled&&(h.options.scrollbar.vertical=!h.horiz,h.options.startOnTick=h.options.endOnTick=!1,h.scrollbar=new u(h.chart.renderer,h.options.scrollbar,h.chart),
t(h.scrollbar,"changed",function(p){var k=A(h),f=k.axisMax,v=k.scrollMin,e=k.scrollMax-v;r(k.axisMin)&&r(f)&&(h.horiz&&!h.reversed||!h.horiz&&h.reversed?(k=v+e*this.to,v+=e*this.from):(k=v+e*(1-this.from),v+=e*(1-this.to)),n(this.options.liveRedraw,a.svg&&!a.isTouchDevice&&!this.chart.isBoosting)||"mouseup"===p.DOMType||"touchend"===p.DOMType||!r(p.DOMType)?h.setExtremes(v,k,!0,"mousemove"!==p.DOMType&&"touchmove"!==p.DOMType,p):this.setRange(this.from,this.to))}))});t(h,"afterRender",function(){var a=
A(this),h=a.scrollMin,k=a.scrollMax;a=this.scrollbar;var f=this.axisTitleMargin+(this.titleOffset||0),n=this.chart.scrollbarsOffsets,e=this.options.margin||0;a&&(this.horiz?(this.opposite||(n[1]+=f),a.position(this.left,this.top+this.height+2+n[1]-(this.opposite?e:0),this.width,this.height),this.opposite||(n[1]+=e),f=1):(this.opposite&&(n[0]+=f),a.position(this.left+this.width+2+n[0]-(this.opposite?0:e),this.top,this.width,this.height),this.opposite&&(n[0]+=e),f=0),n[f]+=a.size+a.options.margin,isNaN(h)||
isNaN(k)||!r(this.min)||!r(this.max)||this.min===this.max?a.setRange(0,1):(n=(this.min-h)/(k-h),h=(this.max-h)/(k-h),this.horiz&&!this.reversed||!this.horiz&&this.reversed?a.setRange(n,h):a.setRange(1-h,1-n)))});t(h,"afterGetOffset",function(){var a=this.horiz?2:1,h=this.scrollbar;h&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[a]+=h.size+h.options.margin)})};return h}()});F(a,"Core/Scrollbar.js",[a["Core/Axis/Axis.js"],a["Core/Globals.js"],a["Core/Color/Palette.js"],a["Core/Axis/ScrollbarAxis.js"],
a["Core/Utilities.js"],a["Core/Options.js"]],function(a,u,t,r,n,h){var A=n.addEvent,B=n.correctFloat,D=n.defined,p=n.destroyObjectProperties,w=n.fireEvent,k=n.merge,f=n.pick,v=n.removeEvent;n=h.defaultOptions;var e=u.isTouchDevice,g=u.swapXY=function(a,e){e&&a.forEach(function(a){for(var c=a.length,b,e=0;e<c;e+=2)b=a[e+1],"number"===typeof b&&(a[e+1]=a[e+2],a[e+2]=b)});return a};h=function(){function a(a,c,d){this._events=[];this.from=this.chartY=this.chartX=0;this.scrollbar=this.group=void 0;this.scrollbarButtons=
[];this.scrollbarGroup=void 0;this.scrollbarLeft=0;this.scrollbarRifles=void 0;this.scrollbarStrokeWidth=1;this.to=this.size=this.scrollbarTop=0;this.track=void 0;this.trackBorderWidth=1;this.userOptions={};this.y=this.x=0;this.chart=d;this.options=c;this.renderer=d.renderer;this.init(a,c,d)}a.prototype.addEvents=function(){var a=this.options.inverted?[1,0]:[0,1],c=this.scrollbarButtons,d=this.scrollbarGroup.element,b=this.track.element,e=this.mouseDownHandler.bind(this),f=this.mouseMoveHandler.bind(this),
g=this.mouseUpHandler.bind(this);a=[[c[a[0]].element,"click",this.buttonToMinClick.bind(this)],[c[a[1]].element,"click",this.buttonToMaxClick.bind(this)],[b,"click",this.trackClick.bind(this)],[d,"mousedown",e],[d.ownerDocument,"mousemove",f],[d.ownerDocument,"mouseup",g]];u.hasTouch&&a.push([d,"touchstart",e],[d.ownerDocument,"touchmove",f],[d.ownerDocument,"touchend",g]);a.forEach(function(a){A.apply(null,a)});this._events=a};a.prototype.buttonToMaxClick=function(a){var c=(this.to-this.from)*f(this.options.step,
.2);this.updatePosition(this.from+c,this.to+c);w(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:a})};a.prototype.buttonToMinClick=function(a){var c=B(this.to-this.from)*f(this.options.step,.2);this.updatePosition(B(this.from-c),B(this.to-c));w(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:a})};a.prototype.cursorToScrollbarPosition=function(a){var c=this.options;c=c.minWidth>this.calculatedWidth?c.minWidth:0;return{chartX:(a.chartX-this.x-this.xOffset)/
(this.barWidth-c),chartY:(a.chartY-this.y-this.yOffset)/(this.barWidth-c)}};a.prototype.destroy=function(){var a=this.chart.scroller;this.removeEvents();["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(a){this[a]&&this[a].destroy&&(this[a]=this[a].destroy())},this);a&&this===a.scrollbar&&(a.scrollbar=null,p(a.scrollbarButtons))};a.prototype.drawScrollbarButton=function(a){var c=this.renderer,d=this.scrollbarButtons,b=this.options,e=this.size;var f=c.g().add(this.group);
d.push(f);f=c.rect().addClass("highcharts-scrollbar-button").add(f);this.chart.styledMode||f.attr({stroke:b.buttonBorderColor,"stroke-width":b.buttonBorderWidth,fill:b.buttonBackgroundColor});f.attr(f.crisp({x:-.5,y:-.5,width:e+1,height:e+1,r:b.buttonBorderRadius},f.strokeWidth()));f=c.path(g([["M",e/2+(a?-1:1),e/2-3],["L",e/2+(a?-1:1),e/2+3],["L",e/2+(a?2:-2),e/2]],b.vertical)).addClass("highcharts-scrollbar-arrow").add(d[a]);this.chart.styledMode||f.attr({fill:b.buttonArrowColor})};a.prototype.init=
function(e,c,d){this.scrollbarButtons=[];this.renderer=e;this.userOptions=c;this.options=k(a.defaultOptions,c);this.chart=d;this.size=f(this.options.size,this.options.height);c.enabled&&(this.render(),this.addEvents())};a.prototype.mouseDownHandler=function(a){a=this.chart.pointer.normalize(a);a=this.cursorToScrollbarPosition(a);this.chartX=a.chartX;this.chartY=a.chartY;this.initPositions=[this.from,this.to];this.grabbedCenter=!0};a.prototype.mouseMoveHandler=function(a){var c=this.chart.pointer.normalize(a),
d=this.options.vertical?"chartY":"chartX",b=this.initPositions||[];!this.grabbedCenter||a.touches&&0===a.touches[0][d]||(c=this.cursorToScrollbarPosition(c)[d],d=this[d],d=c-d,this.hasDragged=!0,this.updatePosition(b[0]+d,b[1]+d),this.hasDragged&&w(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:a.type,DOMEvent:a}))};a.prototype.mouseUpHandler=function(a){this.hasDragged&&w(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:a.type,DOMEvent:a});this.grabbedCenter=
this.hasDragged=this.chartX=this.chartY=null};a.prototype.position=function(a,c,d,b){var e=this.options.vertical,f=0,g=this.rendered?"animate":"attr";this.x=a;this.y=c+this.trackBorderWidth;this.width=d;this.xOffset=this.height=b;this.yOffset=f;e?(this.width=this.yOffset=d=f=this.size,this.xOffset=c=0,this.barWidth=b-2*d,this.x=a+=this.options.margin):(this.height=this.xOffset=b=c=this.size,this.barWidth=d-2*b,this.y+=this.options.margin);this.group[g]({translateX:a,translateY:this.y});this.track[g]({width:d,
height:b});this.scrollbarButtons[1][g]({translateX:e?0:d-c,translateY:e?b-f:0})};a.prototype.removeEvents=function(){this._events.forEach(function(a){v.apply(null,a)});this._events.length=0};a.prototype.render=function(){var a=this.renderer,c=this.options,d=this.size,b=this.chart.styledMode,e;this.group=e=a.g("scrollbar").attr({zIndex:c.zIndex,translateY:-99999}).add();this.track=a.rect().addClass("highcharts-scrollbar-track").attr({x:0,r:c.trackBorderRadius||0,height:d,width:d}).add(e);b||this.track.attr({fill:c.trackBackgroundColor,
stroke:c.trackBorderColor,"stroke-width":c.trackBorderWidth});this.trackBorderWidth=this.track.strokeWidth();this.track.attr({y:-this.trackBorderWidth%2/2});this.scrollbarGroup=a.g().add(e);this.scrollbar=a.rect().addClass("highcharts-scrollbar-thumb").attr({height:d,width:d,r:c.barBorderRadius||0}).add(this.scrollbarGroup);this.scrollbarRifles=a.path(g([["M",-3,d/4],["L",-3,2*d/3],["M",0,d/4],["L",0,2*d/3],["M",3,d/4],["L",3,2*d/3]],c.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup);
b||(this.scrollbar.attr({fill:c.barBackgroundColor,stroke:c.barBorderColor,"stroke-width":c.barBorderWidth}),this.scrollbarRifles.attr({stroke:c.rifleColor,"stroke-width":1}));this.scrollbarStrokeWidth=this.scrollbar.strokeWidth();this.scrollbarGroup.translate(-this.scrollbarStrokeWidth%2/2,-this.scrollbarStrokeWidth%2/2);this.drawScrollbarButton(0);this.drawScrollbarButton(1)};a.prototype.setRange=function(a,c){var d=this.options,b=d.vertical,e=d.minWidth,f=this.barWidth,g,h=!this.rendered||this.hasDragged||
this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(D(f)){a=Math.max(a,0);var k=Math.ceil(f*a);this.calculatedWidth=g=B(f*Math.min(c,1)-k);g<e&&(k=(f-e+g)*a,g=e);e=Math.floor(k+this.xOffset+this.yOffset);f=g/2-.5;this.from=a;this.to=c;b?(this.scrollbarGroup[h]({translateY:e}),this.scrollbar[h]({height:g}),this.scrollbarRifles[h]({translateY:f}),this.scrollbarTop=e,this.scrollbarLeft=0):(this.scrollbarGroup[h]({translateX:e}),this.scrollbar[h]({width:g}),this.scrollbarRifles[h]({translateX:f}),
this.scrollbarLeft=e,this.scrollbarTop=0);12>=g?this.scrollbarRifles.hide():this.scrollbarRifles.show(!0);!1===d.showFull&&(0>=a&&1<=c?this.group.hide():this.group.show());this.rendered=!0}};a.prototype.trackClick=function(a){var c=this.chart.pointer.normalize(a),d=this.to-this.from,b=this.y+this.scrollbarTop,e=this.x+this.scrollbarLeft;this.options.vertical&&c.chartY>b||!this.options.vertical&&c.chartX>e?this.updatePosition(this.from+d,this.to+d):this.updatePosition(this.from-d,this.to-d);w(this,
"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:a})};a.prototype.update=function(a){this.destroy();this.init(this.chart.renderer,k(!0,this.options,a),this.chart)};a.prototype.updatePosition=function(a,c){1<c&&(a=B(1-B(c-a)),c=1);0>a&&(c=B(c-a),a=0);this.from=a;this.to=c};a.defaultOptions={height:e?20:14,barBorderRadius:0,buttonBorderRadius:0,liveRedraw:void 0,margin:10,minWidth:6,step:.2,zIndex:3,barBackgroundColor:t.neutralColor20,barBorderWidth:1,barBorderColor:t.neutralColor20,
buttonArrowColor:t.neutralColor80,buttonBackgroundColor:t.neutralColor10,buttonBorderColor:t.neutralColor20,buttonBorderWidth:1,rifleColor:t.neutralColor80,trackBackgroundColor:t.neutralColor5,trackBorderColor:t.neutralColor5,trackBorderWidth:1};return a}();u.Scrollbar||(n.scrollbar=k(!0,h.defaultOptions,n.scrollbar),u.Scrollbar=h,r.compose(a,h));return u.Scrollbar});F(a,"Extensions/RangeSelector.js",[a["Core/Axis/Axis.js"],a["Core/Chart/Chart.js"],a["Core/Globals.js"],a["Core/Options.js"],a["Core/Color/Palette.js"],
a["Core/Renderer/SVG/SVGElement.js"],a["Core/Utilities.js"]],function(a,u,t,r,n,h,A){function B(a){if(-1!==a.indexOf("%L"))return"text";var b="aAdewbBmoyY".split("").some(function(b){return-1!==a.indexOf("%"+b)}),c="HkIlMS".split("").some(function(b){return-1!==a.indexOf("%"+b)});return b&&c?"datetime-local":b?"date":c?"time":"text"}var D=r.defaultOptions,p=A.addEvent,w=A.createElement,k=A.css,f=A.defined,v=A.destroyObjectProperties,e=A.discardElement,g=A.extend,m=A.find,q=A.fireEvent,c=A.isNumber,
d=A.merge,b=A.objectEach,F=A.pad,I=A.pick,K=A.pInt,L=A.splat;g(D,{rangeSelector:{allButtonsEnabled:!1,buttons:void 0,buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%b %e, %Y",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,
buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:n.highlightColor80,cursor:"pointer"},labelStyle:{color:n.neutralColor60}}});g(D.lang,{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"\u2192"});var H=function(){function m(a){this.buttons=void 0;this.buttonOptions=m.prototype.defaultButtons;this.initialButtonGroupWidth=0;this.options=void 0;this.chart=a;this.init(a)}m.prototype.clickButton=function(b,d){var l=this.chart,e=this.buttonOptions[b],g=l.xAxis[0],h=l.scroller&&l.scroller.getUnionExtremes()||
g||{},y=h.dataMin,k=h.dataMax,z=g&&Math.round(Math.min(g.max,I(k,g.max))),m=e.type;h=e._range;var n,r=e.dataGrouping;if(null!==y&&null!==k){l.fixedRange=h;r&&(this.forcedDataGrouping=!0,a.prototype.setDataGrouping.call(g||{chart:this.chart},r,!1),this.frozenStates=e.preserveDataGrouping);if("month"===m||"year"===m)if(g){m={range:e,max:z,chart:l,dataMin:y,dataMax:k};var t=g.minFromRange.call(m);c(m.newMax)&&(z=m.newMax)}else h=e;else if(h)t=Math.max(z-h,y),z=Math.min(t+h,k);else if("ytd"===m)if(g)"undefined"===
typeof k&&(y=Number.MAX_VALUE,k=Number.MIN_VALUE,l.series.forEach(function(a){a=a.xData;y=Math.min(a[0],y);k=Math.max(a[a.length-1],k)}),d=!1),z=this.getYTDExtremes(k,y,l.time.useUTC),t=n=z.min,z=z.max;else{this.deferredYTDClick=b;return}else"all"===m&&g&&(t=y,z=k);f(t)&&(t+=e._offsetMin);f(z)&&(z+=e._offsetMax);this.setSelected(b);this.dropdown&&(this.dropdown.selectedIndex=b+1);if(g)g.setExtremes(t,z,I(d,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:e});else{var v=L(l.options.xAxis)[0];
var u=v.range;v.range=h;var w=v.min;v.min=n;p(l,"load",function(){v.range=u;v.min=w})}q(this,"afterBtnClick")}};m.prototype.setSelected=function(a){this.selected=this.options.selected=a};m.prototype.init=function(a){var b=this,c=a.options.rangeSelector,d=c.buttons||b.defaultButtons.slice(),e=c.selected,f=function(){var a=b.minInput,c=b.maxInput;a&&a.blur&&q(a,"blur");c&&c.blur&&q(c,"blur")};b.chart=a;b.options=c;b.buttons=[];b.buttonOptions=d;this.eventsToUnbind=[];this.eventsToUnbind.push(p(a.container,
"mousedown",f));this.eventsToUnbind.push(p(a,"resize",f));d.forEach(b.computeButtonRange);"undefined"!==typeof e&&d[e]&&this.clickButton(e,!1);this.eventsToUnbind.push(p(a,"load",function(){a.xAxis&&a.xAxis[0]&&p(a.xAxis[0],"setExtremes",function(c){this.max-this.min!==a.fixedRange&&"rangeSelectorButton"!==c.trigger&&"updatedData"!==c.trigger&&b.forcedDataGrouping&&!b.frozenStates&&this.setDataGrouping(!1,!1)})}))};m.prototype.updateButtonStates=function(){var a=this,b=this.chart,d=this.dropdown,
e=b.xAxis[0],f=Math.round(e.max-e.min),g=!e.hasVisibleSeries,h=b.scroller&&b.scroller.getUnionExtremes()||e,k=h.dataMin,m=h.dataMax;b=a.getYTDExtremes(m,k,b.time.useUTC);var n=b.min,q=b.max,t=a.selected,p=c(t),r=a.options.allButtonsEnabled,v=a.buttons;a.buttonOptions.forEach(function(b,c){var l=b._range,x=b.type,h=b.count||1,y=v[c],z=0,G=b._offsetMax-b._offsetMin;b=c===t;var O=l>m-k,P=l<e.minRange,N=!1,u=!1;l=l===f;("month"===x||"year"===x)&&f+36E5>=864E5*{month:28,year:365}[x]*h-G&&f-36E5<=864E5*
{month:31,year:366}[x]*h+G?l=!0:"ytd"===x?(l=q-n+G===f,N=!b):"all"===x&&(l=e.max-e.min>=m-k,u=!b&&p&&l);x=!r&&(O||P||u||g);h=b&&l||l&&!p&&!N||b&&a.frozenStates;x?z=3:h&&(p=!0,z=2);y.state!==z&&(y.setState(z),d&&(d.options[c+1].disabled=x,2===z&&(d.selectedIndex=c+1)),0===z&&t===c&&a.setSelected())})};m.prototype.computeButtonRange=function(a){var b=a.type,c=a.count||1,d={millisecond:1,second:1E3,minute:6E4,hour:36E5,day:864E5,week:6048E5};if(d[b])a._range=d[b]*c;else if("month"===b||"year"===b)a._range=
864E5*{month:30,year:365}[b]*c;a._offsetMin=I(a.offsetMin,0);a._offsetMax=I(a.offsetMax,0);a._range+=a._offsetMax-a._offsetMin};m.prototype.getInputValue=function(a){a="min"===a?this.minInput:this.maxInput;var b=this.chart.options.rangeSelector,c=this.chart.time;return a?("text"===a.type&&b.inputDateParser||this.defaultInputDateParser)(a.value,c.useUTC,c):0};m.prototype.setInputValue=function(a,b){var c=this.options,d=this.chart.time,e="min"===a?this.minInput:this.maxInput;a="min"===a?this.minDateBox:
this.maxDateBox;if(e){var g=e.getAttribute("data-hc-time");g=f(g)?Number(g):void 0;f(b)&&(f(g)&&e.setAttribute("data-hc-time-previous",g),e.setAttribute("data-hc-time",b),g=b);e.value=d.dateFormat(this.inputTypeFormats[e.type]||c.inputEditDateFormat,g);a&&a.attr({text:d.dateFormat(c.inputDateFormat,g)})}};m.prototype.setInputExtremes=function(a,b,c){if(a="min"===a?this.minInput:this.maxInput){var d=this.inputTypeFormats[a.type],l=this.chart.time;d&&(b=l.dateFormat(d,b),a.min!==b&&(a.min=b),c=l.dateFormat(d,
c),a.max!==c&&(a.max=c))}};m.prototype.showInput=function(a){var b="min"===a?this.minDateBox:this.maxDateBox;if((a="min"===a?this.minInput:this.maxInput)&&b&&this.inputGroup){var c="text"===a.type,d=this.inputGroup,e=d.translateX;d=d.translateY;k(a,{width:c?b.width-2+"px":"auto",height:c?b.height-2+"px":"auto",border:"2px solid silver"});c?k(a,{left:e+b.x+"px",top:d+"px"}):k(a,{left:Math.min(Math.round(b.x+e-(a.offsetWidth-b.width)/2),this.chart.chartWidth-a.offsetWidth)+"px",top:d-(a.offsetHeight-
b.height)/2+"px"})}};m.prototype.hideInput=function(a){(a="min"===a?this.minInput:this.maxInput)&&k(a,{top:"-9999em",border:0,width:"1px",height:"1px"})};m.prototype.defaultInputDateParser=function(a,b,d){var e=a.split("/").join("-").split(" ").join("T");-1===e.indexOf("T")&&(e+="T00:00");if(b)e+="Z";else{var l;if(l=t.isSafari)l=e,l=!(6<l.length&&(l.lastIndexOf("-")===l.length-6||l.lastIndexOf("+")===l.length-6));l&&(l=(new Date(e)).getTimezoneOffset()/60,e+=0>=l?"+"+F(-l)+":00":"-"+F(l)+":00")}e=
Date.parse(e);c(e)||(a=a.split("-"),e=Date.UTC(K(a[0]),K(a[1])-1,K(a[2])));d&&b&&(e+=d.getTimezoneOffset(e));return e};m.prototype.drawInput=function(a){function b(){var b=m.getInputValue(a),d=e.xAxis[0],l=e.scroller&&e.scroller.xAxis?e.scroller.xAxis:d,f=l.dataMin;l=l.dataMax;var g=m.maxInput,x=m.minInput;b!==Number(u.getAttribute("data-hc-time-previous"))&&c(b)&&(u.setAttribute("data-hc-time-previous",b),r&&g&&c(f)?b>Number(g.getAttribute("data-hc-time"))?b=void 0:b<f&&(b=f):x&&c(l)&&(b<Number(x.getAttribute("data-hc-time"))?
b=void 0:b>l&&(b=l)),"undefined"!==typeof b&&d.setExtremes(r?b:d.min,r?d.max:b,void 0,void 0,{trigger:"rangeSelectorInput"}))}var e=this.chart,f=this.div,h=this.inputGroup,m=this,y=e.renderer.style||{},q=e.renderer,p=e.options.rangeSelector,r="min"===a,v=D.lang[r?"rangeSelectorFrom":"rangeSelectorTo"];v=q.label(v,0).addClass("highcharts-range-label").attr({padding:v?2:0}).add(h);q=q.label("",0).addClass("highcharts-range-input").attr({padding:2,width:p.inputBoxWidth,height:p.inputBoxHeight,"text-align":"center"}).on("click",
function(){m.showInput(a);m[a+"Input"].focus()});e.styledMode||q.attr({stroke:p.inputBoxBorderColor,"stroke-width":1});q.add(h);var u=w("input",{name:a,className:"highcharts-range-selector"},void 0,f);u.setAttribute("type",B(p.inputDateFormat||"%b %e, %Y"));e.styledMode||(v.css(d(y,p.labelStyle)),q.css(d({color:n.neutralColor80},y,p.inputStyle)),k(u,g({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:y.fontSize,fontFamily:y.fontFamily,
top:"-9999em"},p.inputStyle)));u.onfocus=function(){m.showInput(a)};u.onblur=function(){u===t.doc.activeElement&&b();m.hideInput(a);m.setInputValue(a);u.blur()};var E=!1;u.onchange=function(){b();E||(m.hideInput(a),u.blur())};u.onkeypress=function(a){13===a.keyCode&&b()};u.onkeydown=function(){E=!0};u.onkeyup=function(){E=!1};return{dateBox:q,input:u,label:v}};m.prototype.getPosition=function(){var a=this.chart,b=a.options.rangeSelector;a="top"===b.verticalAlign?a.plotTop-a.axisOffset[0]:0;return{buttonTop:a+
b.buttonPosition.y,inputTop:a+b.inputPosition.y-10}};m.prototype.getYTDExtremes=function(a,b,c){var d=this.chart.time,e=new d.Date(a),l=d.get("FullYear",e);c=c?d.Date.UTC(l,0,1):+new d.Date(l,0,1);b=Math.max(b,c);e=e.getTime();return{max:Math.min(a||e,e),min:b}};m.prototype.render=function(a,b){var c=this.chart,d=c.renderer,e=c.container,g=c.options,h=g.rangeSelector,k=I(g.chart.style&&g.chart.style.zIndex,0)+1;g=h.inputEnabled;if(!1!==h.enabled){this.rendered||(this.group=d.g("range-selector-group").attr({zIndex:7}).add(),
this.div=w("div",void 0,{position:"relative",height:0,zIndex:k}),this.buttonOptions.length&&this.renderButtons(),e.parentNode&&e.parentNode.insertBefore(this.div,e),g&&(this.inputGroup=d.g("input-group").add(this.group),d=this.drawInput("min"),this.minDateBox=d.dateBox,this.minLabel=d.label,this.minInput=d.input,d=this.drawInput("max"),this.maxDateBox=d.dateBox,this.maxLabel=d.label,this.maxInput=d.input));if(g&&(this.setInputValue("min",a),this.setInputValue("max",b),a=c.scroller&&c.scroller.getUnionExtremes()||
c.xAxis[0]||{},f(a.dataMin)&&f(a.dataMax)&&(c=c.xAxis[0].minRange||0,this.setInputExtremes("min",a.dataMin,Math.min(a.dataMax,this.getInputValue("max"))-c),this.setInputExtremes("max",Math.max(a.dataMin,this.getInputValue("min"))+c,a.dataMax)),this.inputGroup)){var m=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(function(a){a&&a.width&&(a.attr({x:m}),m+=a.width+h.inputSpacing)})}this.alignElements();this.rendered=!0}};m.prototype.renderButtons=function(){var a=this,b=this.buttons,
c=this.options,e=D.lang,f=this.chart.renderer,g=d(c.buttonTheme),h=g&&g.states,k=g.width||28;delete g.width;this.buttonGroup=f.g("range-selector-buttons").add(this.group);var m=this.dropdown=w("select",void 0,{position:"absolute",width:"1px",height:"1px",padding:0,border:0,top:"-9999em",cursor:"pointer",opacity:.0001},this.div);p(m,"touchstart",function(){m.style.fontSize="16px"});[[t.isMS?"mouseover":"mouseenter"],[t.isMS?"mouseout":"mouseleave"],["change","click"]].forEach(function(c){var d=c[0],
e=c[1];p(m,d,function(){var c=b[a.currentButtonIndex()];c&&q(c.element,e||d)})});this.zoomText=f.text(e.rangeSelectorZoom,0,15).add(this.buttonGroup);this.chart.styledMode||(this.zoomText.css(c.labelStyle),g["stroke-width"]=I(g["stroke-width"],0));w("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,m);this.buttonOptions.forEach(function(c,d){w("option",{textContent:c.title||c.text},void 0,m);b[d]=f.button(c.text,0,0,function(b){var e=c.events&&c.events.click,l;e&&(l=e.call(c,b));!1!==
l&&a.clickButton(d);a.isActive=!0},g,h&&h.hover,h&&h.select,h&&h.disabled).attr({"text-align":"center",width:k}).add(a.buttonGroup);c.title&&b[d].attr("title",c.title)})};m.prototype.alignElements=function(){var a=this,b=this.buttonGroup,c=this.buttons,d=this.chart,e=this.group,f=this.inputGroup,g=this.options,h=this.zoomText,k=d.options,m=k.exporting&&!1!==k.exporting.enabled&&k.navigation&&k.navigation.buttonOptions;k=g.buttonPosition;var n=g.inputPosition,q=g.verticalAlign,p=function(b,c){return m&&
a.titleCollision(d)&&"top"===q&&"right"===c.align&&c.y-b.getBBox().height-12<(m.y||0)+(m.height||0)+d.spacing[0]?-40:0},t=d.plotLeft;if(e&&k&&n){var r=k.x-d.spacing[3];if(b){this.positionButtons();if(!this.initialButtonGroupWidth){var u=0;h&&(u+=h.getBBox().width+5);c.forEach(function(a,b){u+=a.width;b!==c.length-1&&(u+=g.buttonSpacing)});this.initialButtonGroupWidth=u}t-=d.spacing[3];this.updateButtonStates();h=p(b,k);this.alignButtonGroup(h);e.placed=b.placed=d.hasLoaded}b=0;f&&(b=p(f,n),"left"===
n.align?r=t:"right"===n.align&&(r=-Math.max(d.axisOffset[1],-b)),f.align({y:n.y,width:f.getBBox().width,align:n.align,x:n.x+r-2},!0,d.spacingBox),f.placed=d.hasLoaded);this.handleCollision(b);e.align({verticalAlign:q},!0,d.spacingBox);f=e.alignAttr.translateY;b=e.getBBox().height+20;p=0;"bottom"===q&&(p=(p=d.legend&&d.legend.options)&&"bottom"===p.verticalAlign&&p.enabled&&!p.floating?d.legend.legendHeight+I(p.margin,10):0,b=b+p-20,p=f-b-(g.floating?0:g.y)-(d.titleOffset?d.titleOffset[2]:0)-10);if("top"===
q)g.floating&&(p=0),d.titleOffset&&d.titleOffset[0]&&(p=d.titleOffset[0]),p+=d.margin[0]-d.spacing[0]||0;else if("middle"===q)if(n.y===k.y)p=f;else if(n.y||k.y)p=0>n.y||0>k.y?p-Math.min(n.y,k.y):f-b;e.translate(g.x,g.y+Math.floor(p));k=this.minInput;n=this.maxInput;f=this.dropdown;g.inputEnabled&&k&&n&&(k.style.marginTop=e.translateY+"px",n.style.marginTop=e.translateY+"px");f&&(f.style.marginTop=e.translateY+"px")}};m.prototype.alignButtonGroup=function(a,b){var c=this.chart,d=this.buttonGroup,e=
this.options.buttonPosition,f=c.plotLeft-c.spacing[3],g=e.x-c.spacing[3];"right"===e.align?g+=a-f:"center"===e.align&&(g-=f/2);d&&d.align({y:e.y,width:I(b,this.initialButtonGroupWidth),align:e.align,x:g},!0,c.spacingBox)};m.prototype.positionButtons=function(){var a=this.buttons,b=this.chart,c=this.options,d=this.zoomText,e=b.hasLoaded?"animate":"attr",f=c.buttonPosition,g=b.plotLeft,h=g;d&&"hidden"!==d.visibility&&(d[e]({x:I(g+f.x,g)}),h+=f.x+d.getBBox().width+5);this.buttonOptions.forEach(function(b,
d){if("hidden"!==a[d].visibility)a[d][e]({x:h}),h+=a[d].width+c.buttonSpacing;else a[d][e]({x:g})})};m.prototype.handleCollision=function(a){var b=this,c=this.chart,d=this.buttonGroup,e=this.inputGroup,f=this.options,g=f.buttonPosition,h=f.dropdown,k=f.inputPosition;f=function(){var a=0;b.buttons.forEach(function(b){b=b.getBBox();b.width>a&&(a=b.width)});return a};var m=function(b){if(e&&d){var c=e.alignAttr.translateX+e.alignOptions.x-a+e.getBBox().x+2,l=e.alignOptions.width,f=d.alignAttr.translateX+
d.getBBox().x;return f+b>c&&c+l>f&&g.y<k.y+e.getBBox().height}return!1},n=function(){e&&d&&e.attr({translateX:e.alignAttr.translateX+(c.axisOffset[1]>=-a?0:-a),translateY:e.alignAttr.translateY+d.getBBox().height+10})};if(d){if("always"===h){this.collapseButtons(a);m(f())&&n();return}"never"===h&&this.expandButtons()}e&&d?k.align===g.align||m(this.initialButtonGroupWidth+20)?"responsive"===h?(this.collapseButtons(a),m(f())&&n()):n():"responsive"===h&&this.expandButtons():d&&"responsive"===h&&(this.initialButtonGroupWidth>
c.plotWidth?this.collapseButtons(a):this.expandButtons())};m.prototype.collapseButtons=function(a){var b,c=this.buttons,d=this.buttonOptions,e=this.dropdown,f=this.options,g=this.zoomText,h=function(a){return{text:a?a+" \u25be":"\u25be",width:"auto",paddingLeft:8,paddingRight:8}};g&&g.hide();var k=!1;d.forEach(function(a,b){b=c[b];2!==b.state?b.hide():(b.show(),b.attr(h(a.text)),k=!0)});k||(e&&(e.selectedIndex=0),c[0].show(),c[0].attr(h(null===(b=this.zoomText)||void 0===b?void 0:b.textStr)));b=f.buttonPosition.align;
this.positionButtons();"right"!==b&&"center"!==b||this.alignButtonGroup(a,c[this.currentButtonIndex()].getBBox().width);this.showDropdown()};m.prototype.expandButtons=function(){var a=this.buttons,b=this.buttonOptions,c=this.options,d=this.zoomText;this.hideDropdown();d&&d.show();b.forEach(function(b,d){d=a[d];d.show();d.attr({text:b.text,width:c.buttonTheme.width||28,paddingLeft:"unset",paddingRight:"unset"});2>d.state&&d.setState(0)});this.positionButtons()};m.prototype.currentButtonIndex=function(){var a=
this.dropdown;return a&&0<a.selectedIndex?a.selectedIndex-1:0};m.prototype.showDropdown=function(){var a=this.buttonGroup,b=this.buttons,d=this.chart,c=this.dropdown;if(a&&c){var e=a.translateX;a=a.translateY;b=b[this.currentButtonIndex()].getBBox();k(c,{left:d.plotLeft+e+"px",top:a+.5+"px",width:b.width+"px",height:b.height+"px"});this.hasVisibleDropdown=!0}};m.prototype.hideDropdown=function(){var a=this.dropdown;a&&(k(a,{top:"-9999em",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)};m.prototype.getHeight=
function(){var a=this.options,b=this.group,d=a.y,c=a.buttonPosition.y,e=a.inputPosition.y;if(a.height)return a.height;this.alignElements();a=b?b.getBBox(!0).height+13+d:0;b=Math.min(e,c);if(0>e&&0>c||0<e&&0<c)a+=Math.abs(b);return a};m.prototype.titleCollision=function(a){return!(a.options.title.text||a.options.subtitle.text)};m.prototype.update=function(a){var b=this.chart;d(!0,b.options.rangeSelector,a);this.destroy();this.init(b);this.render()};m.prototype.destroy=function(){var a=this,c=a.minInput,
d=a.maxInput;a.eventsToUnbind&&(a.eventsToUnbind.forEach(function(a){return a()}),a.eventsToUnbind=void 0);v(a.buttons);c&&(c.onfocus=c.onblur=c.onchange=null);d&&(d.onfocus=d.onblur=d.onchange=null);b(a,function(b,c){b&&"chart"!==c&&(b instanceof h?b.destroy():b instanceof window.HTMLElement&&e(b));b!==m.prototype[c]&&(a[c]=null)},this)};return m}();H.prototype.defaultButtons=[{type:"month",count:1,text:"1m",title:"View 1 month"},{type:"month",count:3,text:"3m",title:"View 3 months"},{type:"month",
count:6,text:"6m",title:"View 6 months"},{type:"ytd",text:"YTD",title:"View year to date"},{type:"year",count:1,text:"1y",title:"View 1 year"},{type:"all",text:"All",title:"View all"}];H.prototype.inputTypeFormats={"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"};a.prototype.minFromRange=function(){var a=this.range,b=a.type,d=this.max,e=this.chart.time,f=function(a,d){var c="year"===b?"FullYear":"Month",l=new e.Date(a),f=e.get(c,l);e.set(c,l,f+d);f===e.get(c,l)&&e.set("Date",
l,0);return l.getTime()-a};if(c(a)){var g=d-a;var h=a}else g=d+f(d,-a.count),this.chart&&(this.chart.fixedRange=d-g);var k=I(this.dataMin,Number.MIN_VALUE);c(g)||(g=k);g<=k&&(g=k,"undefined"===typeof h&&(h=f(g,a.count)),this.newMax=Math.min(g+h,this.dataMax));c(d)||(g=void 0);return g};if(!t.RangeSelector){var C=[],M=function(a){function b(){l&&(e=a.xAxis[0].getExtremes(),f=a.legend,h=null===l||void 0===l?void 0:l.options.verticalAlign,c(e.min)&&l.render(e.min,e.max),f.display&&"top"===h&&h===f.options.verticalAlign&&
(g=d(a.spacingBox),g.y="vertical"===f.options.layout?a.plotTop:g.y+l.getHeight(),f.group.placed=!1,f.align(g)))}var e,l=a.rangeSelector,f,g,h;l&&(m(C,function(b){return b[0]===a})||C.push([a,[p(a.xAxis[0],"afterSetExtremes",function(a){l&&l.render(a.min,a.max)}),p(a,"redraw",b)]]),b())};p(u,"afterGetContainer",function(){var a;if(null===(a=this.options.rangeSelector)||void 0===a?0:a.enabled)this.rangeSelector=new H(this)});p(u,"beforeRender",function(){var a=this.axes,b=this.rangeSelector;b&&(c(b.deferredYTDClick)&&
(b.clickButton(b.deferredYTDClick),delete b.deferredYTDClick),a.forEach(function(a){a.updateNames();a.setScale()}),this.getAxisMargins(),b.render(),a=b.options.verticalAlign,b.options.floating||("bottom"===a?this.extraBottomMargin=!0:"middle"!==a&&(this.extraTopMargin=!0)))});p(u,"update",function(a){var b=a.options.rangeSelector;a=this.rangeSelector;var c=this.extraBottomMargin,d=this.extraTopMargin;b&&b.enabled&&!f(a)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=
a=new H(this));this.extraTopMargin=this.extraBottomMargin=!1;a&&(M(this),b=b&&b.verticalAlign||a.options&&a.options.verticalAlign,a.options.floating||("bottom"===b?this.extraBottomMargin=!0:"middle"!==b&&(this.extraTopMargin=!0)),this.extraBottomMargin!==c||this.extraTopMargin!==d)&&(this.isDirtyBox=!0)});p(u,"render",function(){var a=this.rangeSelector;a&&!a.options.floating&&(a.render(),a=a.options.verticalAlign,"bottom"===a?this.extraBottomMargin=!0:"middle"!==a&&(this.extraTopMargin=!0))});p(u,
"getMargins",function(){var a=this.rangeSelector;a&&(a=a.getHeight(),this.extraTopMargin&&(this.plotTop+=a),this.extraBottomMargin&&(this.marginBottom+=a))});u.prototype.callbacks.push(M);p(u,"destroy",function(){for(var a=0;a<C.length;a++){var b=C[a];if(b[0]===this){b[1].forEach(function(a){return a()});C.splice(a,1);break}}});t.RangeSelector=H}return t.RangeSelector});F(a,"Core/Axis/NavigatorAxis.js",[a["Core/Globals.js"],a["Core/Utilities.js"]],function(a,u){var t=a.isTouchDevice,r=u.addEvent,
n=u.correctFloat,h=u.defined,A=u.isNumber,B=u.pick,D=function(){function a(a){this.axis=a}a.prototype.destroy=function(){this.axis=void 0};a.prototype.toFixedRange=function(a,k,f,p){var e=this.axis,g=e.chart;g=g&&g.fixedRange;var m=(e.pointRange||0)/2;a=B(f,e.translate(a,!0,!e.horiz));k=B(p,e.translate(k,!0,!e.horiz));e=g&&(k-a)/g;h(f)||(a=n(a+m));h(p)||(k=n(k-m));.7<e&&1.3>e&&(p?a=k-g:k=a+g);A(a)&&A(k)||(a=k=void 0);return{min:a,max:k}};return a}();return function(){function a(){}a.compose=function(a){a.keepProps.push("navigatorAxis");
r(a,"init",function(){this.navigatorAxis||(this.navigatorAxis=new D(this))});r(a,"zoom",function(a){var f=this.chart.options,k=f.navigator,e=this.navigatorAxis,g=f.chart.pinchType,m=f.rangeSelector;f=f.chart.zoomType;this.isXAxis&&(k&&k.enabled||m&&m.enabled)&&("y"===f?a.zoomed=!1:(!t&&"xy"===f||t&&"xy"===g)&&this.options.range&&(k=e.previousZoom,h(a.newMin)?e.previousZoom=[this.min,this.max]:k&&(a.newMin=k[0],a.newMax=k[1],e.previousZoom=void 0)));"undefined"!==typeof a.zoomed&&a.preventDefault()})};
a.AdditionsClass=D;return a}()});F(a,"Core/Navigator.js",[a["Core/Axis/Axis.js"],a["Core/Chart/Chart.js"],a["Core/Color/Color.js"],a["Core/Globals.js"],a["Core/Axis/NavigatorAxis.js"],a["Core/Options.js"],a["Core/Color/Palette.js"],a["Core/Scrollbar.js"],a["Core/Series/Series.js"],a["Core/Series/SeriesRegistry.js"],a["Core/Utilities.js"]],function(a,u,t,r,n,h,A,B,D,p,w){t=t.parse;var k=r.hasTouch,f=r.isTouchDevice,v=h.defaultOptions,e=w.addEvent,g=w.clamp,m=w.correctFloat,q=w.defined,c=w.destroyObjectProperties,
d=w.erase,b=w.extend,F=w.find,I=w.isArray,K=w.isNumber,L=w.merge,H=w.pick,C=w.removeEvent,M=w.splat,E=function(a){for(var b=[],c=1;c<arguments.length;c++)b[c-1]=arguments[c];b=[].filter.call(b,K);if(b.length)return Math[a].apply(0,b)};h="undefined"===typeof p.seriesTypes.areaspline?"line":"areaspline";b(v,{navigator:{height:40,margin:25,maskInside:!0,handles:{width:7,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:A.neutralColor5,borderColor:A.neutralColor40},
maskFill:t(A.highlightColor60).setOpacity(.3).get(),outlineColor:A.neutralColor20,outlineWidth:1,series:{type:h,fillOpacity:.05,lineWidth:1,compare:null,dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,smoothed:!0,units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",
className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{overscroll:0,className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:A.neutralColor10,gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:A.neutralColor40},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:null},tickLength:0,
tickWidth:0}}});r.Renderer.prototype.symbols["navigator-handle"]=function(a,b,c,d,e){a=(e&&e.width||0)/2;b=Math.round(a/3)+.5;e=e&&e.height||0;return[["M",-a-1,.5],["L",a,.5],["L",a,e+.5],["L",-a-1,e+.5],["L",-a-1,.5],["M",-b,4],["L",-b,e-3],["M",b-1,4],["L",b-1,e-3]]};var y=function(){function h(a){this.zoomedMin=this.zoomedMax=this.yAxis=this.xAxis=this.top=this.size=this.shades=this.rendered=this.range=this.outlineHeight=this.outline=this.opposite=this.navigatorSize=this.navigatorSeries=this.navigatorOptions=
this.navigatorGroup=this.navigatorEnabled=this.left=this.height=this.handles=this.chart=this.baseSeries=void 0;this.init(a)}h.prototype.drawHandle=function(a,b,c,d){var e=this.navigatorOptions.handles.height;this.handles[b][d](c?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(a,10)+.5-e)}:{translateX:Math.round(this.left+parseInt(a,10)),translateY:Math.round(this.top+this.height/2-e/2-1)})};h.prototype.drawOutline=function(a,b,c,d){var e=this.navigatorOptions.maskInside,
l=this.outline.strokeWidth(),f=l/2,g=l%2/2;l=this.outlineHeight;var h=this.scrollbarHeight||0,k=this.size,m=this.left-h,x=this.top;c?(m-=f,c=x+b+g,b=x+a+g,g=[["M",m+l,x-h-g],["L",m+l,c],["L",m,c],["L",m,b],["L",m+l,b],["L",m+l,x+k+h]],e&&g.push(["M",m+l,c-f],["L",m+l,b+f])):(a+=m+h-g,b+=m+h-g,x+=f,g=[["M",m,x],["L",a,x],["L",a,x+l],["L",b,x+l],["L",b,x],["L",m+k+2*h,x]],e&&g.push(["M",a-f,x],["L",b+f,x]));this.outline[d]({d:g})};h.prototype.drawMasks=function(a,b,c,d){var e=this.left,l=this.top,f=
this.height;if(c){var g=[e,e,e];var h=[l,l+a,l+b];var k=[f,f,f];var m=[a,b-a,this.size-b]}else g=[e,e+a,e+b],h=[l,l,l],k=[a,b-a,this.size-b],m=[f,f,f];this.shades.forEach(function(a,b){a[d]({x:g[b],y:h[b],width:k[b],height:m[b]})})};h.prototype.renderElements=function(){var a=this,b=a.navigatorOptions,c=b.maskInside,d=a.chart,e=d.renderer,f,g={cursor:d.inverted?"ns-resize":"ew-resize"};a.navigatorGroup=f=e.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();[!c,c,!c].forEach(function(c,l){a.shades[l]=
e.rect().addClass("highcharts-navigator-mask"+(1===l?"-inside":"-outside")).add(f);d.styledMode||a.shades[l].attr({fill:c?b.maskFill:"rgba(0,0,0,0)"}).css(1===l&&g)});a.outline=e.path().addClass("highcharts-navigator-outline").add(f);d.styledMode||a.outline.attr({"stroke-width":b.outlineWidth,stroke:b.outlineColor});b.handles.enabled&&[0,1].forEach(function(c){b.handles.inverted=d.inverted;a.handles[c]=e.symbol(b.handles.symbols[c],-b.handles.width/2-1,0,b.handles.width,b.handles.height,b.handles);
a.handles[c].attr({zIndex:7-c}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][c]).add(f);if(!d.styledMode){var l=b.handles;a.handles[c].attr({fill:l.backgroundColor,stroke:l.borderColor,"stroke-width":l.lineWidth}).css(g)}})};h.prototype.update=function(a){(this.series||[]).forEach(function(a){a.baseSeries&&delete a.baseSeries.navigatorSeries});this.destroy();L(!0,this.chart.options.navigator,this.options,a);this.init(this.chart)};h.prototype.render=function(a,
b,c,d){var e=this.chart,l=this.scrollbarHeight,f,h=this.xAxis,k=h.pointRange||0;var x=h.navigatorAxis.fake?e.xAxis[0]:h;var n=this.navigatorEnabled,p,t=this.rendered;var r=e.inverted;var u=e.xAxis[0].minRange,v=e.xAxis[0].options.maxRange;if(!this.hasDragged||q(c)){a=m(a-k/2);b=m(b+k/2);if(!K(a)||!K(b))if(t)c=0,d=H(h.width,x.width);else return;this.left=H(h.left,e.plotLeft+l+(r?e.plotWidth:0));this.size=p=f=H(h.len,(r?e.plotHeight:e.plotWidth)-2*l);e=r?l:f+2*l;c=H(c,h.toPixels(a,!0));d=H(d,h.toPixels(b,
!0));K(c)&&Infinity!==Math.abs(c)||(c=0,d=e);a=h.toValue(c,!0);b=h.toValue(d,!0);var G=Math.abs(m(b-a));G<u?this.grabbedLeft?c=h.toPixels(b-u-k,!0):this.grabbedRight&&(d=h.toPixels(a+u+k,!0)):q(v)&&m(G-k)>v&&(this.grabbedLeft?c=h.toPixels(b-v-k,!0):this.grabbedRight&&(d=h.toPixels(a+v+k,!0)));this.zoomedMax=g(Math.max(c,d),0,p);this.zoomedMin=g(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(c,d),0,p);this.range=this.zoomedMax-this.zoomedMin;p=Math.round(this.zoomedMax);c=Math.round(this.zoomedMin);
n&&(this.navigatorGroup.attr({visibility:"visible"}),t=t&&!this.hasDragged?"animate":"attr",this.drawMasks(c,p,r,t),this.drawOutline(c,p,r,t),this.navigatorOptions.handles.enabled&&(this.drawHandle(c,0,r,t),this.drawHandle(p,1,r,t)));this.scrollbar&&(r?(r=this.top-l,x=this.left-l+(n||!x.opposite?0:(x.titleOffset||0)+x.axisTitleMargin),l=f+2*l):(r=this.top+(n?this.height:-l),x=this.left-l),this.scrollbar.position(x,r,e,l),this.scrollbar.setRange(this.zoomedMin/(f||1),this.zoomedMax/(f||1)));this.rendered=
!0}};h.prototype.addMouseEvents=function(){var a=this,b=a.chart,c=b.container,d=[],f,g;a.mouseMoveHandler=f=function(b){a.onMouseMove(b)};a.mouseUpHandler=g=function(b){a.onMouseUp(b)};d=a.getPartsEvents("mousedown");d.push(e(b.renderTo,"mousemove",f),e(c.ownerDocument,"mouseup",g));k&&(d.push(e(b.renderTo,"touchmove",f),e(c.ownerDocument,"touchend",g)),d.concat(a.getPartsEvents("touchstart")));a.eventsToUnbind=d;a.series&&a.series[0]&&d.push(e(a.series[0].xAxis,"foundExtremes",function(){b.navigator.modifyNavigatorAxisExtremes()}))};
h.prototype.getPartsEvents=function(a){var b=this,c=[];["shades","handles"].forEach(function(d){b[d].forEach(function(l,f){c.push(e(l.element,a,function(a){b[d+"Mousedown"](a,f)}))})});return c};h.prototype.shadesMousedown=function(a,b){a=this.chart.pointer.normalize(a);var c=this.chart,d=this.xAxis,e=this.zoomedMin,l=this.left,f=this.size,g=this.range,h=a.chartX;c.inverted&&(h=a.chartY,l=this.top);if(1===b)this.grabbedCenter=h,this.fixedWidth=g,this.dragOffset=h-e;else{a=h-l-g/2;if(0===b)a=Math.max(0,
a);else if(2===b&&a+g>=f)if(a=f-g,this.reversedExtremes){a-=g;var k=this.getUnionExtremes().dataMin}else var m=this.getUnionExtremes().dataMax;a!==e&&(this.fixedWidth=g,b=d.navigatorAxis.toFixedRange(a,a+g,k,m),q(b.min)&&c.xAxis[0].setExtremes(Math.min(b.min,b.max),Math.max(b.min,b.max),!0,null,{trigger:"navigator"}))}};h.prototype.handlesMousedown=function(a,b){this.chart.pointer.normalize(a);a=this.chart;var c=a.xAxis[0],d=this.reversedExtremes;0===b?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,
this.fixedExtreme=d?c.min:c.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=d?c.max:c.min);a.fixedRange=null};h.prototype.onMouseMove=function(a){var b=this,c=b.chart,d=b.left,e=b.navigatorSize,l=b.range,g=b.dragOffset,h=c.inverted;a.touches&&0===a.touches[0].pageX||(a=c.pointer.normalize(a),c=a.chartX,h&&(d=b.top,c=a.chartY),b.grabbedLeft?(b.hasDragged=!0,b.render(0,0,c-d,b.otherHandlePos)):b.grabbedRight?(b.hasDragged=!0,b.render(0,0,b.otherHandlePos,c-d)):b.grabbedCenter&&
(b.hasDragged=!0,c<g?c=g:c>e+g-l&&(c=e+g-l),b.render(0,0,c-g,c-g+l)),b.hasDragged&&b.scrollbar&&H(b.scrollbar.options.liveRedraw,r.svg&&!f&&!this.chart.isBoosting)&&(a.DOMType=a.type,setTimeout(function(){b.onMouseUp(a)},0)))};h.prototype.onMouseUp=function(a){var b=this.chart,c=this.xAxis,d=this.scrollbar,e=a.DOMEvent||a,f=b.inverted,g=this.rendered&&!this.hasDragged?"animate":"attr";if(this.hasDragged&&(!d||!d.hasDragged)||"scrollbar"===a.trigger){d=this.getUnionExtremes();if(this.zoomedMin===this.otherHandlePos)var l=
this.fixedExtreme;else if(this.zoomedMax===this.otherHandlePos)var h=this.fixedExtreme;this.zoomedMax===this.size&&(h=this.reversedExtremes?d.dataMin:d.dataMax);0===this.zoomedMin&&(l=this.reversedExtremes?d.dataMax:d.dataMin);c=c.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,l,h);q(c.min)&&b.xAxis[0].setExtremes(Math.min(c.min,c.max),Math.max(c.min,c.max),!0,this.hasDragged?!1:null,{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:e})}"mousemove"!==a.DOMType&&"touchmove"!==a.DOMType&&
(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null);this.navigatorEnabled&&K(this.zoomedMin)&&K(this.zoomedMax)&&(b=Math.round(this.zoomedMin),a=Math.round(this.zoomedMax),this.shades&&this.drawMasks(b,a,f,g),this.outline&&this.drawOutline(b,a,f,g),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(b,0,f,g),this.drawHandle(a,1,f,g)))};h.prototype.removeEvents=
function(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(a){a()}),this.eventsToUnbind=void 0);this.removeBaseSeriesEvents()};h.prototype.removeBaseSeriesEvents=function(){var a=this.baseSeries||[];this.navigatorEnabled&&a[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&a.forEach(function(a){C(a,"updatedData",this.updatedDataHandler)},this),a[0].xAxis&&C(a[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))};h.prototype.init=function(b){var c=b.options,d=c.navigator,f=d.enabled,
g=c.scrollbar,h=g.enabled;c=f?d.height:0;var l=h?g.height:0;this.handles=[];this.shades=[];this.chart=b;this.setBaseSeries();this.height=c;this.scrollbarHeight=l;this.scrollbarEnabled=h;this.navigatorEnabled=f;this.navigatorOptions=d;this.scrollbarOptions=g;this.outlineHeight=c+l;this.opposite=H(d.opposite,!(f||!b.inverted));var k=this;f=k.baseSeries;g=b.xAxis.length;h=b.yAxis.length;var m=f&&f[0]&&f[0].xAxis||b.xAxis[0]||{options:{}};b.isDirtyBox=!0;k.navigatorEnabled?(k.xAxis=new a(b,L({breaks:m.options.breaks,
ordinal:m.options.ordinal},d.xAxis,{id:"navigator-x-axis",yAxis:"navigator-y-axis",isX:!0,type:"datetime",index:g,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:0,maxPadding:0,zoomEnabled:!1},b.inverted?{offsets:[l,0,-l,0],width:c}:{offsets:[0,-l,0,l],height:c})),k.yAxis=new a(b,L(d.yAxis,{id:"navigator-y-axis",alignTicks:!1,offset:0,index:h,isInternal:!0,reversed:H(d.yAxis&&d.yAxis.reversed,b.yAxis[0]&&b.yAxis[0].reversed,!1),zoomEnabled:!1},b.inverted?{width:c}:
{height:c})),f||d.series.data?k.updateNavigatorSeries(!1):0===b.series.length&&(k.unbindRedraw=e(b,"beforeRedraw",function(){0<b.series.length&&!k.series&&(k.setBaseSeries(),k.unbindRedraw())})),k.reversedExtremes=b.inverted&&!k.xAxis.reversed||!b.inverted&&k.xAxis.reversed,k.renderElements(),k.addMouseEvents()):(k.xAxis={chart:b,navigatorAxis:{fake:!0},translate:function(a,c){var d=b.xAxis[0],e=d.getExtremes(),f=d.len-2*l,g=E("min",d.options.min,e.dataMin);d=E("max",d.options.max,e.dataMax)-g;return c?
a*d/f+g:f*(a-g)/d},toPixels:function(a){return this.translate(a)},toValue:function(a){return this.translate(a,!0)}},k.xAxis.navigatorAxis.axis=k.xAxis,k.xAxis.navigatorAxis.toFixedRange=n.AdditionsClass.prototype.toFixedRange.bind(k.xAxis.navigatorAxis));b.options.scrollbar.enabled&&(b.scrollbar=k.scrollbar=new B(b.renderer,L(b.options.scrollbar,{margin:k.navigatorEnabled?0:10,vertical:b.inverted}),b),e(k.scrollbar,"changed",function(a){var c=k.size,d=c*this.to;c*=this.from;k.hasDragged=k.scrollbar.hasDragged;
k.render(0,0,c,d);(b.options.scrollbar.liveRedraw||"mousemove"!==a.DOMType&&"touchmove"!==a.DOMType)&&setTimeout(function(){k.onMouseUp(a)})}));k.addBaseSeriesEvents();k.addChartEvents()};h.prototype.getUnionExtremes=function(a){var b=this.chart.xAxis[0],c=this.xAxis,d=c.options,e=b.options,f;a&&null===b.dataMin||(f={dataMin:H(d&&d.min,E("min",e.min,b.dataMin,c.dataMin,c.min)),dataMax:H(d&&d.max,E("max",e.max,b.dataMax,c.dataMax,c.max))});return f};h.prototype.setBaseSeries=function(a,b){var c=this.chart,
d=this.baseSeries=[];a=a||c.options&&c.options.navigator.baseSeries||(c.series.length?F(c.series,function(a){return!a.options.isInternal}).index:0);(c.series||[]).forEach(function(b,c){b.options.isInternal||!b.options.showInNavigator&&(c!==a&&b.options.id!==a||!1===b.options.showInNavigator)||d.push(b)});this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,b)};h.prototype.updateNavigatorSeries=function(a,c){var d=this,e=d.chart,f=d.baseSeries,g,h,l=d.navigatorOptions.series,k,
m={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:"navigator-x-axis",yAxis:"navigator-y-axis",showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},n=d.series=(d.series||[]).filter(function(a){var b=a.baseSeries;return 0>f.indexOf(b)?(b&&(C(b,"updatedData",d.updatedDataHandler),delete b.navigatorSeries),a.chart&&a.destroy(),!1):!0});f&&f.length&&f.forEach(function(a){var p=a.navigatorSeries,q=b({color:a.color,visible:a.visible},I(l)?v.navigator.series:
l);p&&!1===d.navigatorOptions.adaptToUpdatedData||(m.name="Navigator "+f.length,g=a.options||{},k=g.navigatorOptions||{},h=L(g,m,q,k),h.pointRange=H(q.pointRange,k.pointRange,v.plotOptions[h.type||"line"].pointRange),q=k.data||q.data,d.hasNavigatorData=d.hasNavigatorData||!!q,h.data=q||g.data&&g.data.slice(0),p&&p.options?p.update(h,c):(a.navigatorSeries=e.initSeries(h),a.navigatorSeries.baseSeries=a,n.push(a.navigatorSeries)))});if(l.data&&(!f||!f.length)||I(l))d.hasNavigatorData=!1,l=M(l),l.forEach(function(a,
b){m.name="Navigator "+(n.length+1);h=L(v.navigator.series,{color:e.series[b]&&!e.series[b].options.isInternal&&e.series[b].color||e.options.colors[b]||e.options.colors[0]},m,a);h.data=a.data;h.data&&(d.hasNavigatorData=!0,n.push(e.initSeries(h)))});a&&this.addBaseSeriesEvents()};h.prototype.addBaseSeriesEvents=function(){var a=this,b=a.baseSeries||[];b[0]&&b[0].xAxis&&e(b[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes);b.forEach(function(b){e(b,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,
!1)});e(b,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)});!1!==this.navigatorOptions.adaptToUpdatedData&&b.xAxis&&e(b,"updatedData",this.updatedDataHandler);e(b,"remove",function(){this.navigatorSeries&&(d(a.series,this.navigatorSeries),q(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)})},this)};h.prototype.getBaseSeriesMin=function(a){return this.baseSeries.reduce(function(a,b){return Math.min(a,b.xData?b.xData[0]:a)},a)};
h.prototype.modifyNavigatorAxisExtremes=function(){var a=this.xAxis,b;"undefined"!==typeof a.getExtremes&&(!(b=this.getUnionExtremes(!0))||b.dataMin===a.min&&b.dataMax===a.max||(a.min=b.dataMin,a.max=b.dataMax))};h.prototype.modifyBaseAxisExtremes=function(){var a=this.chart.navigator,b=this.getExtremes(),c=b.dataMin,d=b.dataMax;b=b.max-b.min;var e=a.stickToMin,f=a.stickToMax,g=H(this.options.overscroll,0),h=a.series&&a.series[0],k=!!this.setExtremes;if(!this.eventArgs||"rangeSelectorButton"!==this.eventArgs.trigger){if(e){var m=
c;var n=m+b}f&&(n=d+g,e||(m=Math.max(c,n-b,a.getBaseSeriesMin(h&&h.xData?h.xData[0]:-Number.MAX_VALUE))));k&&(e||f)&&K(m)&&(this.min=this.userMin=m,this.max=this.userMax=n)}a.stickToMin=a.stickToMax=null};h.prototype.updatedDataHandler=function(){var a=this.chart.navigator,b=this.navigatorSeries,c=a.getBaseSeriesMin(this.xData[0]);a.stickToMax=a.reversedExtremes?0===Math.round(a.zoomedMin):Math.round(a.zoomedMax)>=Math.round(a.size);a.stickToMin=K(this.xAxis.min)&&this.xAxis.min<=c&&(!this.chart.fixedRange||
!a.stickToMax);b&&!a.hasNavigatorData&&(b.options.pointStart=this.xData[0],b.setData(this.options.data,!1,null,!1))};h.prototype.addChartEvents=function(){this.eventsToUnbind||(this.eventsToUnbind=[]);this.eventsToUnbind.push(e(this.chart,"redraw",function(){var a=this.navigator,b=a&&(a.baseSeries&&a.baseSeries[0]&&a.baseSeries[0].xAxis||this.xAxis[0]);b&&a.render(b.min,b.max)}),e(this.chart,"getMargins",function(){var a=this.navigator,b=a.opposite?"plotTop":"marginBottom";this.inverted&&(b=a.opposite?
"marginRight":"plotLeft");this[b]=(this[b]||0)+(a.navigatorEnabled||!this.inverted?a.outlineHeight:0)+a.navigatorOptions.margin}))};h.prototype.destroy=function(){this.removeEvents();this.xAxis&&(d(this.chart.xAxis,this.xAxis),d(this.chart.axes,this.xAxis));this.yAxis&&(d(this.chart.yAxis,this.yAxis),d(this.chart.axes,this.yAxis));(this.series||[]).forEach(function(a){a.destroy&&a.destroy()});"series xAxis yAxis shades outline scrollbarTrack scrollbarRifles scrollbarGroup scrollbar navigatorGroup rendered".split(" ").forEach(function(a){this[a]&&
this[a].destroy&&this[a].destroy();this[a]=null},this);[this.handles].forEach(function(a){c(a)},this)};return h}();r.Navigator||(r.Navigator=y,n.compose(a),e(u,"beforeShowResetZoom",function(){var a=this.options,b=a.navigator,c=a.rangeSelector;if((b&&b.enabled||c&&c.enabled)&&(!f&&"x"===a.chart.zoomType||f&&"x"===a.chart.pinchType))return!1}),e(u,"beforeRender",function(){var a=this.options;if(a.navigator.enabled||a.scrollbar.enabled)this.scroller=this.navigator=new y(this)}),e(u,"afterSetChartSize",
function(){var a=this.legend,b=this.navigator;if(b){var c=a&&a.options;var d=b.xAxis;var e=b.yAxis;var f=b.scrollbarHeight;this.inverted?(b.left=b.opposite?this.chartWidth-f-b.height:this.spacing[3]+f,b.top=this.plotTop+f):(b.left=this.plotLeft+f,b.top=b.navigatorOptions.top||this.chartHeight-b.height-f-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(c&&"bottom"===c.verticalAlign&&"proximate"!==c.layout&&c.enabled&&!c.floating?a.legendHeight+H(c.margin,
10):0)-(this.titleOffset?this.titleOffset[2]:0));d&&e&&(this.inverted?d.options.left=e.options.left=b.left:d.options.top=e.options.top=b.top,d.setAxisSize(),e.setAxisSize())}}),e(u,"update",function(a){var b=a.options.navigator||{},c=a.options.scrollbar||{};this.navigator||this.scroller||!b.enabled&&!c.enabled||(L(!0,this.options.navigator,b),L(!0,this.options.scrollbar,c),delete a.options.navigator,delete a.options.scrollbar)}),e(u,"afterUpdate",function(a){this.navigator||this.scroller||!this.options.navigator.enabled&&
!this.options.scrollbar.enabled||(this.scroller=this.navigator=new y(this),H(a.redraw,!0)&&this.redraw(a.animation))}),e(u,"afterAddSeries",function(){this.navigator&&this.navigator.setBaseSeries(null,!1)}),e(D,"afterUpdate",function(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}),u.prototype.callbacks.push(function(a){var b=a.navigator;b&&a.xAxis[0]&&(a=a.xAxis[0].getExtremes(),b.render(a.min,a.max))}));r.Navigator=y;return r.Navigator});F(a,"masters/modules/gantt.src.js",
[],function(){})});
//# sourceMappingURL=gantt.js.map