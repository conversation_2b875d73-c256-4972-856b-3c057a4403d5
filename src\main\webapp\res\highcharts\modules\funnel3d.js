/*
 Highcharts JS v9.0.1 (2021-02-15)

 Highcharts funnel module

 (c) 2010-2021 Kacper <PERSON>j

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/modules/funnel3d",["highcharts","highcharts/highcharts-3d","highcharts/modules/cylinder"],function(u){a(u);a.Highcharts=u;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){function u(a,f,l,m){a.hasOwnProperty(f)||(a[f]=m.apply(null,l))}a=a?a._modules:{};u(a,"Series/Funnel3D/Funnel3DPoint.js",[a["Core/Series/SeriesRegistry.js"],
a["Core/Utilities.js"]],function(a,f){var l=this&&this.__extends||function(){var a=function(e,n){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,e){a.__proto__=e}||function(a,e){for(var h in e)e.hasOwnProperty(h)&&(a[h]=e[h])};return a(e,n)};return function(e,n){function f(){this.constructor=e}a(e,n);e.prototype=null===n?Object.create(n):(f.prototype=n.prototype,new f)}}();f=f.extend;a=function(a){function e(){var e=null!==a&&a.apply(this,arguments)||this;e.dlBoxRaw=void 0;e.options=
void 0;e.series=void 0;e.y=void 0;return e}l(e,a);return e}(a.seriesTypes.column.prototype.pointClass);f(a.prototype,{shapeType:"funnel3d"});return a});u(a,"Series/Funnel3D/Funnel3DComposition.js",[a["Core/Color/Color.js"],a["Core/Globals.js"],a["Core/Utilities.js"]],function(a,f,l){var m=a.parse,e=f.charts;a=f.Renderer.prototype;var n=a.cuboidPath;a=a.elements3d;var u=l.error,H=l.extend,h=l.merge;a.funnel3d=h(a.cuboid,{parts:"top bottom frontUpper backUpper frontLower backLower rightUpper rightLower".split(" "),
mainParts:["top","bottom"],sideGroups:["upperGroup","lowerGroup"],sideParts:{upperGroup:["frontUpper","backUpper","rightUpper"],lowerGroup:["frontLower","backLower","rightLower"]},pathType:"funnel3d",opacitySetter:function(b){var d=this,a=d.parts,c=f.charts[d.renderer.chartIndex],k="group-opacity-"+b+"-"+c.index;d.parts=d.mainParts;d.singleSetterForParts("opacity",b);d.parts=a;c.renderer.filterId||(c.renderer.definition({tagName:"filter",attributes:{id:k},children:[{tagName:"feComponentTransfer",
children:[{tagName:"feFuncA",attributes:{type:"table",tableValues:"0 "+b}}]}]}),d.sideGroups.forEach(function(a){d[a].attr({filter:"url(#"+k+")"})}),d.renderer.styledMode&&(c.renderer.definition({tagName:"style",textContent:".highcharts-"+k+" {filter:url(#"+k+")}"}),d.sideGroups.forEach(function(d){d.addClass("highcharts-"+k)})));return d},fillSetter:function(a){var d=this,b=m(a),c=b.rgba[3],k={top:m(a).brighten(.1).get(),bottom:m(a).brighten(-.2).get()};1>c?(b.rgba[3]=1,b=b.get("rgb"),d.attr({opacity:c})):
b=a;b.linearGradient||b.radialGradient||!d.gradientForSides||(b={linearGradient:{x1:0,x2:1,y1:1,y2:1},stops:[[0,m(a).brighten(-.2).get()],[.5,a],[1,m(a).brighten(-.2).get()]]});b.linearGradient?d.sideGroups.forEach(function(a){var t=d[a].gradientBox,c=b.linearGradient,v=h(b,{linearGradient:{x1:t.x+c.x1*t.width,y1:t.y+c.y1*t.height,x2:t.x+c.x2*t.width,y2:t.y+c.y2*t.height}});d.sideParts[a].forEach(function(a){k[a]=v})}):(h(!0,k,{frontUpper:b,backUpper:b,rightUpper:b,frontLower:b,backLower:b,rightLower:b}),
b.radialGradient&&d.sideGroups.forEach(function(a){var b=d[a].gradientBox,c=b.x+b.width/2,v=b.y+b.height/2,k=Math.min(b.width,b.height);d.sideParts[a].forEach(function(a){d[a].setRadialReference([c,v,k])})}));d.singleSetterForParts("fill",null,k);d.color=d.fill=a;b.linearGradient&&[d.frontLower,d.frontUpper].forEach(function(a){(a=(a=a.element)&&d.renderer.gradients[a.gradient])&&"userSpaceOnUse"!==a.attr("gradientUnits")&&a.attr({gradientUnits:"userSpaceOnUse"})});return d},adjustForGradient:function(){var a=
this,d;a.sideGroups.forEach(function(b){var c={x:Number.MAX_VALUE,y:Number.MAX_VALUE},k={x:-Number.MAX_VALUE,y:-Number.MAX_VALUE};a.sideParts[b].forEach(function(b){d=a[b].getBBox(!0);c={x:Math.min(c.x,d.x),y:Math.min(c.y,d.y)};k={x:Math.max(k.x,d.x+d.width),y:Math.max(k.y,d.y+d.height)}});a[b].gradientBox={x:c.x,width:k.x-c.x,y:c.y,height:k.y-c.y}})},zIndexSetter:function(){this.finishedOnAdd&&this.adjustForGradient();return this.renderer.Element.prototype.zIndexSetter.apply(this,arguments)},onAdd:function(){this.adjustForGradient();
this.finishedOnAdd=!0}});H(f.Renderer.prototype,{funnel3d:function(a){var b=this.element3d("funnel3d",a),e=this.styledMode,c={"stroke-width":1,stroke:"none"};b.upperGroup=this.g("funnel3d-upper-group").attr({zIndex:b.frontUpper.zIndex}).add(b);[b.frontUpper,b.backUpper,b.rightUpper].forEach(function(a){e||a.attr(c);a.add(b.upperGroup)});b.lowerGroup=this.g("funnel3d-lower-group").attr({zIndex:b.frontLower.zIndex}).add(b);[b.frontLower,b.backLower,b.rightLower].forEach(function(a){e||a.attr(c);a.add(b.lowerGroup)});
b.gradientForSides=a.gradientForSides;return b},funnel3dPath:function(a){this.getCylinderEnd||u("A required Highcharts module is missing: cylinder.js",!0,e[this.chartIndex]);var b=e[this.chartIndex],f=a.alphaCorrection=90-Math.abs(b.options.chart.options3d.alpha%180-90),c=n.call(this,h(a,{depth:a.width,width:(a.width+a.bottom.width)/2})),k=c.isTop,q=!c.isFront,t=!!a.middle,y=this.getCylinderEnd(b,h(a,{x:a.x-a.width/2,z:a.z-a.width/2,alphaCorrection:f})),v=a.bottom.width,I=h(a,{width:v,x:a.x-v/2,z:a.z-
v/2,alphaCorrection:f}),l=this.getCylinderEnd(b,I,!0),z=v,F=I,g=l,m=l;t&&(z=a.middle.width,F=h(a,{y:a.y+a.middle.fraction*a.height,width:z,x:a.x-z/2,z:a.z-z/2}),g=this.getCylinderEnd(b,F,!1),m=this.getCylinderEnd(b,F,!1));c={top:y,bottom:l,frontUpper:this.getCylinderFront(y,g),zIndexes:{group:c.zIndexes.group,top:0!==k?0:3,bottom:1!==k?0:3,frontUpper:q?2:1,backUpper:q?1:2,rightUpper:q?2:1}};c.backUpper=this.getCylinderBack(y,g);y=1!==Math.min(z,a.width)/Math.max(z,a.width);c.rightUpper=this.getCylinderFront(this.getCylinderEnd(b,
h(a,{x:a.x-a.width/2,z:a.z-a.width/2,alphaCorrection:y?-f:0}),!1),this.getCylinderEnd(b,h(F,{alphaCorrection:y?-f:0}),!t));t&&(y=1!==Math.min(z,v)/Math.max(z,v),h(!0,c,{frontLower:this.getCylinderFront(m,l),backLower:this.getCylinderBack(m,l),rightLower:this.getCylinderFront(this.getCylinderEnd(b,h(I,{alphaCorrection:y?-f:0}),!0),this.getCylinderEnd(b,h(F,{alphaCorrection:y?-f:0}),!1)),zIndexes:{frontLower:q?2:1,backLower:q?1:2,rightLower:q?1:2}}));return c}})});u(a,"Series/Funnel3D/Funnel3DSeries.js",
[a["Series/Funnel3D/Funnel3DPoint.js"],a["Core/Globals.js"],a["Extensions/Math3D.js"],a["Core/Series/SeriesRegistry.js"],a["Core/Utilities.js"]],function(a,f,l,m,e){var n=this&&this.__extends||function(){var a=function(b,c){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])};return a(b,c)};return function(b,c){function d(){this.constructor=b}a(b,c);b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,
new d)}}();f=f.noop;var u=l.perspective,H=m.series,h=m.seriesTypes.column,b=e.extend,d=e.merge,E=e.pick,c=e.relativeLength;l=function(a){function e(){var b=null!==a&&a.apply(this,arguments)||this;b.center=void 0;b.data=void 0;b.options=void 0;b.points=void 0;return b}n(e,a);e.prototype.alignDataLabel=function(a,b,c){var d=a.dlBoxRaw,e=this.chart.inverted,f=a.plotY>E(this.translatedThreshold,this.yAxis.len),k=E(c.inside,!!this.options.stacking),g={x:d.x,y:d.y,height:0};c.align=E(c.align,!e||k?"center":
f?"right":"left");c.verticalAlign=E(c.verticalAlign,e||k?"middle":f?"top":"bottom");"top"!==c.verticalAlign&&(g.y+=d.bottom/("bottom"===c.verticalAlign?1:2));g.width=this.getWidthAt(g.y);this.options.reversed&&(g.width=d.fullWidth-g.width);k?g.x-=g.width/2:"left"===c.align?(c.align="right",g.x-=1.5*g.width):"right"===c.align?(c.align="left",g.x+=g.width/2):g.x-=g.width/2;a.dlBox=g;h.prototype.alignDataLabel.apply(this,arguments)};e.prototype.bindAxes=function(){H.prototype.bindAxes.apply(this,arguments);
b(this.xAxis.options,{gridLineWidth:0,lineWidth:0,title:null,tickPositions:[]});b(this.yAxis.options,{gridLineWidth:0,title:null,labels:{enabled:!1}})};e.prototype.translate=function(){H.prototype.translate.apply(this,arguments);var a=0,d=this.chart,e=this.options,f=e.reversed,k=e.ignoreHiddenPoint,h=d.plotWidth,l=d.plotHeight,g=0,m=e.center,n=c(m[0],h),q=c(m[1],l),J=c(e.width,h),w,A,r=c(e.height,l),K=c(e.neckWidth,h),L=c(e.neckHeight,l),D=q-r/2+r-L;h=this.data;var B,M,x,C,N,G,p;this.getWidthAt=A=
function(a){var b=q-r/2;return a>D||r===L?K:K+(J-K)*(1-(a-b)/(r-L))};this.center=[n,q,r];this.centerX=n;h.forEach(function(b){k&&!1===b.visible||(a+=b.y)});h.forEach(function(c){N=null;B=a?c.y/a:0;x=q-r/2+g*r;C=x+B*r;w=A(x);G=C-x;p={gradientForSides:E(c.options.gradientForSides,e.gradientForSides),x:n,y:x,height:G,width:w,z:1,top:{width:w}};w=A(C);p.bottom={fraction:B,width:w};x>=D?p.isCylinder=!0:C>D&&(N=C,w=A(D),C=D,p.bottom.width=w,p.middle={fraction:G?(D-x)/G:0,width:w});f&&(p.y=x=q+r/2-(g+B)*
r,p.middle&&(p.middle.fraction=1-(G?p.middle.fraction:0)),w=p.width,p.width=p.bottom.width,p.bottom.width=w);c.shapeArgs=b(c.shapeArgs,p);c.percentage=100*B;c.plotX=n;c.plotY=f?q+r/2-(g+B/2)*r:(x+(N||C))/2;M=u([{x:n,y:c.plotY,z:f?-(J-A(c.plotY))/2:-A(c.plotY)/2}],d,!0)[0];c.tooltipPos=[M.x,M.y];c.dlBoxRaw={x:n,width:A(c.plotY),y:x,bottom:p.height,fullWidth:J};k&&!1===c.visible||(g+=B)})};e.defaultOptions=d(h.defaultOptions,{center:["50%","50%"],width:"90%",neckWidth:"30%",height:"100%",neckHeight:"25%",
reversed:!1,gradientForSides:!0,animation:!1,edgeWidth:0,colorByPoint:!0,showInLegend:!1,dataLabels:{align:"right",crop:!1,inside:!1,overflow:"allow"}});return e}(h);b(l.prototype,{pointClass:a,translate3dShapes:f});m.registerSeriesType("funnel3d",l);"";return l});u(a,"masters/modules/funnel3d.src.js",[],function(){})});
//# sourceMappingURL=funnel3d.js.map