li {
    display: list-item;
    text-align: -webkit-match-parent;
}
user agent stylesheet
ul {
    list-style-type: disc;
}

.searchul {
    margin: 0;
	margin-left: -40px;
    margin-top: 5px;
}
.searchul .lileft {
    float: left;
    display: block;
    line-height: 23px;
    min-height: 28px;
    padding-right: 5px;
}

.btn-mini {
    padding: 0 5px;
    line-height: 22px;
    border-width: 2px;
    font-size: 12px;
}

.btn-minier {
    padding: 0 2px;
    line-height: 16px;
    border-width: 2px;
    font-size: 14px;
}
.bt-form {
    height: 100%;
}
.pb70 {
    padding-bottom: 70px;
}
.pd20 {
    padding: 20px;
}
.line {
    padding: 5px 0;
}
.line .tname {
    display: block;
    float: left;
    height: 32px;
    line-height: 32px;
    overflow: hidden;
    padding-right: 20px;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100px;
}
.bt-input-text {
    border: 1px solid #ccc;
    height: 30px;
    line-height: 30px;
    padding-left: 5px;
    border-radius: 2px;
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.bt-form-submit-btn {
    background: #f6f8f8;
    border-top: 1px solid #edf1f2;
    bottom: 0;
    left: 0;
    padding: 8px 20px 10px;
    position: absolute;
    text-align: right;
    width: 100%;
}
.bt-form-submit-btn .btn:first-child {
    margin-right: 4px;
}
.btn-danger, .btn-danger:focus {
    background-color: #d15b47!important;
    border-color: #d15b47;
}
.btn {
    vertical-align: inherit;
}
.btn-success {
    color: #fff;
    background-color: #20a53a;
    border-color: #20a53a;
}
.btn-group-sm>.btn, .btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

span.input-icon {
    display: inline-block;
}

.input-icon {
    position: relative;
    line-height: 20px;
}
.input-icon>input {
    padding-left: 24px;
    padding-right: 6px;
}
.input-icon>[class*=icon-] {
    padding: 0 3px;
    z-index: 2;
    position: absolute;
    top: 1px;
    bottom: 1px;
    left: 3px;
    line-height: 24px;
    display: inline-block;
    color: #909090;
    font-size: 16px;
}