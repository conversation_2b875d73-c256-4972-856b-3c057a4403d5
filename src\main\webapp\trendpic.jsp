<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>任务数据</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="res/css/layui.css" type="text/css" rel="stylesheet"> 
    <link href="res/layui-v2.9.2/layui/css/layui.css" rel="stylesheet" />
    <link href="res/css/mytab.css" type="text/css" rel="stylesheet">
    <link href="res/css/hr.css" type="text/css" rel="stylesheet">
    <meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<meta http-equiv="Cache-control" content="no-cache">
	<meta http-equiv="Cache" content="no-cache">	
</head>
<body >
<div id="zzbcall">  </div>
<script type="text/javascript" src="res/jquery.min.js"></script>
 <script type="text/javascript" src="res/layui.js"></script> 
<script type="text/javascript" src="res/waterMark.js"></script>
<script type="text/javascript" src="res/highcharts/highcharts.js"></script>
<script type="text/javascript" src="res/xm-select/treeTable.js"></script>
<script type="text/javascript" src="res/xm-select/xm-select.js"></script>
<script type="text/javascript" src="res/echarts.js"></script>
<script type="text/javascript">
	console.log( eval('('+parent.mytrenddata+')'));//父界面传递的任务信息);
	var patredata = eval('('+parent.mytrenddata+')');
	console.log(patredata);
	console.log(patredata.index.split(":")[0]);
	document.getElementById("zzbcall").innerHTML = "";
	$.ajax({
		   type: "post",
		   //url:'https://iwork.zx.zte.com.cn/iWorkDataHandlerAll/QueryAutoDataAnalysisServlet',//20表+4个图
		   //url:'chartsdata2.json',
		    url:'https://iwork.zx.zte.com.cn/iWorkDataHandlerAll/iWork2ChartHandler3',
		   //async:false,
		     data: {
		    	 jobid:patredata.jobid,
		    	 kpitemplate:patredata.kpitemplate,
		    	 index:encodeURI(patredata.index.split(":")[0]),
		    	 build:"",
		    	 newcase:encodeURI(patredata.newcase),
		    	 newcell:patredata.newcell},
		   //data: {jobid:padata.taskid,kpitemplate:padata.tem},
		    dataType:'json',
		   success: function(data){
			console.log(data[1].length);
			for (let icc= 0; icc<data[1].length; icc++) {
				if (icc% 2 === 0) {//偶数
					$('#zzbcall').append("<div id ='chart"+icc+"' class='layui-col-md12' style='padding:1%;height: 430px'>");	
				}else{//奇数
					$('#zzbcall').append("<div id ='chart"+icc+"' class='layui-col-md12' style='padding:1%;height: 430px'>");
				}
			}
			for(let icf=0;icf<data[1].length;icf++){
				console.log(data[1][icf].y[0].max);
					const nullPositions = data[1][icf].case.reduce((acc, curr, index) => {
					  if (curr === null) {
					    acc.push(index);
					  }
					  return acc;
					}, []);
					nullPositions.unshift(0)	;
					var listband = [];
					for(let icb=0;icb<nullPositions.length-1;icb++){
							var cfband={from:nullPositions[icb],to:nullPositions[icb+1],color:'white',borderWidth:1,borderColor:'rgba(68, 170, 213, 0.1)',label:{text:''+data[1][icf].case[nullPositions[icb+1]-1]+''}}
							listband.push(cfband);
					}		  
				console.log(nullPositions);				
				//动态增加图	
				//渲染增加的图：
				Highcharts.chart('chart'+icf+'', {
				chart: {
			        backgroundColor: '#fcfcfc'
			    },
				title: {
						text: ""+data[1][icf].title+"",
				},
				credits: {
		        enabled: true,
		        text: 'iWork',
		        position: {
		            x: 0,
		            y: -20
		        },
		        opacity: 0.5
		    },
				xAxis: [{
						categories: data[1][icf].x,
						labels: {
							  formatter: function() {
								if(Number.isInteger(this.value)){
										this.value = ""; 
								}else{
									
								}
		           return this.value
		        },
						rotation:90,
			         style :{
				fontSize: 8
				}
						},
				 plotLines:listband,
				 lineColor:'black'		
				}],
				yAxis: [{ // Primary yAxis
						max:data[1][icf].y[0].max,
						min:data[1][icf].y[0].min,
						tickInterval:data[1][icf].y[0].tickInterval,
						labels: {
								format: '{value}',
								style: {
										color: Highcharts.getOptions().colors[0]
								}
						},
						title: {
								text: data[1][icf].y[0].label,
								style: {
										color: Highcharts.getOptions().colors[0]
								}
						}
						
				}
					],
				tooltip: {
				},
				series:data[1][icf].series,
		
		});	
			}
		}
		}) 
</script>
<script type="text/javascript">
	$(document).ready(function()
	{
		var user1 = "iWork";
		var user2 = "性能研发四部";
		var user3 = "ZTE";
		watermark({"watermark_txt0":user1,"watermark_txt1":user2,"watermark_txt2":user3});
	});
			
</script>  
</body>
</html>