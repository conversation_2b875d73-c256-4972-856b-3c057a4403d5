package utilvmax;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import Dao.DaoCMCC;
import Dao.ResultSetToJsonArray;

public class vmax_function {
	//从数据库中读取数据并存入double数组
	JSONArray comparison=   new  JSONArray();
	
	public JSONArray vmaxset(String sql) {
		DaoCMCC dbu= new DaoCMCC();
		Connection con= dbu.getConnection();
		JSONArray result = new JSONArray();
		try {
			Statement sta = con.createStatement();
			ResultSet rs=sta.executeQuery(sql);
			ResultSetMetaData rsmd = rs.getMetaData();
			int count = rsmd.getColumnCount();
			for(int i=1;i<=count-19;i++) {
				result.put(0);
			}
			int rownum=0;
			while(rs.next()) {
				rownum++;
				for(int i=1;i<=count;i++) {
					if(i>=6&&i<=63) {
						result.put(i-6,result.getDouble(i-6)+ rs.getDouble(i));
					}
				}
			}
			if(rownum!=0) {
				for(int i=0;i<result.length();i++) {
					 result.put(i,result.getDouble(i)/rownum);
					}
			}
			
			
			rs.close();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dbu.close();
		return result;
	}
	
	
	//计算差值以及幅度
	public  JSONArray score(JSONArray dou1,JSONArray dou2,String v1,String v2,String usecase1,String usecase2,String jobid) {
		DaoCMCC dao= new DaoCMCC();
		
		String  sql="SELECT * FROM iwork2.vmaxcount  order by id+0;";
		ResultSet rs = dao.executeQuery(sql);
		JSONArray kpicountArray = new JSONArray();
		try {
			kpicountArray = ResultSetToJsonArray.resultSetToJsonArray(rs);
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		int normal =0;
		int improve = 0;
		int bad = 0;
		for(int i=0;i<dou1.length();i++) {
			JSONObject object = kpicountArray.getJSONObject(i);
			String formulamode = object.getString("formulamode");
			String trend = object.getString("trend");

			double fudu = object.getDouble("fudu");
			if(formulamode.equals("0")) {
				double tempfudu = 0.0;
				if(dou1.getDouble(i)!=0) {
					tempfudu =  (dou2.getDouble(i)-dou1.getDouble(i) )/dou1.getDouble(i);
				}else {
					tempfudu = 0.0;
				}
				//判断是否异常项
				if(Math.abs(tempfudu) > fudu) {
					//异常项 先判断正向或反向
					if(trend.equals("up")) {
						if(tempfudu>0) {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}else {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}
					}else {
						if(tempfudu>0) {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}else {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}
					}
				}else {
					normal++;
					comparison.put("持平项");
					object.put("comparison", "持平项");
				}
				
			}else {
				double tempchazhi=dou2.getDouble(i)-dou1.getDouble(i) ;
				if(Math.abs(tempchazhi) >fudu) {
					if(trend.equals("up")) {
						if(tempchazhi>0) {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}else {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}
					}else {
						if(tempchazhi>0) {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}else {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}
					}
				}else {
					normal++;
					comparison.put("持平项");
					object.put("comparison", "持平项");
				}
			}
		}

		int score=100;
		String tempflag = "0";
		for(int i=0;i<kpicountArray.length();i++) {
			JSONObject object = kpicountArray.getJSONObject(i);
			String comparison = object.getString("comparison");
			String deduction = object.getString("deduction");

			if(comparison.equals("恶化项")) {
				if(deduction.equals("")){
					
				}else {
					Integer deductionint = Integer.valueOf(deduction); 
					score = score+deductionint;
				}
			}
			
		}
		JSONArray resultArray = new JSONArray();
		resultArray.put(v1).put(v2).put(usecase1).put(usecase2).put(jobid).put(improve).put(normal).put(bad).put(score);
	    dao.close();
	    return resultArray;
	}
	

	//计算差值以及幅度
	public  void firstjisuan(JSONArray dou1,JSONArray dou2, String citeam, String devName, String TestVersion, 
			String contrastTestVersion, String ptype,String jobid,String type) {
		DaoCMCC dao= new DaoCMCC();
		
		
		String  sql="SELECT * FROM iwork2.vmaxcount  order by id+0;";
		
		ResultSet rs = dao.executeQuery(sql);
		JSONArray kpicountArray = new JSONArray();
		try {
			kpicountArray = ResultSetToJsonArray.resultSetToJsonArray(rs);
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		int normal =0;
		int improve = 0;
		int bad = 0;
		for(int i=0;i<dou1.length();i++) {
			JSONObject object = kpicountArray.getJSONObject(i);
			String formulamode = object.getString("formulamode");
			String trend = object.getString("trend");

			double fudu = object.getDouble("fudu");
			if(formulamode.equals("0")) {
				double tempfudu = 0.0;
				if(dou1.getDouble(i)!=0) {
					tempfudu =  (dou2.getDouble(i)-dou1.getDouble(i) )/dou1.getDouble(i);
				}else {
					tempfudu = 0.0;
				}
				//判断是否异常项
				if(Math.abs(tempfudu) > fudu) {
					//异常项 先判断正向或反向
					if(trend.equals("up")) {
						if(tempfudu>0) {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}else {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}
					}else {
						if(tempfudu>0) {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}else {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}
					}
				}else {
					normal++;
					comparison.put("持平项");
					object.put("comparison", "持平项");
				}
				
			}else {
				double tempchazhi=dou2.getDouble(i)-dou1.getDouble(i) ;
				if(Math.abs(tempchazhi) >fudu) {
					if(trend.equals("up")) {
						if(tempchazhi>0) {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}else {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}
					}else {
						if(tempchazhi>0) {
							bad++;
							comparison.put("恶化项");
							object.put("comparison","恶化项");
						}else {
							improve++;
							comparison.put("提升项");
							object.put("comparison", "提升项");
						}
					}
				}else {
					normal++;
					comparison.put("持平项");
					object.put("comparison", "持平项");
				}
			}
		}

		int score=100;
		String tempflag = "0";
		for(int i=0;i<kpicountArray.length();i++) {
			JSONObject object = kpicountArray.getJSONObject(i);
			String comparison = object.getString("comparison");
			String deduction = object.getString("deduction");

			if(comparison.equals("恶化项")) {
				if(deduction.equals("")){
					
				}else {
					Integer deductionint = Integer.valueOf(deduction); 
					score = score+deductionint;
				}
			}
			
		}
		String ifpass="";
//		if(tempflag.equals("1")) {
//			ifpass="一票否决";
//		}else if(score<80) {
//			ifpass="不通过";
//		}else {
//			ifpass="通过";
//		}
		String updateSql="insert into kpiscore(TestVersion,contrastTestVersion,team,devName,improve2,normal2,bad2,score2,"
				+ "ptype,date,ifpass2,ifnew,jobid,type) values";
		Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    String date=sbf.format(d);
	    updateSql+= "('"+TestVersion+"','"+contrastTestVersion+"','"+citeam+"','"+devName+"','"+improve+"','"+normal+"',"
	    		+ "'"+bad+"','"+score+"','"+ptype+"','"+date+"','"+ifpass+"','1','"+jobid+"','"+type+"')";
	    updateSql+=" on duplicate key update TestVersion=values(TestVersion),contrastTestVersion=values(contrastTestVersion),"
	    		+ "team=values(team),devName=values(devName),improve2=values(improve2),normal2=values(normal2),bad2=values(bad2),"
	    		+ "score2=values(score2),ptype=values(ptype),date=values(date),ifpass2=values(ifpass2),ifnew=values(ifnew),"
	    		+ "jobid=values(jobid),type=values(type);";
	    dao.execute(updateSql);
//	    System.out.println(updateSql);
	    dao.close();
	}
	
	//计算差值以及幅度
		public  JSONArray show(JSONArray dou1,JSONArray dou2) {
			DaoCMCC dao= new DaoCMCC();
			String  sql="SELECT * FROM iwork2.vmaxcount  order by id+0;";
			
			ResultSet rs = dao.executeQuery(sql);
			JSONArray kpicountArray = new JSONArray();
			try {
				kpicountArray = ResultSetToJsonArray.resultSetToJsonArray(rs);
			} catch (JSONException | SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

			
			int  improve=0;
			int  bad=0;
			int  normal=0;

			for(int i=0;i<dou1.length();i++) {
				JSONObject object = kpicountArray.getJSONObject(i);
				String formulamode = object.getString("formulamode");
				String trend = object.getString("trend");
				object.put("old",dou1.getDouble(i));
				object.put("new", dou2.getDouble(i));
				double fudu = object.getDouble("fudu");
				if(formulamode.equals("0")) {
					double tempfudu = 0.0;
					double tempcahzhi = 0.0;

					if(dou1.getDouble(i)!=0) {
						tempcahzhi =  dou2.getDouble(i)-dou1.getDouble(i) ;
						tempfudu =  (dou2.getDouble(i)-dou1.getDouble(i) )/dou1.getDouble(i);
						object.put("chazhi", tempcahzhi);
						object.put("fudu", tempfudu);

					}else {
						tempcahzhi =  dou2.getDouble(i)-dou1.getDouble(i) ;
						tempfudu = 0.0;
						object.put("chazhi", tempcahzhi);
						object.put("fudu", tempfudu);

					}
					//判断是否异常项
					if(Math.abs(tempfudu) > fudu) {
						//异常项 先判断正向或反向
						if(trend.equals("up")) {
							if(tempfudu>0) {
								improve++;
								comparison.put("提升项");
								object.put("comparison", "提升项");
							}else {
								bad++;
								comparison.put("恶化项");
								object.put("comparison","恶化项");
							}
						}else {
							if(tempfudu>0) {
								bad++;
								comparison.put("恶化项");
								object.put("comparison","恶化项");
							}else {
								improve++;
								comparison.put("提升项");
								object.put("comparison", "提升项");
							}
						}
					}else {
						normal++;
						comparison.put("持平项");
						object.put("comparison", "持平项");
					}
					
				}else {
					double tempchazhi=dou2.getDouble(i)-dou1.getDouble(i) ;
					if(Math.abs(tempchazhi) >fudu) {
						if(trend.equals("up")) {
							if(tempchazhi>0) {
								improve++;
								comparison.put("提升项");
								object.put("comparison", "提升项");
							}else {
								bad++;
								comparison.put("恶化项");
								object.put("comparison","恶化项");
							}
						}else {
							if(tempchazhi>0) {
								bad++;
								comparison.put("恶化项");
								object.put("comparison","恶化项");
							}else {
								improve++;
								comparison.put("提升项");
								object.put("comparison", "提升项");
							}
						}
					}else {
						normal++;
						comparison.put("持平项");
						object.put("comparison", "持平项");
					}
				}
			}

			for(int i=0;i<kpicountArray.length();i++) {
				JSONObject object = kpicountArray.getJSONObject(i);
				
				String tempcomparison = object.getString("comparison");
				if(tempcomparison.equals("持平项")||tempcomparison.equals("提升项")) {
					object.put("deduction", "");
				}
				
				if(object.getString("unit").equals("%")) {
//					object.put("old",object.getDouble("old")*100 );
//					object.put("new",object.getDouble("new")*100 );
//					object.put("chazhi",object.getDouble("chazhi")*100 );
//					object.put("fudu",object.getDouble("fudu")*100 );
					object.put("old",String.format("%.4f", object.getDouble("old"))+"%" );
					object.put("new", String.format("%.4f", object.getDouble("new"))+"%" );
					object.put("chazhi", String.format("%.4f", object.getDouble("chazhi"))+"%" );
					object.put("fudu", String.format("%.4f", object.getDouble("fudu"))+"%" );
				}else {
					object.put("old",String.format("%.4f", object.getDouble("old")) );
					object.put("new", String.format("%.4f", object.getDouble("new")) );
					object.put("chazhi", String.format("%.4f", object.getDouble("chazhi")) );
					object.put("fudu", String.format("%.4f", object.getDouble("fudu")) );
				}
			}			
		    dao.close();
		    return kpicountArray;
		}
		
}
