/*
 Highcharts JS v9.0.1 (2021-02-15)

 (c) 2009-2021 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/modules/overlapping-datalabels",["highcharts"],function(b){a(b);a.Highcharts=b;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){(function(a,c,d,e){a.hasOwnProperty(c)||(a[c]=e.apply(null,d))})(a?a._modules:{},"masters/modules/overlapping-datalabels.src.js",[],function(){})});
//# sourceMappingURL=overlapping-datalabels.js.map