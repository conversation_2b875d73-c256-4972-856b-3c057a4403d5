/**
 * 
 */
var xmlHttp=false;      
	var kkkk = "";
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}
function init()
{
	function resetTabs(){
        $("#content > div").hide(); //Hide all content
        $("#tabs a").attr("id",""); //Reset id's      
    }

    var myUrl = window.location.href; //get URL
    var myUrlTab = myUrl.substring(myUrl.indexOf("#")); // For localhost/tabs.html#tab2, myUrlTab = #tab2     
    var myUrlTabName = myUrlTab.substring(0,4); // For the above example, myUrlTabName = #tab

    (function(){
        $("#content > div").hide(); // Initially hide all content
        $("#tabs li:first a").attr("id","current"); // Activate first tab
        $("#content > div:first").fadeIn(); // Show first tab content
        
        $("#tabs a").on("click",function(e) {
            e.preventDefault();
            console.log(this);
            if ($(this).attr("id") == "current"){ //detection for current tab
             return       
            }
            else{             
            resetTabs();
            $(this).attr("id","current"); // Activate this
            $($(this).attr('name')).fadeIn(); // Show content for current tab
            }
        });

        for (i = 1; i <= $("#tabs li").length; i++) {
          if (myUrlTab == myUrlTabName + i) {
              resetTabs();
              $("a[name='"+myUrlTab+"']").attr("id","current"); // Activate url tab
              $(myUrlTab).fadeIn(); // Show url tab content        
          }
        }
    })()
}
////
var padata = eval('('+parent.myjson+')');//父界面传递的任务信息
   var colorList = ['#C33531','#EFE42A','#64BD3D','#EE9201','#29AAE3', '#B74AE5','#0AAF9F','#E89589','#16A085','#4A235A','#C39BD3 ','#F9E79F','#BA4A00','#ECF0F1','#616A6B','#EAF2F8','#4A235A','#3498DB' ]; 
var loc =encodeURI( location.href);//获取整个跳转地址内容，其实就是你传过来的整个地址字符串
	//var loc = "window.open(http://***********:8080/kpidataanalys/dataanalys.html?taskid=1&tem=111";
	console.log("我的地址"+loc);
	var n1 = loc.length;//地址的总长
	//console.log(n1);
	var n2 = loc.indexOf("?");//取得=号的位置
	//console.log(n2);
	var parameter = decodeURI(loc.substr(n2+1, n1-n2));//截取从?号后面的内容,也就是参数列表，因为传过来的路径是加了码的，所以要解码
	var parameters  = parameter.split("&");//从&处拆分，返回字符串数组
	//console.log("参数列表"+parameters);
	var padata = new Array();//创建一个用于保存具体值得数组
	for (var i = 0; i < parameters.length; i++) {
		//console.log("参数键值对值"+i+":"+parameters[i]);
		var m1 = parameters[i].length;//获得每个键值对的长度
		var m2 = parameters[i].indexOf("=");//获得每个键值对=号的位置
		var value = parameters[i].substr(m2+1, m1-m2).split(":")[0];//获取每个键值对=号后面具体的值
		//console.log(parameters[i].split("=")[0]);
		var key=parameters[i].split("=")[0];
		padata[key] = value;
		console.log("参数值"+i+":"+value);
	}
	console.log(padata);
//var tem="1";
//padata = {"taskid": "1908",tem:"5"};
var t = document.getElementById("title");
	s="";
	s+="<a style=' color:rgb(0, 142, 211);font-size: 33px;font-weight: 550;font-family:Microsoft YaHei' id='titleaa'> </a><span style='margin-left:0.5%' class='layui-badge layui-bg-blue'>iWork</span>";
	////console.log(data[i].prokey);
//	
var temall = padata.tem;
console.log(s);
t.innerHTML=s;
console.log(t);
	  $("#helhcom").hide();
//======================================================
 ///===================判断百分比==========================
 function comparePercentages(percentage1, percentage2) {
  const decimal1 = parseFloat(percentage1) / 100;
  const decimal2 = parseFloat(percentage2) / 100;

  if (decimal1 > decimal2) {
    return 'ok';
  } else if (decimal1 < decimal2) {
    return 'no';
  } else {
    return 'ok';
  }
} 
var selcount = ""//当前构建的指标选择框
var selbeforenum = ""//历史构建版本选择框
//-------------当前默认的指标和历史构建号的传递：
//--------------------指标和构建号的选择
  	   $.ajax({
	type: "post",
				   url:'http://***********:8080/iWorkDataHandlerAll/Iwork2TaskHandler',
				   //url:'indexzz.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					selcount = data[0];
					selbeforenum = data[1];
				}
}); 
//
///
function initdata()
{	
//初始页面的表格和图的数据传递和显示：
var datatable1 = [];//最新构建的表
var datatable2 = [];//历史构建的表	
var vchart1=[];//最新构建的图
var jobname="";
//----------------初始页面数据的ajax：
	  $.ajax({
	type: "get",
				   url:'taskindexold',
				   //url:'initdata2.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data[3][0]);
					//table.reload('test', {data: data[0]});	//基础指标
					//table.reload('test2', {data: data[1]});	//基础指标
					datatable1=data[0];
					datatable2=data[1];
					vchart1 = data[2];
					jobname = data[3][0].jobname;
					document.getElementById("titleaa").innerHTML = data[3][0].jobid+":"+data[3][0].jobname+"  任务数据看板";
					document.getElementById("newbuild").innerHTML =  "当前构建号为："+data[3][0].buildnum+"#"
				}
}); 
console.log(jobname);
jobname=encodeURIComponent(jobname);
//-----------------------------------------------------------------------
var tjump = document.getElementById("jump");
	sjump="";
	sjump+="<a style='float:right;padding:0.5%;font-weight:bolder;color:#008ED3' href='/iWork2ChartHandler2/inittest.jsp?taskid="+padata.taskid+"&tem="+padata.tem+"&jobname="+jobname+"'>新版界面</a>";	  
	tjump.innerHTML=sjump;
//-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=		
  layui.use('table', function(){
	var treeTable = layui.treeTable;  
  var table = layui.table;  
table.render({
    elem: '#test1',
    //,url: '###'
    maxHeight:500 
    ,cols: [[
       {field:'useCaseName',title: '用例名称',width: '15%',sort:true}
      ,{field:'startTime', title: '开始时间',width: '20%',sort:true}
      ,{field:'endTime', title: '结束时间',width: '20%',sort:true}
      ,{field:'status',title: '状态',width: '15%',sort:true}
      ,{field:'spendTime', title: '耗时(min)',width: '10%',sort:true} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'people', title: '拥有者',width: '10%',sort:true}
      ,{field:'other', title: '其它信息',width: '10%'}
    ]],
    data:datatable1,
    text:{
                                none:'当前任务暂无数据！'},
  }); 
  
  Highcharts.chart('container', {
	chart: {
		type: 'column',
		backgroundColor: '#fcfcfc'

	},
	title: {
		text: '测试用例执行时长统计'
	},
	subtitle: {
		text: '具体版本测试数据可在数据分析模块查看，数据来源:http://iwork.zx.zte.com.cn.'
	},
	xAxis: {
		type: 'category'
	},
	yAxis: {
		title: {
			text: '用例执行时长(min)'
		}
	},
	legend: {
		enabled: false
	},
	plotOptions: {
		series: {
			borderWidth: 0,
			dataLabels: {
				enabled: true,
				format: '{point.y}'
			}
		}
	},
	tooltip: {
		headerFormat: '<span style="font-size:11px">{point.name}</span><br>',
		pointFormat: '<span style="color:{point.color}">{point.name}</span></b><br/>'
	},
	series: [{
		name: '用例名称',
		colorByPoint: true,
		data: vchart1,
		maxPointWidth: 100
    
	}]
	});
//-----------历史构建：
   treeTable.render({
    elem: '#test2'
    // treeTable 特定属性集
    ,cols: [[
       {field:'casename',title: '构建号',width: '15%'}
      ,{field:'starttime', title: '开始时间',width: '20%'}
      ,{field:'endtime', title: '结束时间',width: '20%'}
      ,{field:'status',title: '状态',width: '15%'}
      ,{field:'spendtime', title: '耗时(min)',width: '10%'} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'people', title: '拥有者',width: '10%'}
      ,{field:'other', title: '其它信息',width: '10%'}
    ]],
   data:datatable2,
   text:{
                                none:'当前任务暂无历史构建数据！'},
   }); 	
//-----------20个基础指标数据：
   if((padata.tem=="1")||(padata.tem=="0")){//-------------中移模板
      table.render({
    elem: '#test3'
    ,cols: [[
       {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',sort:true}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',sort:true,}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',sort:true}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',sort:true}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',sort:true}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',sort:true}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',sort:true}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',sort:true}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',sort:true}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',sort:true}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',sort:true}
        ,{field:'1594820269733', title: '小区上行平均MCS(Kpi)_1594820269733',width: '10%',sort:true}
        ,{field:'1594820269734', title: '小区下行平均MCS(Kpi)_1594820269734',width: '10%',sort:true}
     
    ]],
    text:{
                                none:'当前任务暂无指标数据！'},
    data:[],
     }); 
  }else if(padata.tem=="2"){//-------------电联
	    table.render({
    elem: '#test3'
    ,cols: [[
      {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'667000', title: 'RRC连接建立成功率(%)_667000',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667000]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667000] + '</span>';
																            } else if (comparePercentages(''+d[667000]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667000] + '</span>';
																            } else {
																              return d[667000];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'667184', title: '无线接通率(%)_667184',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667184]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667184] + '</span>';
																            } else if (comparePercentages(''+d[667184]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667184] + '</span>';
																            } else {
																              return d[667184];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'1647503157118', title: 'Flow掉线率(%)_1647503157118',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[1647503157118] + '</span>';
																            } else if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[1647503157118] + '</span>';
																            } else {
																              return d[1647503157118];
																            }
																          }}
      ,{field:'1597370984303',title: 'RRC连接重建比率(%)_1597370984303',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'1597370984308', title: '小区上行平均MCS(Kpi)_1597370984308',width: '10%',}
        ,{field:'1597370984309', title: '小区下行平均MCS(Kpi)_1597370984309',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}else if(padata.tem=="5"){//自定义模板
		table.render({
    elem: '#test3'
    ,cols: [[
      {field:'id',title: '用例别名',width: '5%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '10%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '10%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'4001511', title: '小区上行平均MCS(Kpi)_4001511',width: '10%',}
        ,{field:'4001512', title: '小区下行平均MCS(Kpi)_4001512',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}
	
//-----------当前构建健康度：
 table.render({
    elem: '#test4'
    ,toolbar: '#bar'
    ,cols: [[
       {field:'level',title: '类别',align:'center',width: '10%',sort:true}
      ,{field:'title', title: '指标',align:'center',width: '30%',templet: function (d) {
                        return '<div style="text-align:left">' + d.title + '</div>'
                    }}
      ,{field:'old', title: '用例一',align:'center',width: '10%',}
      ,{field:'new',title: '用例二',align:'center',width: '10%',}
      ,{field:'tempchazhi', title: '差值',align:'center',width: '10%',} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'tempfudu', title: '幅度',align:'center',width: '10%',}
      ,{field:'comparison', title: '描述',align:'center',width:'10%',sort:true,templet: function(d) {
																            if (d.comparison === '提升项') {
																              return '<span style="color: green;">' + d.comparison + '</span>';
																            } else if (d.comparison === '恶化项') {
																              return '<span style="color: red;">' + d.comparison + '</span>';
																            } else {
																              return d.comparison;
																            }
																          }}
       ,{field:'deduction', title: '扣分',align:'center',sort:true}
    ]],
   data:[],
   height: 'full-30',
   text:{
                                none:'当前任务暂无最新构建数据！'},
   }); 	
 //----------当前构建的kpi分析 
  table.render({
    elem: '#test6'
    //,url: '###'
    ,cols: [[
       {field:'version1',title: '版本[用例一]',width: '10%',align:'center'},
       {field:'version2',title: '版本[用例二]',width: '10%',align:'center'},
       {field:'usecasename1',title: '用例一',width: '13%',align:'center'},
       {field:'usecasename2',title: '用例二',width: '12%',align:'center'},
       {field:'task',title: '关联任务',width: '10%',align:'center'},
       {field:'imp',title: '提升项',width: '5%',align:'center'},
       {field:'flat',title: '持平项',width: '5%',align:'center'},
       {field:'worse',title: '恶化项',width: '5%',align:'center'},
       {field:'score',title: '性能KPI得分',width: '10%',align:'center'},
       {field:'con',title: '测试结论',align:'center'},
    ]],
    data:[],
    text:{
                                none:'暂时无法下载历史数据！'},
  });   
  //历史构建健康度：
 table.render({
    elem: '#test7'
    //,url: '###'
    ,toolbar: '#bar2'
    ,cols: [[
       {field:'level',title: '类别',align:'center',width: '10%',sort:true}
      ,{field:'title', title: '指标',align:'center',width: '30%',}
      ,{field:'old', title: '用例一',align:'center',width: '10%',}
      ,{field:'new',title: '用例二',align:'center',width: '10%',}
      ,{field:'tempchazhi', title: '差值',align:'center',width: '10%',} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'tempfudu', title: '幅度',align:'center',width: '10%',}
      ,{field:'comparison', title: '描述',align:'center',width:'10%',sort:true,templet: function(d) {
																            if (d.comparison === '提升项') {
																              return '<span style="color: green;">' + d.comparison + '</span>';
																            } else if (d.comparison === '恶化项') {
																              return '<span style="color: red;">' + d.comparison + '</span>';
																            } else {
																              return d.comparison;
																            }
																          }}
       ,{field:'deduction', title: '扣分',align:'center',sort:true}
    ]],
   data:[],
   height: 'full-30',
   text:{
                                none:'请选择对应测试用例及指标！'},
   });  
    //历史构建健康度[打分表]：
 table.render({
    elem: '#test8'
    //,url: '###'
    ,cols: [[
        {field:'version1',title: '版本[用例一]',width: '10%',align:'center'},
       {field:'version2',title: '版本[用例二]',width: '10%',align:'center'},
       {field:'usecasename1',title: '用例一',width: '13%',align:'center'},
       {field:'usecasename2',title: '用例二',width: '12%',align:'center'},
       {field:'task',title: '关联任务',width: '10%',align:'center'},
       {field:'imp',title: '提升项',width: '5%',align:'center'},
       {field:'flat',title: '持平项',width: '5%',align:'center'},
       {field:'worse',title: '恶化项',width: '5%',align:'center'},
       {field:'score',title: '性能KPI得分',width: '10%',align:'center'},
       {field:'con',title: '测试结论',align:'center'},
    ]],
    data:[],
    text:{
                                none:'请选择对比用例！'},
  });
  
 //-------历史下载：
  table.render({
    elem: '#test5'
    ,maxHeight :'300px'
    ,skin:'line'
    ,cols: [[
       {field:'bulidname',title: '构建号',width: '15%',align:'center'}
      ,{ title: '日志',width: '20%',toolbar: '#barlog',align:'center'}
      ,{ title: 'MTS数据',width: '20%',toolbar: '#barmts',align:'center'}
      ,{ title: 'KPI数据',width: '20%',toolbar: '#barkpi',align:'center'}
       ,{ title: '其他信息',align:'center'}
    ]],
    data:[],
    text:{
                                none:'暂时无法下载历史数据！'},
  });  
 //========================历史分析的基础指标的数据=============================
  if((padata.tem=="0")||(padata.tem=="1")){//-------------中移模板
  console.log("aaa");
      table.render({
    elem: '#test10'
    ,cols: [[
       {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',sort:true}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',sort:true,}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',sort:true}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',sort:true}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',sort:true}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',sort:true}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',sort:true}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',sort:true}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',sort:true}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',sort:true}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',sort:true}
        ,{field:'1594820269733', title: '小区上行平均MCS(Kpi)_1594820269733',width: '10%',sort:true}
        ,{field:'1594820269734', title: '小区下行平均MCS(Kpi)_1594820269734',width: '10%',sort:true}
     
    ]],
    text:{
                                none:'当前任务暂无指标数据！'},
    data:[],
     }); 
  }else if(padata.tem=="2"){//-------------电联
  console.log("bbb");
	    table.render({
    elem: '#test10'
    ,cols: [[
      {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'667000', title: 'RRC连接建立成功率(%)_667000',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667000]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667000] + '</span>';
																            } else if (comparePercentages(''+d[667000]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667000] + '</span>';
																            } else {
																              return d[667000];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'667184', title: '无线接通率(%)_667184',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667184]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667184] + '</span>';
																            } else if (comparePercentages(''+d[667184]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667184] + '</span>';
																            } else {
																              return d[667184];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'1647503157118', title: 'Flow掉线率(%)_1647503157118',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[1647503157118] + '</span>';
																            } else if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[1647503157118] + '</span>';
																            } else {
																              return d[1647503157118];
																            }
																          }}
      ,{field:'1597370984303',title: 'RRC连接重建比率(%)_1597370984303',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'1597370984308', title: '小区上行平均MCS(Kpi)_1597370984308',width: '10%',}
        ,{field:'1597370984309', title: '小区下行平均MCS(Kpi)_1597370984309',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}else if(padata.tem=="5"){//自定义模板
		console.log("ccc");
		table.render({
    elem: '#test10'
    ,cols: [[
      {field:'id',title: '用例别名',width: '5%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '10%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '10%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'4001511', title: '小区上行平均MCS(Kpi)_4001511',width: '10%',}
        ,{field:'4001512', title: '小区下行平均MCS(Kpi)_4001512',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}
 //========================  
  });//layui的加载

} 

//------------------------------------------点击事件【TAB2】-----------------------------------------------------------
 function tab2() {//点击tab2 的事件
   layui.use('table', function(){
	var treeTable = layui.treeTable;  
	var table = layui.table; 
	var form = layui.form;
//渲染xmselect选择图表：
var selchart="";
console.log(padata.tem);         
		if((padata.tem=="1")||(padata.tem=="0")){
		selchart = [
		{name: 'RRC连接最大连接用户数_C600000007', value: "C600000007"},{name: 'RRC连接平均连接用户数_C600000008', value: "C600000008", selected: true},{name: 'DU物理小区RRC连接建立成功率(%)_664216', value: "664216", selected: true},{name: 'QoS Flow建立成功率(%)_667014', value: "667014"},
		{name: '无线接通率（DU物理小区）(%)_664249', value: "664249"},{name: 'gNB间切换成功率(%)_667188', value: "667188"},{name: 'gNB内切换成功率(%)_667189', value: "667189"},{name: '无线掉线率(%)_667185', value: "667185"},
		{name: 'Flow掉线率(%)_667186', value: "667186"},{name: 'RRC连接重建比率（物理小区级）(%)_669002', value: "669002"},{name: '小区上行UE Throughput(kbps)_669798', value: "669798"},{name: '小区下行UE Throughput(kbps)_669501', value: "669501"},
		{name: '上行每PRB平均吞吐量(kbit/PRB)_670049', value: "670049"},{name: '下行每PRB平均吞吐量(kbit/PRB)_670050', value: "670050"},{name: '小区上行RLC SDU平均时延(ms)_669505', value: "669505"},{name: '小区下行RLC SDU平均时延(ms)_669503', value: "669503"},
		{name: '小区上行PRB平均占用率(%)_670067', value: "670067", selected: true},{name: '小区下行PRB平均占用率(%)_670068', value: "670068", selected: true},{name: 'Uplink average MCS(Kpi)_1594820269733', value: "1594820269733"},{name: 'Downlink average MCS(Kpi)_1594820269734', value: "1594820269734"},
	]
		}else if(padata.tem=="2"){
			selchart=[
		{name: 'RRC连接最大连接用户数_C600000007', value: "C600000007"},{name: 'RRC连接平均连接用户数_C600000008', value: "C600000008", selected: true},{name: 'DU物理小区RRC连接建立成功率(%)_667000', value: "667000", selected: true},{name: 'QoS Flow建立成功率(%)_667014', value: "667014"},
		{name: '无线接通率（DU物理小区）(%)_667184', value: "667184"},{name: 'gNB间切换成功率(%)_667188', value: "667188"},{name: 'gNB内切换成功率(%)_667189', value: "667189"},{name: '无线掉线率(%)_667185', value: "667185"},
		{name: 'Flow掉线率(%)_1647503157118', value: "1647503157118"},{name: 'RRC连接重建比率（物理小区级）(%)_1597370984303', value: "1597370984303"},{name: '小区上行UE Throughput(kbps)_669798', value: "669798"},{name: '小区下行UE Throughput(kbps)_669501', value: "669501"},
		{name: '上行每PRB平均吞吐量(kbit/PRB)_670049', value: "670049"},{name: '下行每PRB平均吞吐量(kbit/PRB)_670050', value: "670050"},{name: '小区上行RLC SDU平均时延(ms)_669505', value: "669505"},{name: '小区下行RLC SDU平均时延(ms)_669503', value: "669503"},
		{name: '小区上行PRB平均占用率(%)_670067', value: "670067", selected: true},{name: '小区下行PRB平均占用率(%)_670068', value: "670068", selected: true},{name: 'Uplink average MCS(Kpi)_1597370984308', value: "1597370984308"},{name: 'Downlink average MCS(Kpi)_1597370984309', value: "1597370984309"},
	]
		}else if(padata.tem=="5"){
			selchart=[
		{name: 'RRC连接最大连接用户数_C600000007', value: "C600000007"},{name: 'RRC连接平均连接用户数_C600000008', value: "C600000008", selected: true},{name: 'DU物理小区RRC连接建立成功率(%)_664216', value: "664216", selected: true},{name: 'QoS Flow建立成功率(%)_667014', value: "667014"},
		{name: '无线接通率（DU物理小区）(%)_664249', value: "664249"},{name: 'gNB间切换成功率(%)_667188', value: "667188"},{name: 'gNB内切换成功率(%)_667189', value: "667189"},{name: '无线掉线率(%)_667185', value: "667185"},
		{name: 'Flow掉线率(%)_667186', value: "667186"},{name: 'RRC连接重建比率（物理小区级）(%)_669002', value: "669002"},{name: '小区上行UE Throughput(kbps)_669798', value: "669798"},{name: '小区下行UE Throughput(kbps)_669501', value: "669501"},
		{name: '上行每PRB平均吞吐量(kbit/PRB)_670049', value: "670049"},{name: '下行每PRB平均吞吐量(kbit/PRB)_670050', value: "670050"},{name: '小区上行RLC SDU平均时延(ms)_669505', value: "669505"},{name: '小区下行RLC SDU平均时延(ms)_669503', value: "669503"},
		{name: '小区上行PRB平均占用率(%)_670067', value: "670067", selected: true},{name: '小区下行PRB平均占用率(%)_670068', value: "670068", selected: true},{name: 'Uplink average MCS(Kpi)_4001511', value: "4001511"},{name: 'Downlink average MCS(Kpi)_4001512', value: "4001512"},
	]
		}
console.log(selchart);		
xmSelect.render({
                 	el: '#newbuildchart', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	theme: {
						color: '#008ed3',
					},
                 	autoRow: true,
                	filterable: true,
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: selchart
                 }); 
 //添加xmselect指标：[不同模板不同值]       
            
 //获取对应的基础数据：                	 
  $.ajax({
				   type: "get",
				   url:'http://***********:8080/iWorkDataHandlerAll/QueryAutoDataAnalysisServlet',//20表+4个图
				   //url:'chartsdata.json',
				   //async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data)
					table.reload('test3', {data: data[0]});	//基础指标表格数据
					document.getElementById("newselchart").innerHTML = "";
					for(let icf=0;icf<data[1].length;icf++){
						//内部循环取对应值拼接band：
						console.log(data[1][icf].y[0].max);
							const nullPositions = data[1][icf].case.reduce((acc, curr, index) => {
							  if (curr === null) {
							    acc.push(index);
							  }
							  return acc;
							}, []);
							nullPositions.unshift(0)	;
							var listband = [];
							for(let icb=0;icb<nullPositions.length-1;icb++){
							/*	console.log(icb);
								console.log(nullPositions[icb]);
									console.log(nullPositions[icb+1]);
									console.log(data[1][icf].case[nullPositions[icb+1]-1]);*/
									var cfband={from:nullPositions[icb],to:nullPositions[icb+1],color:'white',borderWidth:1,borderColor:'rgba(68, 170, 213, 0.1)',label:{text:''+data[1][icf].case[nullPositions[icb+1]-1]+''}}
									listband.push(cfband);
							}		  
						console.log(nullPositions);
						//var cfband={from:0,to:4.5,color:'white',borderWidth:1,borderColor:'rgba(68, 170, 213, 0.1)',label:{text:'case1'}}
					/*	console.log(data[1][icf]);
						console.log(data[1][icf].title);
						console.log(data[1][icf].x);
						console.log(data[1][icf].series);
							console.log(data[1][icf].series.length);*/
							
						$('#newselchart').append("<div id ='chart"+icf+"' class='layui-col-md6' style='margin-top:1%;height: 430px'>");						
						//动态增加图	
						//渲染增加的图：
						Highcharts.chart('chart'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+data[1][icf].title+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [{
								categories: data[1][icf].x,
								labels: {
									  formatter: function() {
										if(Number.isInteger(this.value)){
												this.value = ""; 
										}else{
											
										}
				           return this.value
				        },
								rotation:90,
					         style :{
						fontSize: 8
						}
								},
						 plotLines:listband,
						 lineColor:'black'		
						}],
						yAxis: [{ // Primary yAxis
								max:data[1][icf].y[0].max,
								min:data[1][icf].y[0].min,
								tickInterval:data[1][icf].y[0].tickInterval,
								labels: {
										format: '{value}',
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								},
								title: {
										text: data[1][icf].y[0].label,
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								}
								
						}
							],
						tooltip: {
						},
						series:data[1][icf].series,
				
				});	
					}
				}
				}) 
			
  })//--------layui的渲染
           
        }
//------------------------------------------点击事件【TAB3】历史构建-----------------------------------------------------------
 function tab3() {//点击tab3 的事件
   layui.use('table', function(){
	var treeTable = layui.treeTable;  
	var table = layui.table; 
	var form = layui.form; 
	//------
	//===================【当前构建】自主分析多选框的初始化====================================== 
	const hisnum1 = [];
	for(let ihn=0;ihn<selbeforenum.length;ihn++){
		for (let value in selbeforenum[ihn]) {
			 console.log(selbeforenum[ihn][value]); // 输出：0, 1, 2
			 var hisnum2 = {name:"构建号_"+selbeforenum[ihn][value],value:selbeforenum[ihn][value]};
			 hisnum1.push(hisnum2);
			 
	}	
	 console.log(selbeforenum[ihn]);
	}
	//===================【历史构建】自主分析多选框的初始化====================================== 
 //table的值的获取：
 $.ajax({
				   type: "get",
				   url:'hisdatadown',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					table.reload('test5', {
							    data: data // 设置新的数据[打分]
							  });						
				}
				   })
 //【自主分析构建号】
	xmSelect.render({
                 	el: '#hbnumber', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'medium',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},	
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 	data: hisnum1
                 }) 				   
 //【自主分析构建号】
	xmSelect.render({
                 	el: '#hnumber1', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'medium',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	on: function(data){
						var arr = data.arr;
						var arrnum = "";
						for(let ihn=0;ihn<arr.length;ihn++){
							console.log(arr[ihn].value);
							arrnum = arrnum + '-'+arr[ihn].value;
						}
						console.log(arrnum);
						$.ajax({
				   type: "get",
				    url:'hisgetcase',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem,index:arrnum,flag:"z"},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					htestcase.update({		//构建号1	
				           		data:  data,	
				           	});
					
				}
				   })
						},
						
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 	data: hisnum1
                 }) 
//【自助分析】用例：
var htestcase=xmSelect.render({
                 	el: '#htestcase', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'medium',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	on:function(data){
						var arr = data.arr;
						var arrnum = "";
						for(let ihn=0;ihn<arr.length;ihn++){
							console.log(arr[ihn].value);
							arrnum = arrnum + ','+arr[ihn].value;
						}
						console.log(arrnum);
						$.ajax({
				   type: "get",
				    url:'hisgetcell',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem,tcase:encodeURI(arrnum)},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					htestcell.update({		//构建号1	
				           		data:  data,	
				           	});
					
				}
				   })
						},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: []
                 });
 //【自主分析】测试小区：
 var htestcell=xmSelect.render({
                 	el: '#htesetcell', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'medium',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: []
                 })                  
 //【自助分析】指标：
 xmSelect.render({
                 	el: '#htesttask', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'medium',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	width:'60px',
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: selcount
                 })   
//【历史健康度构建号】
 xmSelect.render({
                 	el: '#hnumber2', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'medium',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	on: function(data){
						var arr = data.arr;
						var arrnum = "";
						for(let ihn=0;ihn<arr.length;ihn++){
							console.log(arr[ihn].value);
							arrnum = arrnum + '-'+arr[ihn].value;
						}
						console.log(arrnum);
						$.ajax({
				   type: "get",
				   url:'hisgetcase',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem,index:arrnum,flag:"h"},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					htestcase2.update({		//健康度用例更新
				           		data:  data,	
				           	});
					
				}
				   })
						},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 	data: hisnum1
                 }) 
//【历史健康度分析】用例：
 var htestcase2 = xmSelect.render({
                 	el: '#htestcase2', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	max: 2,
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: []
                 }) 
	console.log("历史数据");
	})
	}  
//========================[tab初始化结束]=========================	
//-=-=-=-=-=-=-=-=-=-=-=-=-【form_on的button的事件监听】=-=-=-=-=-=-=-=-=-=-=-=-=
//20基础指标的图显示响应：
 layui.use( function(){
	var treeTable = layui.treeTable;  
	var table = layui.table; 
	var form = layui.form; 
	 var dropdown = layui.dropdown;
	
	form.on('submit(comnew)', function(data){
		var newcount = xmSelect.get("#newbuildchart",true).getValue('valueStr');//当前选择的指标
		console.log(newcount);
		xmSelect.get("#newbuildchart",true).closed();
	 if(newcount==""){
			layer.msg('请选择需要查看的基础关键指标!');
			 return false; // 阻止表单跳转
			}else{
				$.ajax({
				   type: "post",
				   url:'http://***********:8080/iWorkDataHandlerAll/iWork2AutoChartHandler',//当前构建自选图
				   //url:'chartzz.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem,index:newcount},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					document.getElementById("newselchart").innerHTML = "";
					for(let icf=0;icf<data[0].length;icf++){
						//内部循环取对应值拼接band：
							const nullPositions = data[0][icf].case.reduce((acc, curr, index) => {
							  if (curr === null) {
							    acc.push(index);
							  }
							  return acc;
							}, []);
							nullPositions.unshift(0)	;
							var listband = [];
							for(let icb=0;icb<nullPositions.length-1;icb++){
									var cfband={from:nullPositions[icb],to:nullPositions[icb+1],color:'white',borderWidth:1,borderColor:'rgba(68, 170, 213, 0.1)',label:{text:''+data[0][icf].case[nullPositions[icb+1]-1]+''}}
									listband.push(cfband);
							}		  
						console.log(nullPositions);
						$('#newselchart').append("<div id ='chart"+icf+"' class='layui-col-md6' style='margin-top:1%;height: 430px'>");						
						//动态增加图	
						//渲染增加的图：
						Highcharts.chart('chart'+icf+'', {
						chart: {
								//zoomType: 'x',
						},
						title: {
								text: ""+data[0][icf].title+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [{
								categories: data[0][icf].x,
								labels: {
									  formatter: function() {
										if(Number.isInteger(this.value)){
												this.value = ""; 
										}else{
											
										}
				           return this.value
				        },
								rotation:90,
					         style :{
						fontSize: 8
						}
								},
						lineColor:'black',
						 plotLines:listband,		
						}],
						yAxis: [{ // Primary yAxis
								max:data[0][icf].y[0].max,
								min:data[0][icf].y[0].min,
								tickInterval:data[0][icf].y[0].tickInterval,
								labels: {
										format: '{value}',
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								},
								title: {
										text: data[0][icf].y[0].label,
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								}
								
						}
							],
						tooltip: {
						},
						series:data[0][icf].series,
				
				});	
					}
				}
				   })
			 return false; // 阻止表单跳转
				}
				
	
			})
//---------formon指标分析：
 form.on('submit(hbase)', function(data){
	var fnnum= xmSelect.get("#hbnumber",true).getValue('valueStr');//构建号的值
	  var histem = "";
	  xmSelect.get("#hbnumber",true).closed();
	if(kkkk==""){
		console.log("yuan"); 
		histem = padata.tem
	}else if(kkkk==1){
		console.log("z"); 
			histem = "1"
	}else if(kkkk==2){
		console.log("d"); 
		histem = "2"
	}else if(kkkk==5){
		histem = "5"
		console.log("zdy"); 
	}
	console.log(histem);
    if((fnnum=="")){
			layer.msg('请检查是否完成构建号的选择！');
           
			 return false; // 阻止表单跳转
}else{
	$.ajax({
				   type: "post",
				   url:'http://***********:8080/iWorkDataHandlerAll/iWork2ChartHandler',
				   //url:'zizhu.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:histem,usecase:"",index:"",build:fnnum,newcase:"",newcell:""},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					table.reload('test10', {
							    data: data[0] // 设置新的数据[打分]
							  });	
				},error() {
                  	layer.confirm('加载暂时出现了点问题！', {icon: 2, btn: ['好的，回主页吧', '去登陆页'], title: '错误信息'})
                      
                  }
				})
				 return false; // 阻止表单跳转
				 }
})
//------formon自主分析：
  form.on('submit(hana1)', function(data){
    console.log(data.field); // 获取表单数据
    console.log("ok"); // 获取表单数据
     var fnnum1 = xmSelect.get("#hnumber1",true).getValue('valueStr');//构建号的值
    var fncase1 = xmSelect.get("#htestcase",true).getValue('valueStr');//当前测试用例的值
     var fncell1 = xmSelect.get("#htesetcell",true).getValue('valueStr');//当前测试用例的值
	var fncount= xmSelect.get("#htesttask",true).getValue('valueStr');// 当前指标的值
	xmSelect.get("#hnumber1",true).closed();
	xmSelect.get("#htestcase",true).closed();
	xmSelect.get("#htesetcell",true).closed();
	xmSelect.get("#htesttask",true).closed();
	//处理组合数据：
	let arr = fncase1.split(",");
	let arrc = fncell1.split(",");
	  console.log(arr); // 获取表单数据
	   console.log(arrc); // 获取表单数据
	var caseend = "";
for (let i = 0; i < arr.length; i++) {
 	let arr1 = arr[i].split("#")
 	for(let i2 = 0; i2 < arrc.length; i2++){
	 	let arrc2= arrc[i2].split("_")
	caseend = caseend+','+arr1[0]+'#'+arrc2[0]+'#'+arrc2[1]+'#'+arr1[1]+'#'+arr1[2]+'#'+arr1[3];
	console.log(caseend); // 获取表单数据
}
}

   console.log(fncase1); // 获取表单数据
   console.log(fncell1); // 获取表单数据
    console.log(caseend.substring(1)); // 获取表单数据
    var histem = "";
	if(kkkk==""){
		console.log("yuan"); 
		histem = padata.tem
	}else if(kkkk==1){
		console.log("z"); 
			histem = "1"
	}else if(kkkk==2){
		console.log("d"); 
		histem = "2"
	}else if(kkkk==5){
		histem = "5"
		console.log("zdy"); 
	}

    if((fncase1=="")||(fncount=="")||(fnnum1=="")||(fncell1=="")){
			layer.msg('请是否完成检查构建号、测试用例、测试小区和指标的选择！');
           
			 return false; // 阻止表单跳转
}else{
	console.log(encodeURI(caseend.substring(1)));
	console.log(encodeURI(fncount));
$.ajax({
				   type: "post",
				   url:'http://***********:8080/iWorkDataHandlerAll/iWork2ChartHandler',
				    //url:'zichart1.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:histem,usecase:encodeURI(caseend.substring(1)),index:encodeURI(fncount),build:"",newcase:encodeURI(fncase1),newcell:fncell1},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					document.getElementById("fhnum").innerHTML = "";
					for(let icf=0;icf<data[1].length;icf++){
						//内部循环取对应值拼接band：
							const nullPositions = data[1][icf].case.reduce((acc, curr, index) => {
							  if (curr === null) {
							    acc.push(index);
							  }
							  return acc;
							}, []);
							if(nullPositions[0]==0){
								
							}else{
							nullPositions.unshift(0)	;	
							}
							
							var listband = [];
							for(let icb=0;icb<nullPositions.length-1;icb++){
									var cfband={from:nullPositions[icb],to:nullPositions[icb+1],color:'white',borderWidth:1,borderColor:'rgba(68, 170, 213, 0.1)',label:{text:''+data[1][icf].case[nullPositions[icb+1]-1]+''}}
									listband.push(cfband);
							}		  
						console.log(nullPositions);
							
						$('#fhnum').append("<div id ='charth"+icf+"' class='layui-col-md6' style='margin-top:1%;height: 430px'>");						
						//动态增加图	
						//渲染增加的图：
						Highcharts.chart('charth'+icf+'', {
						chart: {
								//zoomType: 'x',
						},
						title: {
								text: ""+data[1][icf].title+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [{
								categories: data[1][icf].x,
								labels: {
									  formatter: function() {
										if(Number.isInteger(this.value)){
												this.value = ""; 
										}else{
											
										}
				           return this.value
				        },
								rotation:90,
					         style :{
						fontSize: 8
						}
								},
						 plotLines:listband,
						  lineColor:'black'			
						}],
						yAxis: [{ // Primary yAxis
								max:data[1][icf].y[0].max,
								min:data[1][icf].y[0].min,
								tickInterval:data[1][icf].y[0].tickInterval,
								labels: {
										format: '{value}',
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								},
								title: {
										text: data[1][icf].y[0].label,
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								}
								
						}
							],
						tooltip: {
						},
						series:data[1][icf].series,
				
				});	
					}
					},error() {
                  	layer.confirm('加载暂时出现了点问题！', {icon: 2, btn: ['好的，回主页吧', '去登陆页'], title: '错误信息'})
                      
                  }
				
					});
	return false; // 阻止表单跳转
}
     


  })
//=====---===历史健康度
var datasea2 = "";
  form.on('submit(hana2)', function(data){
	var comnum = xmSelect.get("#hnumber2",true).getValue('valueStr');//当前测试用例的值
	var comcase = xmSelect.get("#htestcase2",true).getValue('valueStr');//当前测试用例的值
	xmSelect.get("#hnumber2",true).closed();
	xmSelect.get("#htestcase2",true).closed();
	 if(!(comcase.includes(","))||((comnum==""))){
			layer.msg('请选择需要分析的构建号及用例!');
			 return false; // 阻止表单跳转
			}else{
					console.log(comcase);
				$.ajax({
				   type: "post",
				   url:'http://***********:8080/iWorkDataHandlerAll/iWork2TableHandler',
				   //url:'health.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem,usecase:encodeURI(comcase)},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					datasea2 = data[0];
					 $("#helhcom").show();
					const arr = {version1:data[2][0],version2:data[2][1],usecasename1:data[2][2],usecasename2:data[2][3],task:data[2][4],imp:data[2][5],flat:data[2][6],worse:data[2][7],score:data[2][8],con:data[2][9]}
						//console.log(arr);
						Highcharts.chart(healthhcharts, {
		chart: {
				zoomType: 'xy',
		},
		title: {
				text: 'KPI指标情况',
				style:{
						fontSize:'30px'
					}

		},

		xAxis: [{
				categories: ["关键一级(A类)","关键一级(B类)","关键一级(C类)","关键一级","关键二级","关键三级"],
				crosshair: true,
				labels: {
					
					style:{
						fontSize:'20px'
					}
						//rotation:-30
				}
				
		}],
		yAxis: [
		{
				gridLineWidth: 0,
				title: {
						text: '',
				},
				labels: {
						format: '{value} ',
						style:{
						fontSize:'20px'
					}
				}		
		}
		],
		tooltip: {
				shared: true
		},
		legend: {
				layout: 'vertical',
				align: 'left',
				x: 80,
				verticalAlign: 'top',
				y: 55,
				floating: true,
				backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || '#FFFFFF'
		},
		plotOptions: {
        column: {
			maxPointWidth: 80,
            //stacking: 'normal',
            dataLabels: {
                enabled: true,
                color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
                style: {
                    // 如果不需要数据标签阴影，可以尿 textOutline 设置丿 'none'
                    textOutline: '1px 1px black'
                }
            }
        }
    },
		series: [ 
		{
				name: '改善项',
				type: 'column',
				yAxis: 0,
				data:data[1][0],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#A6FFA6'
		},
		{
				name: '持平项',
				type: 'column',
				yAxis: 0,
				data:data[1][1],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#ACD6FF'
		},
		{
				name: '恶化项',
				type: 'column',
				yAxis: 0,
				data:data[1][2],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: 'red'
		}]
});
					table.reload('test8', {
							    data: [arr] // 设置新的数据[打分]
							  });	
					table.reload('test7', {
							    data: data[0] // 设置新的数据【具体指标对比】
							  });
				 
				},error() {
                  	layer.confirm('加载暂时出现了点问题！', {icon: 2, btn: ['好的，回主页吧', '去登陆页'], title: '错误信息'})
                      
                  }
				   });
				    return false; // 阻止表单跳转
			}
});  
//健康度的搜索事件：
table.on('toolbar(hel2)', function(obj){
            switch(obj.event){
                case 'search2':               
                    search2();
                    break;
            }
        });
 function search2(){
				//console.log("www");
				 var demoReload2 = document.getElementById("demoReload2").value;
	                console.log(demoReload2);
	                console.log(datasea2);
                   document.getElementById("demoReload2").value=demoReload2;
                   datasea2 = datasea2.filter((item) => {
	return item.level.includes(demoReload2)||item.title.includes(demoReload2)||item.old.includes(demoReload2)||item.new.includes(demoReload2)||item.tempchazhi.includes(demoReload2)
	||item.tempfudu.includes(demoReload2)||item.comparison.includes(demoReload2)||item.deduction.includes(demoReload2);
	})
console.log("过滤出来的array",datasea2)
       table.reload('test7', {
							    data: datasea2 // 设置新的数据【具体指标对比】
							  }); 
	  document.getElementById("demoReload2").value=demoReload2;						    
}  

//----------数据下载：
////历史任务的提交：
table.on('tool(dhis1)', function(obj){
	  console.log(obj.data.bulidname); // 查看对象所有成员
            switch(obj.event){
                case 'log':               
                   downlog(padata.taskid,obj.data.bulidname);
                    break;
                    case 'downdata':               
                    log("mts",obj.data.bulidname);
                    break;
                    case 'downdata2':               
                    log("kpi",obj.data.bulidname);
                    break;
            }
        });
 function log(type,number){
				//console.log("www");
				console.log(type);
				number = number.slice(0, -1);
				console.log(number);
							$.ajax({
				   type: "get",
				   url:'hisdatajudge',
				   async:false,
				   data: {jobid:padata.taskid,type:type,number:number},
				    dataType:'json',
				   success: function(data){
					console.log(data[0].msg);
					if(data[0].msg=="no"){
					layer.msg('暂时无当前构建号的'+type+'数据！');
					return false;
					}else{
						downloadFile("/kpidataanalys/filedatadownload?jobid="+padata.taskid+"&type="+type+"&number="+number+"");
					}
}
})
}  ;
//------------------下拉框事件：
 dropdown.render({
    elem: '#taskmodel', 
    data: [{
      title: '中移模板',
      id: 100
    },{
      title: '电联模板',
      id: 101
    },{
      title: '自定义模板',
      id: 102
    }],
    click: function(obj){
	console.log(obj);
	this.elem.find('span').text(obj.title);

	if(obj.title=="中移模板"){
		kkkk = 1;
	/*	console.log(kkkk);
			console.log("..");*/
	}else if(obj.title=="电联模板"){
			kkkk = 2;
/*		console.log("...");
		console.log(kkkk);*/
	}else if(obj.title=="自定义模板"){
			kkkk = 5;
/*		console.log("....");
		console.log(kkkk);*/
	}

	$.ajax({
				   type: "get",
				   url:'selkpitemplate',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:kkkk},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					xmSelect.get("#hbnumber",true).update({data:data});//基础指标
					xmSelect.get("#hnumber1",true).update({data:data});//图表
					xmSelect.get("#hnumber2",true).update({data:data});//历史构建
					 if((kkkk=="")||(kkkk=="1")){//-------------中移模板
  console.log("aaa");
  temall = "1"
  table.reload('test5', {});
      table.render({
    elem: '#test10'
    ,cols: [[
       {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',sort:true}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',sort:true,}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',sort:true}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',sort:true}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',sort:true}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',sort:true}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',sort:true}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',sort:true}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',sort:true}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',sort:true}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',sort:true}
        ,{field:'1594820269733', title: '小区上行平均MCS(Kpi)_1594820269733',width: '10%',sort:true}
        ,{field:'1594820269734', title: '小区下行平均MCS(Kpi)_1594820269734',width: '10%',sort:true}
     
    ]],
    text:{
                                none:'当前任务暂无指标数据！'},
    data:[],
     }); 
  }else if(kkkk=="2"){//-------------电联
  console.log("bbb");
  temall = "2"
   table.reload('test5', {});
	    table.render({
    elem: '#test10'
    ,cols: [[
      {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'667000', title: 'RRC连接建立成功率(%)_667000',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667000]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667000] + '</span>';
																            } else if (comparePercentages(''+d[667000]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667000] + '</span>';
																            } else {
																              return d[667000];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'667184', title: '无线接通率(%)_667184',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667184]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667184] + '</span>';
																            } else if (comparePercentages(''+d[667184]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667184] + '</span>';
																            } else {
																              return d[667184];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'1647503157118', title: 'Flow掉线率(%)_1647503157118',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[1647503157118] + '</span>';
																            } else if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[1647503157118] + '</span>';
																            } else {
																              return d[1647503157118];
																            }
																          }}
      ,{field:'1597370984303',title: 'RRC连接重建比率(%)_1597370984303',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'1597370984308', title: '小区上行平均MCS(Kpi)_1597370984308',width: '10%',}
        ,{field:'1597370984309', title: '小区下行平均MCS(Kpi)_1597370984309',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}else if(kkkk=="5"){
		console.log("ccc");
		temall="5"
		 table.reload('test5', {});
		table.render({
    elem: '#test10'
    ,cols: [[
      {field:'id',title: '用例别名',width: '5%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '10%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '10%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'4001511', title: '小区上行平均MCS(Kpi)_4001511',width: '10%',}
        ,{field:'4001512', title: '小区下行平均MCS(Kpi)_4001512',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}
					         	
			}
			})
			
			    }
			  });
//----------包在layui的渲染里	
})
//-=-=-=-=-=-=-=-=-=-=-=其他function-=-=-=-=-=-=-=-=-=-=-
function downloadFile(url) {
  var xhr = new XMLHttpRequest();
 
  xhr.open("GET", url, true);
   console.log(xhr);
  xhr.responseType = "blob";

  xhr.onload = function(e) {
    if (this.status == 200) {
      var blob = this.response;
      console.log(url);
      	var n2 = url.indexOf("?");//取得=号的位置
	var parameter = decodeURI(url.substr(n2+1, url.length-n2));//截取从?号后面的内容,也就是参数列表，因为传过来的路径是加了码的，所以要解码
	var parameters  = parameter.split("&");//从&处拆分，返回字符串数组
	console.log(parameters);
      var link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = ''+parameters[0].split("=")[1]+'_'+parameters[2].split("=")[1]+'_'+parameters[1].split("=")[1]+'.zip'; // 你需要设置正确的文件名
      link.click();
    }
  };

  xhr.send();
}	

function downlog(id,buildno){
	var buildno1 = buildno.slice(0, -1);
	//console.log(buildno1);
	window.location.href='http://***********:8080/iWork2ChartHandler2/logdownload?id='+id+"&buildno="+buildno1
}	

function findMinMaxNonNullIndex(list) {
  let minIndex = list.findIndex((value) => value !== null);
  let maxIndex = minIndex;

  for (let i = minIndex + 1; i < list.length; i++) {
    if (list[i] !== null) {
      maxIndex = i;
    }
  }

  return [minIndex, maxIndex];
}




