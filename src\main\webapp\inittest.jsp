<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>任务数据</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="res/css/layui.css" type="text/css" rel="stylesheet"> 
    <link href="res/layui-v2.9.2/layui/css/layui.css" rel="stylesheet" />
    <link href="res/css/mytab.css" type="text/css" rel="stylesheet">
    <link href="res/css/hr.css" type="text/css" rel="stylesheet">
    <meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<meta http-equiv="Cache-control" content="no-cache">
	<meta http-equiv="Cache" content="no-cache">	
<style>

 xm-select > .xm-body .scroll-body {
    max-width: 500px;
};
.zte_buttoncolor {
  background-color: #ff0000; /* 红色 */
};
body {
  background-color: #ff0000;
};
.layui-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 2px 10px rgba(0, 0, 0, 0.6);
}
.layui-col-md-custom3 {
    width: 99% !important; /* 设置你想要的宽度 */
  }
.layui-col-md-custom {
    width: 24% !important; /* 设置你想要的宽度 */
  }
  .layui-col-md-custom2 {
    width: 49% !important; /* 设置你想要的宽度 */
  }
</style>
</head>
<body >
   <center style="padding-top: 20px">
        <div id="title"></div>
	</center>
	<div id="jump"></div>
<hr class="hr-twill-colorful">
<button id="taskmodel" class="layui-btn layui-btn-sm demo-dropdown-base layui-bg-blue" style="margin-left:93%">
    <span id="modelname">模板选择</span>
    <i class="layui-icon layui-icon-down layui-font-12"></i>
  </button>	
    <ul id="tabs" style="margin-top:-2%">
      <li><a href="#" name="#tab1">任务概览</a></li>
      <li><a href="#"  name="#tab2">健康度</a></li>
      <li><a href="#"  name="#tab3">自主分析(表)</a></li>
      <li><a href="#"  name="#tab4">自主分析(图)</a></li> 
      <li><a href="#"  name="#tab5">AI数据分析</a></li>    
         
  	</ul>

  <div id="content">
      <div id="tab1" >
       <div class="layui-row "  style="margin-top:1%">
    <div class=" layui-panel layui-col-md3 layui-col-md-custom"  >
      <div class="layui-card">
        <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">构建次数</div>
        <div class="layui-card-body">
         <i class="layui-icon layui-icon-templeate-1" style="margin-left:3%;font-size: 30px; color: #008ED3;"></i>
       
         <span id="card1" style="margin-right:5%;float:right;font-size: 18px;font-weight: 550;font-family:Microsoft YaHei;color:#008ED3;"></span>
        </div>
      </div>
    </div>
    <div class=" layui-panel layui-col-md3 layui-col-md-custom"  style="margin-left:1%">
      <div class="layui-card">
        <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">执行用例</div>
        <div class="layui-card-body">
          <i class="layui-icon layui-icon-template-1" style="margin-left:3%;font-size: 30px; color: #008ED3;"></i>
        <span  id="card2" style="margin-right:5%;float:right;font-size: 18px;font-weight: 550;font-family:Microsoft YaHei;color:#008ED3;"></span>
        </div>
      </div>
    </div>
    <div class=" layui-panel layui-col-md3 layui-col-md-custom" style="margin-left:1%">
      <div class="layui-card">
        <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">执行成功率</div>
        <div class="layui-card-body">
          <i class="layui-icon layui-icon-chart-screen" style="margin-left:5%;font-size: 30px; color: #008ED3;"></i>
         <span  id="card3" style="margin-right:5%;float:right;font-size: 18px;font-weight: 550;font-family:Microsoft YaHei;color:#008ED3;"></span>
        </div>
      </div>
    </div>
    <div class="layui-panel layui-col-md3 layui-col-md-custom" style="margin-left:1%">
      <div class="layui-card">
        <div class="layui-card-header"  style="font-size:15px;font-weight:bolder;color:#008ED3">最新构建</div>
        <div class="layui-card-body">
         <i class="layui-icon layui-icon-form" style="margin-left:5%;font-size: 30px; color: #008ED3;"></i>
         <span  id="card4" style="margin-right:5%;float:right;font-size: 18px;font-weight: 550;font-family:Microsoft YaHei;color:#008ED3;"></span>
        </div>
      </div>
    </div>
  </div>
     
    <div class=" layui-row " style="margin-top:1%">
    <div class="layui-panel layui-col-md6 layui-col-md-custom2"  >
    	<div id="charttime"  style="margin-top:1%;height:300px;"></div>
    </div>
   <div class="layui-panel layui-col-md6 layui-col-md-custom2"  style="margin-left:1%">
   <div id="chartcase"  style="margin-top:1%;height:300px;"></div>
   </div>
  </div>	
   <div class="layui-panel layui-row layui-col-md-custom3" style="margin-top:1%">
   <h2 style="margin-top:0.5%;color:rgb(0, 142, 211)">【任务信息 】 </h2> 
  <form class="layui-form" style="padding:0.5%;margin-top:-1%">
					<table class="layui-hide" id="taskinfo" ></table><!-- 任务构建号相关信息 -->
				</form>
  </div>		
      </div>
  <!-- *****************************************tab2*************************************************************************************** -->    
      <div id="tab2">
       	<div class="layui-row "  style="margin-top:0.5%">
       			<div class=" layui-panel layui-col-md12">
       				<form class="layui-form" style="padding:1%">
       				  			<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3;margin-bottom:1%;">
       				<i class="layui-icon layui-icon-search" style="margin-left:-0.5%;font-size: 30px; color: #008ED3;"></i>
       				Kpi指标</div>
        					<div class="layui-form-item " style="margin-left:-2%">
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline " style="width:15%">
				                      <div id="helnum"></div>
				                  </div> 
		        					<label class="layui-form-label" style="font-weight:bold">测试用例</label>
                                    <div class="layui-input-inline" style="width:35%">
				                      <div id="helcase"></div>
				                  </div>  
				                  <button   lay-submit lay-filter="helcomp" style="margin-left:1%" class="layui-btn layui-btn-sm layui-bg-blue">对比</button>  
		                       </div>
		                       </form> 
       			</div>
       	</div>
       	<!-- 图的占位符 -->
       	<div class="layui-panel layui-row " style="margin-top:1%">
			  <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
			       				<i class="layui-icon layui-icon-chart" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
			       				  Kpi指标(图)
			       				</div>
				  <div class="layui-card-body" style="height:300px">
        					 <div id="healthhcharts" style="height:280px"></div><!-- 健康度图 -->
        				</div>
  </div>	
  	<!-- 表的占位符 -->
       	<div class="layui-panel layui-row " style="margin-top:1%">
  			<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
       				<i class="layui-icon layui-icon-form" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
       				Kpi指标(表)</div>
  					<form class="layui-form" style="padding:0.5%;margin-top:1%">
								<div  class="layui-hide"  id="helscore" ></div><!-- 健康度打分表 -->	 
					</form>
					<div class="layui-form" style="padding:0.5%;margin-top:1%">
							 <table style="margin-top:1%" class="layui-hide" id="helkpidata"  lay-filter="hel"></table><!-- 健康度数据表 -->
					</div>
					
  			</div>	
  			
<!--   VMAX用例选择 -->
  			<div class="layui-row "  style="margin-top:0.5%">
       			<div class=" layui-panel layui-col-md12">
       				<form class="layui-form" style="padding:1%">
  			<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3;margin-bottom:1%;">
       				<i class="layui-icon layui-icon-search" style="margin-left:-0.5%;font-size: 30px; color: #008ED3;"></i>
       				VMAX指标</div>
        					<div class="layui-form-item " style="margin-left:-2%">
        					     
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline " style="width:15%">
				                      <div id="vmaxnum"></div>
				                  </div> 
		        					<label class="layui-form-label" style="font-weight:bold">测试用例</label>
                                    <div class="layui-input-inline" style="width:35%">
				                      <div id="vmaxcase"></div>
				                  </div>  
				                  <button   lay-submit lay-filter="vmaxcomp" style="margin-left:1%" class="layui-btn layui-btn-sm layui-bg-blue">对比</button>  
		                       </div>
		                       </form> 
       			</div>
       		</div>
       		  	<!-- 表的占位符 -->
       	<div class="layui-panel layui-row " style="margin-top:1%">
  			<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
       				<i class="layui-icon layui-icon-form" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
       				VMAX指标(表)</div>
  					<form class="layui-form" style="padding:0.5%;margin-top:1%">
								<div  class="layui-hide"  id="vmaxscore" ></div><!-- 健康度打分表 -->	 
					</form>
					<div class="layui-form" style="padding:0.5%;margin-top:1%">
							 <table style="margin-top:1%" class="layui-hide" id="vmaxdata"  lay-filter="vmaxtt"></table><!-- 健康度数据表 -->
					</div>
					
  			</div>	
  			
      </div>
  <!-- *****************************************************tab3******************************************************************** -->    
      <div id="tab3">
			<div class="layui-row "  style="margin-top:0.5%">
      				<div class="layui-panel layui-row " style="margin-top:1%">
							  <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
							       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
							       				 基础数据
							       				</div>
								  <div class="layui-card-body" >
								  <form class="layui-form " style="margin-left:-2%">
        					<div class="layui-form-item" >
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline"  style="width:20%">
				                      <div id="zztbnum"></div>
				                      </div> 
				                  <button   lay-submit lay-filter="zztbasic"  style="margin-left:1%" class="layui-btn layui-btn-sm  layui-bg-blue">查看</button>  		                          	  
		                       </div>
		                       </form> 
								  <form class="layui-form" style="padding:0.5%;margin-top:1%">
								<table  class="layui-hide" id="zztbasic" ></table><!-- 基础数据分析-->	 
					</form>
				        				</div>
				  </div>	
      		</div>
      		<div class="layui-row "  style="margin-top:0.5%">
      				<div class="layui-panel layui-row " style="margin-top:1%">
							  <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
							       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
							       				 ACT数据分析
							       				</div>
								  <form class="layui-form " style="padding:1%">
        					<div class="layui-form-item" >
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline"  style="width:20%">
				                      <div id="zztbnumact"></div>
				                      </div> 
				                      <label class="layui-form-label" style="margin-left:-1%;font-weight:bold">测试用例</label>
                                    <div class="layui-input-inline" style="width:35%">
				                      <div id="zztcaseact"></div>
				                  </div>  
				                   <button   lay-submit lay-filter="actcase"  style="margin-left:1%" class="layui-btn layui-btn-sm  layui-bg-blue">分析</button>  		           
				                      </div> 
				                      <div class="layui-form-item"  id="actdiv" style="display:none">
				                      	  <div class="layui-input-inline"  style="margin-left:3%;width:55%">
				                      	   <div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="demo-filter-progress">
											  <div id="prodiv"  class="layui-progress-bar" lay-percent="0%"></div></div> 
				                      	  </div>
				                      	  <button   id="actbtn1"   lay-submit lay-filter="actdown1"  style="margin-top:-0.5%;margin-left:2%;display:none" class="layui-btn layui-btn-sm  layui-bg-blue">数据分析下载</button>  
				                      	  <button  id="actbtn2"    lay-submit lay-filter="actdown2"  style="margin-top:-0.5%;margin-left:1%;display:none" class="layui-btn layui-btn-sm  layui-bg-blue">测试报告下载</button>  
				                      </div>
				                         </form> 
							  </div>	
      		</div>
      		<div class="layui-row"  style="margin-top:0.5%">
       			<div class=" layui-panel ">
       			<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
							       				<i class="layui-icon layui-icon-layouts" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
							       				 多组自主分析 
							       				</div>
       				<form class="layui-form" style="padding:1%">
        					<div class="layui-form-item " style="margin-left:-2%">
        					

		        				<label class="layui-form-label" style="margin-left:-0.5%;width:10%;font-weight:bold">任务号-构建号</label>

                                    <div class="layui-input-inline " style="width:15%">
				                      <div id="zztbnum2"></div>
				                  </div> 
		        					<label class="layui-form-label" style="margin-left:-1%;font-weight:bold">测试用例</label>
                                    <div class="layui-input-inline" style="width:35%">
				                      <div id="zztcase"></div>
				                  </div>  
				                  <button   lay-submit lay-filter="zztadd" style="margin-left:1%" class="layui-btn layui-btn-sm layui-bg-blue">添加</button>  
				                   <button   lay-submit lay-filter="zzdcom" style="margin-left:1%" class="layui-btn layui-btn-sm layui-bg-blue">分析</button> 
		                       </div>
		                       </form>    
		                       <form class="layui-form" style="margin-left:2.5%;margin-top:-1.5%">
        						<table   class="layui-hide" id="zztaddcase" ></table><!-- 测试用例的添加对比组的表格 -->	 
        						 </form> 
		               <hr class="hr-shadow"> 
		                     <form class="layui-form" style="padding:0.5%;margin-top:1%">
								<table  class="layui-hide" id="zzmtscore" ></table><!-- 多组对比打分表 -->	 
					</form>
					<div class="layui-form" style="padding:0.5%;margin-top:1%">
							 <table style="margin-top:1%" class="layui-hide" id="zzmtdata"  lay-filter="hel2"></table><!-- 多组对比数据表 -->
					</div>  
       			</div>                  
       	</div>
      </div>
  <!-- *****************************************************tab4******************************************************************** -->        
      <div id="tab4">
      	<div class=" layui-panel layui-row layui-col-md-custom3"  style="margin-top:0.5%">
				<form class="layui-form" style="margin-left:-2%;padding:1%">
        					<div class="layui-form-item " >
        					<label class="layui-form-label" style="margin-left:-1%;font-weight:bold">测试指标</label>
                                    <div class="layui-input-inline" style="width:25%">
				                      <div id="zzbindex"></div>
				                  </div>
		        				<label class="layui-form-label" style="margin-left:-3%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline " style="width:12%">
				                      <div id="zzbnum"></div>
				                  </div>
		        						<label class="layui-form-label" style="margin-left:-2%;font-weight:bold">测试用例</label>
                                    <div class="layui-input-inline " style="width:18%">
				                      <div id="zzcase"></div>
				                  </div> 
		        						<label class="layui-form-label" style="margin-left:-2%;font-weight:bold">测试小区</label>
                                    <div class="layui-input-inline " style="width:13%">
				                      <div id="zzcell"></div>
				                  </div> 
                                	<button   lay-submit lay-filter="zzchartbtn"   style="float:right"class="layui-btn layui-btn-sm  layui-bg-blue" >分析</button>
		                       </div>
		                       </form>
      	 	</div>
      					<div id="zzbcall" class=" layui-row " >
      							<div class="layui-panel layui-col-md6 layui-col-md-custom2"  style="margin-top:1%">
						    	<div id="chart0" ></div>
						    </div>
						    <div class="layui-panel layui-col-md6 layui-col-md-custom2"  style="margin-top:1%;margin-left:1%">
						   <div id="chart1" ></div>
						   </div>
						   <div class="layui-panel layui-col-md6 layui-col-md-custom2"  style="margin-top:1%">
						    	<div id="chart2" ></div>
						    </div>
						    <div class="layui-panel layui-col-md6 layui-col-md-custom2"  style="margin-top:1%;margin-left:1%">
						   <div id="chart3" ></div>
						   </div>
						   <div class="layui-panel layui-col-md6 layui-col-md-custom2"  style="margin-top:1%;">
						   <div id="chart4" ></div>
						   </div>
      					</div>	
      </div>
      
      
       <!-- *****************************************************tab5******************************************************************** -->        
      <div id="tab5">
      	<div class=" layui-panel layui-row"  style="margin-top:0.5%">
      		
      		       			<div class=" layui-panel ">
       			<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
							       				<i class="layui-icon layui-icon-layouts" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
							       				 自主分析 
							       				</div>
       				<form class="layui-form" style="padding:1%">
        					<div class="layui-form-item " style="margin-left:-2%">
        					

		        				<label class="layui-form-label" style="margin-left:-0.5%;width:10%;font-weight:bold">测试数据</label>

                                    <div class="layui-input-inline " style="width:15%">
				                      <div id="aidata111"></div>
				                  </div>
				                  <button   lay-submit lay-filter="aidataact" style="margin-left:1%" class="layui-btn layui-btn-sm layui-bg-blue">分析</button>  
		                       </div>
		                       </form>    
		               <hr class="hr-shadow">  
       			</div>  
      		
      		
      	
<!--       		<form class="layui-form" style="float: right;"> -->
      			<button   onclick="downloadsum()" style="float:right; margin-right:1
      			%;margin-top:0.5%;margin-bottom: 0.5%" class="layui-btn layui-btn-sm layui-bg-blue">SummaryReport</button> 
<!--       		</form> -->
			<form class="layui-form" style="">
        		<table class="layui-hide" id="aidata" ></table><!-- 测试用例的添加对比组的表格 -->
        	</form> 
        			               <hr class="hr-shadow">  
        	
        	    <div class=" layui-panel ">
       				<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
							 <i class="layui-icon layui-icon-layouts" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
							 更多数据分析 
					</div>  
					<form class="layui-form" style="">
        				<table class="layui-hide" id="otherdata" ></table><!-- 测试用例的添加对比组的表格 -->
        			</form>  
       			</div>  
      	</div>
      </div>
  </div>
  <br><br><br><br>
  <input id ="temchange" style="display:none"/>
  <p id="about">&nbsp;iWork测试平台.2024 </p>


<script type="text/javascript" src="res/jquery.min.js"></script>
 <script type="text/javascript" src="res/layui.js"></script> 
<script type="text/javascript" src="res/waterMark.js"></script>
<script type="text/javascript" src="res/highcharts/highcharts.js"></script>
<script type="text/javascript" src="res/xm-select/treeTable.js"></script>
<script type="text/javascript" src="res/xm-select/xm-select.js"></script>
<script type="text/javascript" src="res/echarts.js"></script>
<!-- <script type="text/javascript" src="res/layui-v2.9.2/layui/layui.js"></script> -->
<!-- <script type="text/javascript" src="res/inittest.js?v=7"></script>  
<script type="text/javascript" src="res/eleminit.js"></script>  -->

 <script type="text/javascript" src="res/eleminitzs.js?v=53"></script> 
<script type="text/javascript" src="res/inittestzs.js?v=53"></script>
<script type="text/html" id="bar">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload" οnkeydοwn="if(event.keyCode==13){return false;}" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm layui-bg-blue"  lay-event="search" style="margin-left:2%" >搜索</button>
		    </div>
</script>
<script type="text/html" id="bar2">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload2" οnkeydοwn="if(event.keyCode==13){return false;}" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm layui-bg-blue"  lay-event="search2" style="margin-left:2%" >搜索</button>
		    </div>
</script>

<script type="text/html" id="bar3">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload3" οnkeydοwn="if(event.keyCode==13){return false;}" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm layui-bg-blue"  lay-event="search3" style="margin-left:2%" >搜索</button>
</div>
</script>
<script type="text/html" id="page0">

</script>

<script type="text/html" id="bardown">
  {{#  if(d.level==1){ }}
    <a  class="layui-btn layui-btn-sm layui-bg-orange" lay-event="log" >日志</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downdata" >历史KPI</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downrealdata" >实时KPI</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downudadata" >SPA LOG</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downmetrics" >Metrics</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downappdata" >UE感知数据</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downsta" >信令</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downdata2" >MTS</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downuds" >UDS容器信息</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downssa" >SSA LOG</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downvsa" >VSA LOG</a>

 {{#  } else { }}
 {{#  } }}
	</script>
	<script type="text/html" id="bardown1">
  {{#  if(d.level==1){ }}
    <a  class="layui-btn layui-btn-sm layui-bg-orange" lay-event="sparkdata" >Spark数据分析</a>
 {{#  } else { }}
 {{#  } }}
	</script>
	
<script type="text/html" id="bardown2">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="preview" >简要预览</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downaidata" >结果下载</a>
</script>
<script type="text/html" id="bardown22">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downaidata" >结果下载</a>
</script>
<script type="text/html" id="bardown3">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downaidata2" >查看</a>
</script>
<script type="text/html" id="bardown4">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="delaidata" >删除</a>
</script>
<script type="text/javascript">init()</script>
<script type="text/javascript">
	$(document).ready(function()
	{
		var user1 = "iWork";
		var user2 = "性能研发四部";
		var user3 = "ZTE";
		watermark({"watermark_txt0":user1,"watermark_txt1":user2,"watermark_txt2":user3});
	});
			
</script>  
</body>
</html>