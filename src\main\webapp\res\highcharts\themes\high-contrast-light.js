/*
 Highcharts JS v9.0.1 (2021-02-15)

 (c) 2009-2021 Highsoft AS

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/themes/high-contrast-light",["highcharts"],function(b){a(b);a.Highcharts=b;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){function b(a,c,b,d){a.hasOwnProperty(c)||(a[c]=d.apply(null,b))}a=a?a._modules:{};b(a,"Extensions/Themes/HighContrastLight.js",[a["Core/Globals.js"],a["Core/Utilities.js"]],function(a,b){b=b.setOptions;
a.theme={colors:"#5f98cf #434348 #49a65e #f45b5b #708090 #b68c51 #397550 #c0493d #4f4a7a #b381b3".split(" "),navigator:{series:{color:"#5f98cf",lineColor:"#5f98cf"}}};b(a.theme)});b(a,"masters/themes/high-contrast-light.src.js",[],function(){})});
//# sourceMappingURL=high-contrast-light.js.map