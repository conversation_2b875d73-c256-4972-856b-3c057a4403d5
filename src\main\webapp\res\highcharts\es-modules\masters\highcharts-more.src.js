/**
 * @license Highcharts JS v9.0.1 (2021-02-15)
 * @module highcharts/highcharts-more
 * @requires highcharts
 *
 * (c) 2009-2021 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */
'use strict';
import '../Extensions/Pane.js';
import '../Core/Axis/RadialAxis.js';
import '../Series/AreaRange/AreaRangeSeries.js';
import '../Series/AreaSplineRange/AreaSplineRangeSeries.js';
import '../Series/ColumnRange/ColumnRangeSeries.js';
import '../Series/ColumnPyramid/ColumnPyramidSeries.js';
import '../Series/Gauge/GaugeSeries.js';
import '../Series/BoxPlot/BoxPlotSeries.js';
import '../Series/ErrorBar/ErrorBarSeries.js';
import '../Series/Waterfall/WaterfallSeries.js';
import '../Series/Polygon/PolygonSeries.js';
import '../Series/Bubble/BubbleSeries.js';
import '../Series/PackedBubble/PackedBubbleSeries.js';
import '../Extensions/Polar.js';
