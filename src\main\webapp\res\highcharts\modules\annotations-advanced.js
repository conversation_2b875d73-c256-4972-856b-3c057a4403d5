/*
 Highcharts JS v9.0.1 (2021-02-15)

 Annotations module

 (c) 2009-2021 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(e){"object"===typeof module&&module.exports?(e["default"]=e,module.exports=e):"function"===typeof define&&define.amd?define("highcharts/modules/annotations-advanced",["highcharts"],function(t){e(t);e.Highcharts=t;return e}):e("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(e){function t(f,e,m,k){f.hasOwnProperty(e)||(f[e]=k.apply(null,m))}e=e?e._modules:{};t(e,"Extensions/Annotations/Mixins/EventEmitterMixin.js",[e["Core/Globals.js"],e["Core/Utilities.js"]],function(f,e){var h=
e.addEvent,k=e.fireEvent,n=e.objectEach,r=e.pick,d=e.removeEvent;return{addEvents:function(){var c=this,b=function(b){h(b,f.isTouchDevice?"touchstart":"mousedown",function(b){c.onMouseDown(b)},{passive:!1})};b(this.graphic.element);(c.labels||[]).forEach(function(a){a.options.useHTML&&a.graphic.text&&b(a.graphic.text.element)});n(c.options.events,function(b,a){var g=function(g){"click"===a&&c.cancelClick||b.call(c,c.chart.pointer.normalize(g),c.target)};if(-1===(c.nonDOMEvents||[]).indexOf(a))c.graphic.on(a,
g);else h(c,a,g,{passive:!1})});if(c.options.draggable&&(h(c,"drag",c.onDrag),!c.graphic.renderer.styledMode)){var a={cursor:{x:"ew-resize",y:"ns-resize",xy:"move"}[c.options.draggable]};c.graphic.css(a);(c.labels||[]).forEach(function(b){b.options.useHTML&&b.graphic.text&&b.graphic.text.css(a)})}c.isUpdating||k(c,"add")},removeDocEvents:function(){this.removeDrag&&(this.removeDrag=this.removeDrag());this.removeMouseUp&&(this.removeMouseUp=this.removeMouseUp())},onMouseDown:function(c){var b=this,
a=b.chart.pointer;c.preventDefault&&c.preventDefault();if(2!==c.button){c=a.normalize(c);var g=c.chartX;var l=c.chartY;b.cancelClick=!1;b.chart.hasDraggedAnnotation=!0;b.removeDrag=h(f.doc,f.isTouchDevice?"touchmove":"mousemove",function(c){b.hasDragged=!0;c=a.normalize(c);c.prevChartX=g;c.prevChartY=l;k(b,"drag",c);g=c.chartX;l=c.chartY},f.isTouchDevice?{passive:!1}:void 0);b.removeMouseUp=h(f.doc,f.isTouchDevice?"touchend":"mouseup",function(a){b.cancelClick=b.hasDragged;b.hasDragged=!1;b.chart.hasDraggedAnnotation=
!1;k(r(b.target,b),"afterUpdate");b.onMouseUp(a)},f.isTouchDevice?{passive:!1}:void 0)}},onMouseUp:function(c){var b=this.chart;c=this.target||this;var a=b.options.annotations;b=b.annotations.indexOf(c);this.removeDocEvents();a[b]=c.options},onDrag:function(c){if(this.chart.isInsidePlot(c.chartX-this.chart.plotLeft,c.chartY-this.chart.plotTop)){var b=this.mouseMoveToTranslation(c);"x"===this.options.draggable&&(b.y=0);"y"===this.options.draggable&&(b.x=0);this.points.length?this.translate(b.x,b.y):
(this.shapes.forEach(function(a){a.translate(b.x,b.y)}),this.labels.forEach(function(a){a.translate(b.x,b.y)}));this.redraw(!1)}},mouseMoveToRadians:function(c,b,a){var g=c.prevChartY-a,l=c.prevChartX-b;a=c.chartY-a;c=c.chartX-b;this.chart.inverted&&(b=l,l=g,g=b,b=c,c=a,a=b);return Math.atan2(a,c)-Math.atan2(g,l)},mouseMoveToTranslation:function(c){var b=c.chartX-c.prevChartX;c=c.chartY-c.prevChartY;if(this.chart.inverted){var a=c;c=b;b=a}return{x:b,y:c}},mouseMoveToScale:function(c,b,a){b=(c.chartX-
b||1)/(c.prevChartX-b||1);c=(c.chartY-a||1)/(c.prevChartY-a||1);this.chart.inverted&&(a=c,c=b,b=a);return{x:b,y:c}},destroy:function(){this.removeDocEvents();d(this);this.hcEvents=null}}});t(e,"Extensions/Annotations/ControlPoint.js",[e["Core/Utilities.js"],e["Extensions/Annotations/Mixins/EventEmitterMixin.js"]],function(f,e){var h=f.merge,k=f.pick;return function(){function f(f,d,c,b){this.addEvents=e.addEvents;this.graphic=void 0;this.mouseMoveToRadians=e.mouseMoveToRadians;this.mouseMoveToScale=
e.mouseMoveToScale;this.mouseMoveToTranslation=e.mouseMoveToTranslation;this.onDrag=e.onDrag;this.onMouseDown=e.onMouseDown;this.onMouseUp=e.onMouseUp;this.removeDocEvents=e.removeDocEvents;this.nonDOMEvents=["drag"];this.chart=f;this.target=d;this.options=c;this.index=k(c.index,b)}f.prototype.setVisibility=function(f){this.graphic.attr("visibility",f?"visible":"hidden");this.options.visible=f};f.prototype.render=function(){var f=this.chart,d=this.options;this.graphic=f.renderer.symbol(d.symbol,0,
0,d.width,d.height).add(f.controlPointsGroup).css(d.style);this.setVisibility(d.visible);this.addEvents()};f.prototype.redraw=function(f){this.graphic[f?"animate":"attr"](this.options.positioner.call(this,this.target))};f.prototype.destroy=function(){e.destroy.call(this);this.graphic&&(this.graphic=this.graphic.destroy());this.options=this.target=this.chart=null};f.prototype.update=function(f){var d=this.chart,c=this.target,b=this.index;f=h(!0,this.options,f);this.destroy();this.constructor(d,c,f,
b);this.render(d.controlPointsGroup);this.redraw()};return f}()});t(e,"Extensions/Annotations/MockPoint.js",[e["Core/Series/Series.js"],e["Core/Utilities.js"],e["Core/Axis/Axis.js"]],function(f,e,m){var k=e.defined,h=e.fireEvent;return function(){function e(d,c,b){this.y=this.x=this.plotY=this.plotX=this.isInside=void 0;this.mock=!0;this.series={visible:!0,chart:d,getPlotBox:f.prototype.getPlotBox};this.target=c||null;this.options=b;this.applyOptions(this.getOptions())}e.fromPoint=function(d){return new e(d.series.chart,
null,{x:d.x,y:d.y,xAxis:d.series.xAxis,yAxis:d.series.yAxis})};e.pointToPixels=function(d,c){var b=d.series,a=b.chart,g=d.plotX,l=d.plotY;a.inverted&&(d.mock?(g=d.plotY,l=d.plotX):(g=a.plotWidth-d.plotY,l=a.plotHeight-d.plotX));b&&!c&&(d=b.getPlotBox(),g+=d.translateX,l+=d.translateY);return{x:g,y:l}};e.pointToOptions=function(d){return{x:d.x,y:d.y,xAxis:d.series.xAxis,yAxis:d.series.yAxis}};e.prototype.hasDynamicOptions=function(){return"function"===typeof this.options};e.prototype.getOptions=function(){return this.hasDynamicOptions()?
this.options(this.target):this.options};e.prototype.applyOptions=function(d){this.command=d.command;this.setAxis(d,"x");this.setAxis(d,"y");this.refresh()};e.prototype.setAxis=function(d,c){c+="Axis";d=d[c];var b=this.series.chart;this.series[c]=d instanceof m?d:k(d)?b[c][d]||b.get(d):null};e.prototype.toAnchor=function(){var d=[this.plotX,this.plotY,0,0];this.series.chart.inverted&&(d[0]=this.plotY,d[1]=this.plotX);return d};e.prototype.getLabelConfig=function(){return{x:this.x,y:this.y,point:this}};
e.prototype.isInsidePlot=function(){var d=this.plotX,c=this.plotY,b=this.series.xAxis,a=this.series.yAxis,g={x:d,y:c,isInsidePlot:!0};b&&(g.isInsidePlot=k(d)&&0<=d&&d<=b.len);a&&(g.isInsidePlot=g.isInsidePlot&&k(c)&&0<=c&&c<=a.len);h(this.series.chart,"afterIsInsidePlot",g);return g.isInsidePlot};e.prototype.refresh=function(){var d=this.series,c=d.xAxis;d=d.yAxis;var b=this.getOptions();c?(this.x=b.x,this.plotX=c.toPixels(b.x,!0)):(this.x=null,this.plotX=b.x);d?(this.y=b.y,this.plotY=d.toPixels(b.y,
!0)):(this.y=null,this.plotY=b.y);this.isInside=this.isInsidePlot()};e.prototype.translate=function(d,c,b,a){this.hasDynamicOptions()||(this.plotX+=b,this.plotY+=a,this.refreshOptions())};e.prototype.scale=function(d,c,b,a){if(!this.hasDynamicOptions()){var g=this.plotY*a;this.plotX=(1-b)*d+this.plotX*b;this.plotY=(1-a)*c+g;this.refreshOptions()}};e.prototype.rotate=function(d,c,b){if(!this.hasDynamicOptions()){var a=Math.cos(b);b=Math.sin(b);var g=this.plotX,l=this.plotY;g-=d;l-=c;this.plotX=g*a-
l*b+d;this.plotY=g*b+l*a+c;this.refreshOptions()}};e.prototype.refreshOptions=function(){var d=this.series,c=d.xAxis;d=d.yAxis;this.x=this.options.x=c?this.options.x=c.toValue(this.plotX,!0):this.plotX;this.y=this.options.y=d?d.toValue(this.plotY,!0):this.plotY};return e}()});t(e,"Extensions/Annotations/Mixins/ControllableMixin.js",[e["Extensions/Annotations/ControlPoint.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Tooltip.js"],e["Core/Utilities.js"]],function(f,e,m,k){var h=k.isObject,r=
k.isString,d=k.merge,c=k.splat;return{init:function(b,a,g){this.annotation=b;this.chart=b.chart;this.options=a;this.points=[];this.controlPoints=[];this.index=g;this.linkPoints();this.addControlPoints()},attr:function(){this.graphic.attr.apply(this.graphic,arguments)},getPointsOptions:function(){var b=this.options;return b.points||b.point&&c(b.point)},attrsFromOptions:function(b){var a=this.constructor.attrsMap,g={},c,d=this.chart.styledMode;for(c in b){var f=a[c];!f||d&&-1!==["fill","stroke","stroke-width"].indexOf(f)||
(g[f]=b[c])}return g},anchor:function(b){var a=b.series.getPlotBox(),g=b.series.chart,c=b.mock?b.toAnchor():m.prototype.getAnchor.call({chart:b.series.chart},b);c={x:c[0]+(this.options.x||0),y:c[1]+(this.options.y||0),height:c[2]||0,width:c[3]||0};return{relativePosition:c,absolutePosition:d(c,{x:c.x+(b.mock?a.translateX:g.plotLeft),y:c.y+(b.mock?a.translateY:g.plotTop)})}},point:function(b,a){if(b&&b.series)return b;a&&null!==a.series||(h(b)?a=new e(this.chart,this,b):r(b)?a=this.chart.get(b)||null:
"function"===typeof b&&(a=b.call(a,this),a=a.series?a:new e(this.chart,this,b)));return a},linkPoints:function(){var b=this.getPointsOptions(),a=this.points,g=b&&b.length||0,c;for(c=0;c<g;c++){var d=this.point(b[c],a[c]);if(!d){a.length=0;return}d.mock&&d.refresh();a[c]=d}return a},addControlPoints:function(){var b=this.options.controlPoints;(b||[]).forEach(function(a,c){a=d(this.options.controlPointOptions,a);a.index||(a.index=c);b[c]=a;this.controlPoints.push(new f(this.chart,this,a))},this)},shouldBeDrawn:function(){return!!this.points.length},
render:function(b){this.controlPoints.forEach(function(b){b.render()})},redraw:function(b){this.controlPoints.forEach(function(a){a.redraw(b)})},transform:function(b,a,c,l,d){if(this.chart.inverted){var g=a;a=c;c=g}this.points.forEach(function(g,w){this.transformPoint(b,a,c,l,d,w)},this)},transformPoint:function(b,a,c,l,d,f){var g=this.points[f];g.mock||(g=this.points[f]=e.fromPoint(g));g[b](a,c,l,d)},translate:function(b,a){this.transform("translate",null,null,b,a)},translatePoint:function(b,a,c){this.transformPoint("translate",
null,null,b,a,c)},translateShape:function(b,a){var c=this.annotation.chart,l=this.annotation.userOptions,d=c.annotations.indexOf(this.annotation);c=c.options.annotations[d];this.translatePoint(b,a,0);c[this.collection][this.index].point=this.options.point;l[this.collection][this.index].point=this.options.point},rotate:function(b,a,c){this.transform("rotate",b,a,c)},scale:function(b,a,c,l){this.transform("scale",b,a,c,l)},setControlPointsVisibility:function(b){this.controlPoints.forEach(function(a){a.setVisibility(b)})},
destroy:function(){this.graphic&&(this.graphic=this.graphic.destroy());this.tracker&&(this.tracker=this.tracker.destroy());this.controlPoints.forEach(function(b){b.destroy()});this.options=this.controlPoints=this.points=this.chart=null;this.annotation&&(this.annotation=null)},update:function(b){var a=this.annotation;b=d(!0,this.options,b);var c=this.graphic.parentGroup;this.destroy();this.constructor(a,b);this.render(c);this.redraw()}}});t(e,"Extensions/Annotations/Mixins/MarkerMixin.js",[e["Core/Chart/Chart.js"],
e["Core/Renderer/SVG/SVGRenderer.js"],e["Core/Utilities.js"]],function(f,e,m){function k(b){return function(a){this.attr(b,"url(#"+a+")")}}var h=m.addEvent,r=m.defined,d=m.merge,c=m.objectEach,b=m.uniqueKey,a={arrow:{tagName:"marker",attributes:{display:"none",id:"arrow",refY:5,refX:9,markerWidth:10,markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 0 L 10 5 L 0 10 Z","stroke-width":0}}]},"reverse-arrow":{tagName:"marker",attributes:{display:"none",id:"reverse-arrow",refY:5,refX:1,markerWidth:10,
markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 5 L 10 0 L 10 10 Z","stroke-width":0}}]}};e.prototype.addMarker=function(b,a){var c={attributes:{id:b}},g={stroke:a.color||"none",fill:a.color||"rgba(0, 0, 0, 0.75)"};c.children=a.children.map(function(b){return d(g,b)});a=d(!0,{attributes:{markerWidth:20,markerHeight:20,refX:0,refY:0,orient:"auto"}},a,c);a=this.definition(a);a.id=b;return a};e={markerEndSetter:k("marker-end"),markerStartSetter:k("marker-start"),setItemMarkers:function(a){var c=
a.options,g=a.chart,f=g.options.defs,e=c.fill,x=r(e)&&"none"!==e?e:c.stroke;["markerStart","markerEnd"].forEach(function(l){var w,e=c[l],v;if(e){for(v in f){var u=f[v];if((e===(null===(w=u.attributes)||void 0===w?void 0:w.id)||e===u.id)&&"marker"===u.tagName){var B=u;break}}B&&(w=a[l]=g.renderer.addMarker((c.id||b())+"-"+e,d(B,{color:x})),a.attr(l,w.getAttribute("id")))}})}};h(f,"afterGetContainer",function(){this.options.defs=d(a,this.options.defs||{});c(this.options.defs,function(b){var a=b.attributes;
"marker"===b.tagName&&a&&"none"!==a.display&&this.renderer.addMarker(a.id,b)},this)});return e});t(e,"Extensions/Annotations/Controllables/ControllablePath.js",[e["Extensions/Annotations/Mixins/ControllableMixin.js"],e["Core/Globals.js"],e["Extensions/Annotations/Mixins/MarkerMixin.js"],e["Core/Utilities.js"]],function(f,e,m,k){var h=k.extend,r="rgba(192,192,192,"+(e.svg?.0001:.002)+")";return function(){function d(c,b,a){this.addControlPoints=f.addControlPoints;this.anchor=f.anchor;this.attr=f.attr;
this.attrsFromOptions=f.attrsFromOptions;this.destroy=f.destroy;this.getPointsOptions=f.getPointsOptions;this.init=f.init;this.linkPoints=f.linkPoints;this.point=f.point;this.rotate=f.rotate;this.scale=f.scale;this.setControlPointsVisibility=f.setControlPointsVisibility;this.setMarkers=m.setItemMarkers;this.transform=f.transform;this.transformPoint=f.transformPoint;this.translate=f.translate;this.translatePoint=f.translatePoint;this.translateShape=f.translateShape;this.update=f.update;this.type="path";
this.init(c,b,a);this.collection="shapes"}d.prototype.toD=function(){var c=this.options.d;if(c)return"function"===typeof c?c.call(this):c;c=this.points;var b=c.length,a=b,g=c[0],l=a&&this.anchor(g).absolutePosition,d=0,f=[];if(l)for(f.push(["M",l.x,l.y]);++d<b&&a;)g=c[d],a=g.command||"L",l=this.anchor(g).absolutePosition,"M"===a?f.push([a,l.x,l.y]):"L"===a?f.push([a,l.x,l.y]):"Z"===a&&f.push([a]),a=g.series.visible;return a?this.chart.renderer.crispLine(f,this.graphic.strokeWidth()):null};d.prototype.shouldBeDrawn=
function(){return f.shouldBeDrawn.call(this)||!!this.options.d};d.prototype.render=function(c){var b=this.options,a=this.attrsFromOptions(b);this.graphic=this.annotation.chart.renderer.path([["M",0,0]]).attr(a).add(c);b.className&&this.graphic.addClass(b.className);this.tracker=this.annotation.chart.renderer.path([["M",0,0]]).addClass("highcharts-tracker-line").attr({zIndex:2}).add(c);this.annotation.chart.styledMode||this.tracker.attr({"stroke-linejoin":"round",stroke:r,fill:r,"stroke-width":this.graphic.strokeWidth()+
2*b.snap});f.render.call(this);h(this.graphic,{markerStartSetter:m.markerStartSetter,markerEndSetter:m.markerEndSetter});this.setMarkers(this)};d.prototype.redraw=function(c){var b=this.toD(),a=c?"animate":"attr";b?(this.graphic[a]({d:b}),this.tracker[a]({d:b})):(this.graphic.attr({d:"M 0 -9000000000"}),this.tracker.attr({d:"M 0 -9000000000"}));this.graphic.placed=this.tracker.placed=!!b;f.redraw.call(this,c)};d.attrsMap={dashStyle:"dashstyle",strokeWidth:"stroke-width",stroke:"stroke",fill:"fill",
zIndex:"zIndex"};return d}()});t(e,"Extensions/Annotations/Controllables/ControllableRect.js",[e["Extensions/Annotations/Mixins/ControllableMixin.js"],e["Extensions/Annotations/Controllables/ControllablePath.js"],e["Core/Utilities.js"]],function(f,e,m){var k=m.merge;return function(){function h(e,d,c){this.addControlPoints=f.addControlPoints;this.anchor=f.anchor;this.attr=f.attr;this.attrsFromOptions=f.attrsFromOptions;this.destroy=f.destroy;this.getPointsOptions=f.getPointsOptions;this.init=f.init;
this.linkPoints=f.linkPoints;this.point=f.point;this.rotate=f.rotate;this.scale=f.scale;this.setControlPointsVisibility=f.setControlPointsVisibility;this.shouldBeDrawn=f.shouldBeDrawn;this.transform=f.transform;this.transformPoint=f.transformPoint;this.translatePoint=f.translatePoint;this.translateShape=f.translateShape;this.update=f.update;this.type="rect";this.translate=f.translateShape;this.init(e,d,c);this.collection="shapes"}h.prototype.render=function(e){var d=this.attrsFromOptions(this.options);
this.graphic=this.annotation.chart.renderer.rect(0,-9E9,0,0).attr(d).add(e);f.render.call(this)};h.prototype.redraw=function(e){var d=this.anchor(this.points[0]).absolutePosition;if(d)this.graphic[e?"animate":"attr"]({x:d.x,y:d.y,width:this.options.width,height:this.options.height});else this.attr({x:0,y:-9E9});this.graphic.placed=!!d;f.redraw.call(this,e)};h.attrsMap=k(e.attrsMap,{width:"width",height:"height"});return h}()});t(e,"Extensions/Annotations/Controllables/ControllableCircle.js",[e["Extensions/Annotations/Mixins/ControllableMixin.js"],
e["Extensions/Annotations/Controllables/ControllablePath.js"],e["Core/Utilities.js"]],function(f,e,m){var k=m.merge;return function(){function h(e,d,c){this.addControlPoints=f.addControlPoints;this.anchor=f.anchor;this.attr=f.attr;this.attrsFromOptions=f.attrsFromOptions;this.destroy=f.destroy;this.getPointsOptions=f.getPointsOptions;this.init=f.init;this.linkPoints=f.linkPoints;this.point=f.point;this.rotate=f.rotate;this.scale=f.scale;this.setControlPointsVisibility=f.setControlPointsVisibility;
this.shouldBeDrawn=f.shouldBeDrawn;this.transform=f.transform;this.transformPoint=f.transformPoint;this.translatePoint=f.translatePoint;this.translateShape=f.translateShape;this.update=f.update;this.type="circle";this.translate=f.translateShape;this.init(e,d,c);this.collection="shapes"}h.prototype.render=function(e){var d=this.attrsFromOptions(this.options);this.graphic=this.annotation.chart.renderer.circle(0,-9E9,0).attr(d).add(e);f.render.call(this)};h.prototype.redraw=function(e){var d=this.anchor(this.points[0]).absolutePosition;
if(d)this.graphic[e?"animate":"attr"]({x:d.x,y:d.y,r:this.options.r});else this.graphic.attr({x:0,y:-9E9});this.graphic.placed=!!d;f.redraw.call(this,e)};h.prototype.setRadius=function(e){this.options.r=e};h.attrsMap=k(e.attrsMap,{r:"r"});return h}()});t(e,"Extensions/Annotations/Controllables/ControllableLabel.js",[e["Extensions/Annotations/Mixins/ControllableMixin.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Renderer/SVG/SVGRenderer.js"],e["Core/Tooltip.js"],e["Core/Utilities.js"]],function(e,
h,m,k,n){var f=n.extend,d=n.format,c=n.isNumber,b=n.pick;n=function(){function a(b,a,c){this.addControlPoints=e.addControlPoints;this.attr=e.attr;this.attrsFromOptions=e.attrsFromOptions;this.destroy=e.destroy;this.getPointsOptions=e.getPointsOptions;this.init=e.init;this.linkPoints=e.linkPoints;this.point=e.point;this.rotate=e.rotate;this.scale=e.scale;this.setControlPointsVisibility=e.setControlPointsVisibility;this.shouldBeDrawn=e.shouldBeDrawn;this.transform=e.transform;this.transformPoint=e.transformPoint;
this.translateShape=e.translateShape;this.update=e.update;this.init(b,a,c);this.collection="labels"}a.alignedPosition=function(b,a){var c=b.align,g=b.verticalAlign,l=(a.x||0)+(b.x||0),d=(a.y||0)+(b.y||0),e,f;"right"===c?e=1:"center"===c&&(e=2);e&&(l+=(a.width-(b.width||0))/e);"bottom"===g?f=1:"middle"===g&&(f=2);f&&(d+=(a.height-(b.height||0))/f);return{x:Math.round(l),y:Math.round(d)}};a.justifiedOptions=function(b,a,c,d){var g=c.align,l=c.verticalAlign,e=a.box?0:a.padding||0,f=a.getBBox();a={align:g,
verticalAlign:l,x:c.x,y:c.y,width:a.width,height:a.height};c=d.x-b.plotLeft;var w=d.y-b.plotTop;d=c+e;0>d&&("right"===g?a.align="left":a.x=-d);d=c+f.width-e;d>b.plotWidth&&("left"===g?a.align="right":a.x=b.plotWidth-d);d=w+e;0>d&&("bottom"===l?a.verticalAlign="top":a.y=-d);d=w+f.height-e;d>b.plotHeight&&("top"===l?a.verticalAlign="bottom":a.y=b.plotHeight-d);return a};a.prototype.translatePoint=function(b,a){e.translatePoint.call(this,b,a,0)};a.prototype.translate=function(b,a){var c=this.annotation.chart,
g=this.annotation.userOptions,l=c.annotations.indexOf(this.annotation);l=c.options.annotations[l];c.inverted&&(c=b,b=a,a=c);this.options.x+=b;this.options.y+=a;l[this.collection][this.index].x=this.options.x;l[this.collection][this.index].y=this.options.y;g[this.collection][this.index].x=this.options.x;g[this.collection][this.index].y=this.options.y};a.prototype.render=function(b){var c=this.options,g=this.attrsFromOptions(c),d=c.style;this.graphic=this.annotation.chart.renderer.label("",0,-9999,
c.shape,null,null,c.useHTML,null,"annotation-label").attr(g).add(b);this.annotation.chart.styledMode||("contrast"===d.color&&(d.color=this.annotation.chart.renderer.getContrast(-1<a.shapesWithoutBackground.indexOf(c.shape)?"#FFFFFF":c.backgroundColor)),this.graphic.css(c.style).shadow(c.shadow));c.className&&this.graphic.addClass(c.className);this.graphic.labelrank=c.labelrank;e.render.call(this)};a.prototype.redraw=function(b){var a=this.options,c=this.text||a.format||a.text,g=this.graphic,f=this.points[0];
g.attr({text:c?d(c,f.getLabelConfig(),this.annotation.chart):a.formatter.call(f,this)});a=this.anchor(f);(c=this.position(a))?(g.alignAttr=c,c.anchorX=a.absolutePosition.x,c.anchorY=a.absolutePosition.y,g[b?"animate":"attr"](c)):g.attr({x:0,y:-9999});g.placed=!!c;e.redraw.call(this,b)};a.prototype.anchor=function(b){var a=e.anchor.apply(this,arguments),c=this.options.x||0,g=this.options.y||0;a.absolutePosition.x-=c;a.absolutePosition.y-=g;a.relativePosition.x-=c;a.relativePosition.y-=g;return a};
a.prototype.position=function(c){var g=this.graphic,d=this.annotation.chart,e=this.points[0],y=this.options,x=c.absolutePosition,m=c.relativePosition;if(c=e.series.visible&&h.prototype.isInsidePlot.call(e)){if(y.distance)var p=k.prototype.getPosition.call({chart:d,distance:b(y.distance,16)},g.width,g.height,{plotX:m.x,plotY:m.y,negative:e.negative,ttBelow:e.ttBelow,h:m.height||m.width});else y.positioner?p=y.positioner.call(this):(e={x:x.x,y:x.y,width:0,height:0},p=a.alignedPosition(f(y,{width:g.width,
height:g.height}),e),"justify"===this.options.overflow&&(p=a.alignedPosition(a.justifiedOptions(d,g,y,p),e)));y.crop&&(y=p.x-d.plotLeft,e=p.y-d.plotTop,c=d.isInsidePlot(y,e)&&d.isInsidePlot(y+g.width,e+g.height))}return c?p:null};a.attrsMap={backgroundColor:"fill",borderColor:"stroke",borderWidth:"stroke-width",zIndex:"zIndex",borderRadius:"r",padding:"padding"};a.shapesWithoutBackground=["connector"];return a}();m.prototype.symbols.connector=function(a,b,d,e,f){var g=f&&f.anchorX;f=f&&f.anchorY;
var l=d/2;if(c(g)&&c(f)){var w=[["M",g,f]];var p=b-f;0>p&&(p=-e-p);p<d&&(l=g<a+d/2?p:d-p);f>b+e?w.push(["L",a+l,b+e]):f<b?w.push(["L",a+l,b]):g<a?w.push(["L",a,b+e/2]):g>a+d&&w.push(["L",a+d,b+e/2])}return w||[]};return n});t(e,"Extensions/Annotations/Controllables/ControllableImage.js",[e["Extensions/Annotations/Controllables/ControllableLabel.js"],e["Extensions/Annotations/Mixins/ControllableMixin.js"]],function(e,h){return function(){function f(e,f,m){this.addControlPoints=h.addControlPoints;this.anchor=
h.anchor;this.attr=h.attr;this.attrsFromOptions=h.attrsFromOptions;this.destroy=h.destroy;this.getPointsOptions=h.getPointsOptions;this.init=h.init;this.linkPoints=h.linkPoints;this.point=h.point;this.rotate=h.rotate;this.scale=h.scale;this.setControlPointsVisibility=h.setControlPointsVisibility;this.shouldBeDrawn=h.shouldBeDrawn;this.transform=h.transform;this.transformPoint=h.transformPoint;this.translatePoint=h.translatePoint;this.translateShape=h.translateShape;this.update=h.update;this.type=
"image";this.translate=h.translateShape;this.init(e,f,m);this.collection="shapes"}f.prototype.render=function(e){var f=this.attrsFromOptions(this.options),k=this.options;this.graphic=this.annotation.chart.renderer.image(k.src,0,-9E9,k.width,k.height).attr(f).add(e);this.graphic.width=k.width;this.graphic.height=k.height;h.render.call(this)};f.prototype.redraw=function(f){var k=this.anchor(this.points[0]);if(k=e.prototype.position.call(this,k))this.graphic[f?"animate":"attr"]({x:k.x,y:k.y});else this.graphic.attr({x:0,
y:-9E9});this.graphic.placed=!!k;h.redraw.call(this,f)};f.attrsMap={width:"width",height:"height",zIndex:"zIndex"};return f}()});t(e,"Extensions/Annotations/Annotations.js",[e["Core/Animation/AnimationUtilities.js"],e["Core/Chart/Chart.js"],e["Extensions/Annotations/Mixins/ControllableMixin.js"],e["Extensions/Annotations/Controllables/ControllableRect.js"],e["Extensions/Annotations/Controllables/ControllableCircle.js"],e["Extensions/Annotations/Controllables/ControllablePath.js"],e["Extensions/Annotations/Controllables/ControllableImage.js"],
e["Extensions/Annotations/Controllables/ControllableLabel.js"],e["Extensions/Annotations/ControlPoint.js"],e["Extensions/Annotations/Mixins/EventEmitterMixin.js"],e["Core/Globals.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Pointer.js"],e["Core/Utilities.js"]],function(e,h,m,k,n,r,d,c,b,a,g,l,w,v){var f=e.getDeferredAnimation;e=h.prototype;var x=v.addEvent,D=v.defined,p=v.destroyObjectProperties,q=v.erase,A=v.extend,u=v.find,B=v.fireEvent,z=v.merge,C=v.pick,F=v.splat;v=v.wrap;var E=function(){function g(b,
a){this.annotation=void 0;this.coll="annotations";this.shapesGroup=this.labelsGroup=this.labelCollector=this.group=this.graphic=this.animationConfig=this.collection=void 0;this.chart=b;this.points=[];this.controlPoints=[];this.coll="annotations";this.labels=[];this.shapes=[];this.options=z(this.defaultOptions,a);this.userOptions=a;a=this.getLabelsAndShapesOptions(this.options,a);this.options.labels=a.labels;this.options.shapes=a.shapes;this.init(b,this.options)}g.prototype.init=function(){var a=this.chart,
b=this.options.animation;this.linkPoints();this.addControlPoints();this.addShapes();this.addLabels();this.setLabelCollector();this.animationConfig=f(a,b)};g.prototype.getLabelsAndShapesOptions=function(a,b){var c={};["labels","shapes"].forEach(function(g){a[g]&&(c[g]=F(b[g]).map(function(b,c){return z(a[g][c],b)}))});return c};g.prototype.addShapes=function(){(this.options.shapes||[]).forEach(function(a,b){a=this.initShape(a,b);z(!0,this.options.shapes[b],a.options)},this)};g.prototype.addLabels=
function(){(this.options.labels||[]).forEach(function(a,b){a=this.initLabel(a,b);z(!0,this.options.labels[b],a.options)},this)};g.prototype.addClipPaths=function(){this.setClipAxes();this.clipXAxis&&this.clipYAxis&&(this.clipRect=this.chart.renderer.clipRect(this.getClipBox()))};g.prototype.setClipAxes=function(){var a=this.chart.xAxis,b=this.chart.yAxis,c=(this.options.labels||[]).concat(this.options.shapes||[]).reduce(function(c,g){return[a[g&&g.point&&g.point.xAxis]||c[0],b[g&&g.point&&g.point.yAxis]||
c[1]]},[]);this.clipXAxis=c[0];this.clipYAxis=c[1]};g.prototype.getClipBox=function(){if(this.clipXAxis&&this.clipYAxis)return{x:this.clipXAxis.left,y:this.clipYAxis.top,width:this.clipXAxis.width,height:this.clipYAxis.height}};g.prototype.setLabelCollector=function(){var a=this;a.labelCollector=function(){return a.labels.reduce(function(a,b){b.options.allowOverlap||a.push(b.graphic);return a},[])};a.chart.labelCollectors.push(a.labelCollector)};g.prototype.setOptions=function(a){this.options=z(this.defaultOptions,
a)};g.prototype.redraw=function(a){this.linkPoints();this.graphic||this.render();this.clipRect&&this.clipRect.animate(this.getClipBox());this.redrawItems(this.shapes,a);this.redrawItems(this.labels,a);m.redraw.call(this,a)};g.prototype.redrawItems=function(a,b){for(var c=a.length;c--;)this.redrawItem(a[c],b)};g.prototype.renderItems=function(a){for(var b=a.length;b--;)this.renderItem(a[b])};g.prototype.render=function(){var a=this.chart.renderer;this.graphic=a.g("annotation").attr({opacity:0,zIndex:this.options.zIndex,
visibility:this.options.visible?"visible":"hidden"}).add();this.shapesGroup=a.g("annotation-shapes").add(this.graphic).clip(this.chart.plotBoxClip);this.labelsGroup=a.g("annotation-labels").attr({translateX:0,translateY:0}).add(this.graphic);this.addClipPaths();this.clipRect&&this.graphic.clip(this.clipRect);this.renderItems(this.shapes);this.renderItems(this.labels);this.addEvents();m.render.call(this)};g.prototype.setVisibility=function(a){var b=this.options;a=C(a,!b.visible);this.graphic.attr("visibility",
a?"visible":"hidden");a||this.setControlPointsVisibility(!1);b.visible=a};g.prototype.setControlPointsVisibility=function(a){var b=function(b){b.setControlPointsVisibility(a)};m.setControlPointsVisibility.call(this,a);this.shapes.forEach(b);this.labels.forEach(b)};g.prototype.destroy=function(){var b=this.chart,c=function(a){a.destroy()};this.labels.forEach(c);this.shapes.forEach(c);this.clipYAxis=this.clipXAxis=null;q(b.labelCollectors,this.labelCollector);a.destroy.call(this);m.destroy.call(this);
p(this,b)};g.prototype.remove=function(){return this.chart.removeAnnotation(this)};g.prototype.update=function(a,b){var c=this.chart,g=this.getLabelsAndShapesOptions(this.userOptions,a),d=c.annotations.indexOf(this);a=z(!0,this.userOptions,a);a.labels=g.labels;a.shapes=g.shapes;this.destroy();this.constructor(c,a);c.options.annotations[d]=a;this.isUpdating=!0;C(b,!0)&&c.redraw();B(this,"afterUpdate");this.isUpdating=!1};g.prototype.initShape=function(a,b){a=z(this.options.shapeOptions,{controlPointOptions:this.options.controlPointOptions},
a);b=new g.shapesMap[a.type](this,a,b);b.itemType="shape";this.shapes.push(b);return b};g.prototype.initLabel=function(a,b){a=z(this.options.labelOptions,{controlPointOptions:this.options.controlPointOptions},a);b=new c(this,a,b);b.itemType="label";this.labels.push(b);return b};g.prototype.redrawItem=function(a,b){a.linkPoints();a.shouldBeDrawn()?(a.graphic||this.renderItem(a),a.redraw(C(b,!0)&&a.graphic.placed),a.points.length&&this.adjustVisibility(a)):this.destroyItem(a)};g.prototype.adjustVisibility=
function(a){var b=!1,c=a.graphic;a.points.forEach(function(a){!1!==a.series.visible&&!1!==a.visible&&(b=!0)});b?"hidden"===c.visibility&&c.show():c.hide()};g.prototype.destroyItem=function(a){q(this[a.itemType+"s"],a);a.destroy()};g.prototype.renderItem=function(a){a.render("label"===a.itemType?this.labelsGroup:this.shapesGroup)};g.ControlPoint=b;g.MockPoint=l;g.shapesMap={rect:k,circle:n,path:r,image:d};g.types={};return g}();z(!0,E.prototype,m,a,z(E.prototype,{nonDOMEvents:["add","afterUpdate",
"drag","remove"],defaultOptions:{visible:!0,animation:{},draggable:"xy",labelOptions:{align:"center",allowOverlap:!1,backgroundColor:"rgba(0, 0, 0, 0.75)",borderColor:"black",borderRadius:3,borderWidth:1,className:"",crop:!1,formatter:function(){return D(this.y)?this.y:"Annotation label"},includeInDataExport:!0,overflow:"justify",padding:5,shadow:!1,shape:"callout",style:{fontSize:"11px",fontWeight:"normal",color:"contrast"},useHTML:!1,verticalAlign:"bottom",x:0,y:-16},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",
strokeWidth:1,fill:"rgba(0, 0, 0, 0.75)",r:0,snap:2},controlPointOptions:{symbol:"circle",width:10,height:10,style:{stroke:"black","stroke-width":2,fill:"white"},visible:!1,events:{}},events:{},zIndex:6}}));g.extendAnnotation=function(a,b,c,g){b=b||E;z(!0,a.prototype,b.prototype,c);a.prototype.defaultOptions=z(a.prototype.defaultOptions,g||{})};A(e,{initAnnotation:function(a){a=new (E.types[a.type]||E)(this,a);this.annotations.push(a);return a},addAnnotation:function(a,b){a=this.initAnnotation(a);
this.options.annotations.push(a.options);C(b,!0)&&(a.redraw(),a.graphic.attr({opacity:1}));return a},removeAnnotation:function(a){var b=this.annotations,c="annotations"===a.coll?a:u(b,function(b){return b.options.id===a});c&&(B(c,"remove"),q(this.options.annotations,c.options),q(b,c),c.destroy())},drawAnnotations:function(){this.plotBoxClip.attr(this.plotBox);this.annotations.forEach(function(a){a.redraw();a.graphic.animate({opacity:1},a.animationConfig)})}});e.collectionsWithUpdate.push("annotations");
e.collectionsWithInit.annotations=[e.addAnnotation];x(h,"afterInit",function(){this.annotations=[];this.options.annotations||(this.options.annotations=[])});e.callbacks.push(function(a){a.plotBoxClip=this.renderer.clipRect(this.plotBox);a.controlPointsGroup=a.renderer.g("control-points").attr({zIndex:99}).clip(a.plotBoxClip).add();a.options.annotations.forEach(function(b,c){if(!a.annotations.some(function(a){return a.options===b})){var g=a.initAnnotation(b);a.options.annotations[c]=g.options}});a.drawAnnotations();
x(a,"redraw",a.drawAnnotations);x(a,"destroy",function(){a.plotBoxClip.destroy();a.controlPointsGroup.destroy()});x(a,"exportData",function(b){var c,g,d,e,l,f,w,p,z=a.annotations,q=(this.options.exporting&&this.options.exporting.csv||{}).columnHeaderFormatter,B=!b.dataRows[1].xValues,u=null===(g=null===(c=a.options.lang)||void 0===c?void 0:c.exportData)||void 0===g?void 0:g.annotationHeader;c=function(a){if(q){var b=q(a);if(!1!==b)return b}b=u+" "+a;return B?{columnTitle:b,topLevelColumnTitle:b}:
b};var C=b.dataRows[0].length,k=null===(l=null===(e=null===(d=a.options.exporting)||void 0===d?void 0:d.csv)||void 0===e?void 0:e.annotations)||void 0===l?void 0:l.itemDelimiter,h=null===(p=null===(w=null===(f=a.options.exporting)||void 0===f?void 0:f.csv)||void 0===w?void 0:w.annotations)||void 0===p?void 0:p.join;z.forEach(function(a){a.options.labelOptions.includeInDataExport&&a.labels.forEach(function(a){if(a.options.text){var c=a.options.text;a.points.forEach(function(a){var g=a.x,d=a.series.xAxis?
a.series.xAxis.options.index:-1,e=!1;if(-1===d){a=b.dataRows[0].length;for(var l=Array(a),f=0;f<a;++f)l[f]="";l.push(c);l.xValues=[];l.xValues[d]=g;b.dataRows.push(l);e=!0}e||b.dataRows.forEach(function(a,b){!e&&a.xValues&&void 0!==d&&g===a.xValues[d]&&(h&&a.length>C?a[a.length-1]+=k+c:a.push(c),e=!0)});if(!e){a=b.dataRows[0].length;l=Array(a);for(f=0;f<a;++f)l[f]="";l[0]=g;l.push(c);l.xValues=[];void 0!==d&&(l.xValues[d]=g);b.dataRows.push(l)}})}})});var v=0;b.dataRows.forEach(function(a){v=Math.max(v,
a.length)});d=v-b.dataRows[0].length;for(e=0;e<d;e++)l=c(e+1),B?(b.dataRows[0].push(l.topLevelColumnTitle),b.dataRows[1].push(l.columnTitle)):b.dataRows[0].push(l)})});v(w.prototype,"onContainerMouseDown",function(a){this.chart.hasDraggedAnnotation||a.apply(this,Array.prototype.slice.call(arguments,1))});return g.Annotation=E});t(e,"Extensions/Annotations/Types/BasicAnnotation.js",[e["Extensions/Annotations/Annotations.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Utilities.js"]],function(e,
h,m){var f=this&&this.__extends||function(){var e=function(d,c){e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(b,a){b.__proto__=a}||function(b,a){for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c])};return e(d,c)};return function(d,c){function b(){this.constructor=d}e(d,c);d.prototype=null===c?Object.create(c):(b.prototype=c.prototype,new b)}}();m=m.merge;var n=function(k){function d(c,b){return k.call(this,c,b)||this}f(d,k);d.prototype.addControlPoints=function(){var c=this.options,
b=d.basicControlPoints,a=this.basicType;(c.labels||c.shapes).forEach(function(c){c.controlPoints=b[a]})};d.prototype.init=function(){var c=this.options;c.shapes?(delete c.labelOptions,this.basicType="circle"===c.shapes[0].type?"circle":"rectangle"):(delete c.shapes,this.basicType="label");e.prototype.init.apply(this,arguments)};d.basicControlPoints={label:[{symbol:"triangle-down",positioner:function(c){if(!c.graphic.placed)return{x:0,y:-9E7};c=h.pointToPixels(c.points[0]);return{x:c.x-this.graphic.width/
2,y:c.y-this.graphic.height/2}},events:{drag:function(c,b){c=this.mouseMoveToTranslation(c);b.translatePoint(c.x,c.y);b.annotation.userOptions.labels[0].point=b.options.point;b.redraw(!1)}}},{symbol:"square",positioner:function(c){return c.graphic.placed?{x:c.graphic.alignAttr.x-this.graphic.width/2,y:c.graphic.alignAttr.y-this.graphic.height/2}:{x:0,y:-9E7}},events:{drag:function(c,b){c=this.mouseMoveToTranslation(c);b.translate(c.x,c.y);b.annotation.userOptions.labels[0].point=b.options.point;b.redraw(!1)}}}],
rectangle:[{positioner:function(c){c=h.pointToPixels(c.points[2]);return{x:c.x-4,y:c.y-4}},events:{drag:function(c,b){var a=b.annotation,g=this.chart.pointer.getCoordinates(c);c=g.xAxis[0].value;g=g.yAxis[0].value;var d=b.options.points;d[1].x=c;d[2].x=c;d[2].y=g;d[3].y=g;a.userOptions.shapes[0].points=b.options.points;a.redraw(!1)}}}],circle:[{positioner:function(c){var b=h.pointToPixels(c.points[0]);c=c.options.r;return{x:b.x+c*Math.cos(Math.PI/4)-this.graphic.width/2,y:b.y+c*Math.sin(Math.PI/4)-
this.graphic.height/2}},events:{drag:function(c,b){var a=b.annotation;c=this.mouseMoveToTranslation(c);b.setRadius(Math.max(b.options.r+c.y/Math.sin(Math.PI/4),5));a.userOptions.shapes[0].r=b.options.r;a.userOptions.shapes[0].point=b.options.point;b.redraw(!1)}}}]};return d}(e);n.prototype.defaultOptions=m(e.prototype.defaultOptions,{});return e.types.basicAnnotation=n});t(e,"Extensions/Annotations/Types/CrookedLine.js",[e["Extensions/Annotations/Annotations.js"],e["Extensions/Annotations/ControlPoint.js"],
e["Extensions/Annotations/MockPoint.js"],e["Core/Utilities.js"]],function(e,h,m,k){var f=this&&this.__extends||function(){var d=function(c,b){d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])};return d(c,b)};return function(c,b){function a(){this.constructor=c}d(c,b);c.prototype=null===b?Object.create(b):(a.prototype=b.prototype,new a)}}(),r=k.merge;k=function(d){function c(b,a){return d.call(this,b,
a)||this}f(c,d);c.prototype.setClipAxes=function(){this.clipXAxis=this.chart.xAxis[this.options.typeOptions.xAxis];this.clipYAxis=this.chart.yAxis[this.options.typeOptions.yAxis]};c.prototype.getPointsOptions=function(){var b=this.options.typeOptions;return(b.points||[]).map(function(a){a.xAxis=b.xAxis;a.yAxis=b.yAxis;return a})};c.prototype.getControlPointsOptions=function(){return this.getPointsOptions()};c.prototype.addControlPoints=function(){this.getControlPointsOptions().forEach(function(b,
a){a=new h(this.chart,this,r(this.options.controlPointOptions,b.controlPoint),a);this.controlPoints.push(a);b.controlPoint=a.options},this)};c.prototype.addShapes=function(){var b=this.options.typeOptions,a=this.initShape(r(b.line,{type:"path",points:this.points.map(function(a,b){return function(a){return a.annotation.points[b]}})}),!1);b.line=a.options};return c}(e);k.prototype.defaultOptions=r(e.prototype.defaultOptions,{typeOptions:{xAxis:0,yAxis:0,line:{fill:"none"}},controlPointOptions:{positioner:function(d){var c=
this.graphic;d=m.pointToPixels(d.points[this.index]);return{x:d.x-c.width/2,y:d.y-c.height/2}},events:{drag:function(d,c){c.chart.isInsidePlot(d.chartX-c.chart.plotLeft,d.chartY-c.chart.plotTop)&&(d=this.mouseMoveToTranslation(d),c.translatePoint(d.x,d.y,this.index),c.options.typeOptions.points[this.index].x=c.points[this.index].x,c.options.typeOptions.points[this.index].y=c.points[this.index].y,c.redraw(!1))}}}});return e.types.crookedLine=k});t(e,"Extensions/Annotations/Types/ElliottWave.js",[e["Extensions/Annotations/Annotations.js"],
e["Extensions/Annotations/Types/CrookedLine.js"],e["Core/Utilities.js"]],function(e,h,m){var f=this&&this.__extends||function(){var e=function(d,c){e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(b,a){b.__proto__=a}||function(b,a){for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c])};return e(d,c)};return function(d,c){function b(){this.constructor=d}e(d,c);d.prototype=null===c?Object.create(c):(b.prototype=c.prototype,new b)}}(),n=m.merge;m=function(e){function d(c,b){return e.call(this,
c,b)||this}f(d,e);d.prototype.addLabels=function(){this.getPointsOptions().forEach(function(c,b){var a=this.initLabel(n(c.label,{text:this.options.typeOptions.labels[b],point:function(a){return a.annotation.points[b]}}),!1);c.label=a.options},this)};return d}(h);m.prototype.defaultOptions=n(h.prototype.defaultOptions,{typeOptions:{labels:"(0) (A) (B) (C) (D) (E)".split(" "),line:{strokeWidth:1}},labelOptions:{align:"center",allowOverlap:!0,crop:!0,overflow:"none",type:"rect",backgroundColor:"none",
borderWidth:0,y:-5}});return e.types.elliottWave=m});t(e,"Extensions/Annotations/Types/Tunnel.js",[e["Extensions/Annotations/Annotations.js"],e["Extensions/Annotations/ControlPoint.js"],e["Extensions/Annotations/Types/CrookedLine.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Utilities.js"]],function(e,h,m,k,n){var f=this&&this.__extends||function(){var c=function(b,a){c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&
(a[c]=b[c])};return c(b,a)};return function(b,a){function g(){this.constructor=b}c(b,a);b.prototype=null===a?Object.create(a):(g.prototype=a.prototype,new g)}}(),d=n.merge;n=function(c){function b(a,b){return c.call(this,a,b)||this}f(b,c);b.prototype.getPointsOptions=function(){var a=m.prototype.getPointsOptions.call(this);a[2]=this.heightPointOptions(a[1]);a[3]=this.heightPointOptions(a[0]);return a};b.prototype.getControlPointsOptions=function(){return this.getPointsOptions().slice(0,2)};b.prototype.heightPointOptions=
function(a){a=d(a);a.y+=this.options.typeOptions.height;return a};b.prototype.addControlPoints=function(){m.prototype.addControlPoints.call(this);var a=this.options,b=a.typeOptions;a=new h(this.chart,this,d(a.controlPointOptions,b.heightControlPoint),2);this.controlPoints.push(a);b.heightControlPoint=a.options};b.prototype.addShapes=function(){this.addLine();this.addBackground()};b.prototype.addLine=function(){var a=this.initShape(d(this.options.typeOptions.line,{type:"path",points:[this.points[0],
this.points[1],function(a){a=k.pointToOptions(a.annotation.points[2]);a.command="M";return a},this.points[3]]}),!1);this.options.typeOptions.line=a.options};b.prototype.addBackground=function(){var a=this.initShape(d(this.options.typeOptions.background,{type:"path",points:this.points.slice()}));this.options.typeOptions.background=a.options};b.prototype.translateSide=function(a,b,c){c=Number(c);var g=0===c?3:2;this.translatePoint(a,b,c);this.translatePoint(a,b,g)};b.prototype.translateHeight=function(a){this.translatePoint(0,
a,2);this.translatePoint(0,a,3);this.options.typeOptions.height=this.points[3].y-this.points[0].y};return b}(m);n.prototype.defaultOptions=d(m.prototype.defaultOptions,{typeOptions:{xAxis:0,yAxis:0,background:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0},line:{strokeWidth:1},height:-2,heightControlPoint:{positioner:function(c){var b=k.pointToPixels(c.points[2]);c=k.pointToPixels(c.points[3]);var a=(b.x+c.x)/2;return{x:a-this.graphic.width/2,y:(c.y-b.y)/(c.x-b.x)*(a-b.x)+b.y-this.graphic.height/
2}},events:{drag:function(c,b){b.chart.isInsidePlot(c.chartX-b.chart.plotLeft,c.chartY-b.chart.plotTop)&&(b.translateHeight(this.mouseMoveToTranslation(c).y),b.redraw(!1))}}}},controlPointOptions:{events:{drag:function(c,b){b.chart.isInsidePlot(c.chartX-b.chart.plotLeft,c.chartY-b.chart.plotTop)&&(c=this.mouseMoveToTranslation(c),b.translateSide(c.x,c.y,this.index),b.redraw(!1))}}}});return e.types.tunnel=n});t(e,"Extensions/Annotations/Types/InfinityLine.js",[e["Extensions/Annotations/Annotations.js"],
e["Extensions/Annotations/Types/CrookedLine.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Utilities.js"]],function(e,h,m,k){var f=this&&this.__extends||function(){var d=function(c,b){d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])};return d(c,b)};return function(c,b){function a(){this.constructor=c}d(c,b);c.prototype=null===b?Object.create(b):(a.prototype=b.prototype,new a)}}(),r=k.merge;k=
function(d){function c(b,a){return d.call(this,b,a)||this}f(c,d);c.edgePoint=function(b,a){return function(g){g=g.annotation;var d=g.points,e=g.options.typeOptions.type;"horizontalLine"===e?d=[d[0],new m(g.chart,d[0].target,{x:d[0].x+1,y:d[0].y,xAxis:d[0].options.xAxis,yAxis:d[0].options.yAxis})]:"verticalLine"===e&&(d=[d[0],new m(g.chart,d[0].target,{x:d[0].x,y:d[0].y+1,xAxis:d[0].options.xAxis,yAxis:d[0].options.yAxis})]);return c.findEdgePoint(d[b],d[a])}};c.findEdgeCoordinate=function(b,a,c,d){var g=
"x"===c?"y":"x";return(a[c]-b[c])*(d-b[g])/(a[g]-b[g])+b[c]};c.findEdgePoint=function(b,a){var g=b.series.xAxis,d=a.series.yAxis,e=m.pointToPixels(b),f=m.pointToPixels(a),k=f.x-e.x,h=f.y-e.y;a=g.left;var n=a+g.width;g=d.top;d=g+d.height;var p=0>k?a:n,q=0>h?g:d;n={x:0===k?e.x:p,y:0===h?e.y:q};0!==k&&0!==h&&(k=c.findEdgeCoordinate(e,f,"y",p),e=c.findEdgeCoordinate(e,f,"x",q),k>=g&&k<=d?(n.x=p,n.y=k):(n.x=e,n.y=q));n.x-=a;n.y-=g;b.series.chart.inverted&&(b=n.x,n.x=n.y,n.y=b);return n};c.prototype.addShapes=
function(){var b=this.options.typeOptions,a=[this.points[0],c.endEdgePoint];b.type.match(/Line/g)&&(a[0]=c.startEdgePoint);a=this.initShape(r(b.line,{type:"path",points:a}),!1);b.line=a.options};c.endEdgePoint=c.edgePoint(0,1);c.startEdgePoint=c.edgePoint(1,0);return c}(h);k.prototype.defaultOptions=r(h.prototype.defaultOptions,{});return e.types.infinityLine=k});t(e,"Extensions/Annotations/Types/Fibonacci.js",[e["Extensions/Annotations/Annotations.js"],e["Extensions/Annotations/MockPoint.js"],e["Extensions/Annotations/Types/Tunnel.js"],
e["Core/Utilities.js"]],function(e,h,m,k){var f=this&&this.__extends||function(){var c=function(b,a){c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])};return c(b,a)};return function(b,a){function g(){this.constructor=b}c(b,a);b.prototype=null===a?Object.create(a):(g.prototype=a.prototype,new g)}}(),r=k.merge,d=function(c,b){return function(){var a=this.annotation,g=this.anchor(a.startRetracements[c]).absolutePosition,
d=this.anchor(a.endRetracements[c]).absolutePosition;g=[["M",Math.round(g.x),Math.round(g.y)],["L",Math.round(d.x),Math.round(d.y)]];b&&(d=this.anchor(a.endRetracements[c-1]).absolutePosition,a=this.anchor(a.startRetracements[c-1]).absolutePosition,g.push(["L",Math.round(d.x),Math.round(d.y)],["L",Math.round(a.x),Math.round(a.y)]));return g}};k=function(c){function b(a,b){return c.call(this,a,b)||this}f(b,c);b.prototype.linkPoints=function(){c.prototype.linkPoints.call(this);this.linkRetracementsPoints()};
b.prototype.linkRetracementsPoints=function(){var a=this.points,c=a[0].y-a[3].y,d=a[1].y-a[2].y,e=a[0].x,f=a[1].x;b.levels.forEach(function(b,g){var l=a[0].y-c*b;b=a[1].y-d*b;this.startRetracements=this.startRetracements||[];this.endRetracements=this.endRetracements||[];this.linkRetracementPoint(g,e,l,this.startRetracements);this.linkRetracementPoint(g,f,b,this.endRetracements)},this)};b.prototype.linkRetracementPoint=function(a,b,c,d){var g=d[a],e=this.options.typeOptions;g?(g.options.x=b,g.options.y=
c,g.refresh()):d[a]=new h(this.chart,this,{x:b,y:c,xAxis:e.xAxis,yAxis:e.yAxis})};b.prototype.addShapes=function(){b.levels.forEach(function(a,b){this.initShape({type:"path",d:d(b)},!1);0<b&&this.initShape({type:"path",fill:this.options.typeOptions.backgroundColors[b-1],strokeWidth:0,d:d(b,!0)})},this)};b.prototype.addLabels=function(){b.levels.forEach(function(a,b){var c=this.options.typeOptions;a=this.initLabel(r(c.labels[b],{point:function(a){return h.pointToOptions(a.annotation.startRetracements[b])},
text:a.toString()}));c.labels[b]=a.options},this)};b.levels=[0,.236,.382,.5,.618,.786,1];return b}(m);k.prototype.defaultOptions=r(m.prototype.defaultOptions,{typeOptions:{height:2,backgroundColors:"rgba(130, 170, 255, 0.4);rgba(139, 191, 216, 0.4);rgba(150, 216, 192, 0.4);rgba(156, 229, 161, 0.4);rgba(162, 241, 130, 0.4);rgba(169, 255, 101, 0.4)".split(";"),lineColor:"grey",lineColors:[],labels:[]},labelOptions:{allowOverlap:!0,align:"right",backgroundColor:"none",borderWidth:0,crop:!1,overflow:"none",
shape:"rect",style:{color:"grey"},verticalAlign:"middle",y:0}});return e.types.fibonacci=k});t(e,"Extensions/Annotations/Types/Pitchfork.js",[e["Extensions/Annotations/Annotations.js"],e["Extensions/Annotations/Types/InfinityLine.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Utilities.js"]],function(e,h,m,k){var f=this&&this.__extends||function(){var d=function(c,b){d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&
(a[c]=b[c])};return d(c,b)};return function(c,b){function a(){this.constructor=c}d(c,b);c.prototype=null===b?Object.create(b):(a.prototype=b.prototype,new a)}}(),r=k.merge;k=function(d){function c(b,a){return d.call(this,b,a)||this}f(c,d);c.outerLineEdgePoint=function(b){return function(a){var d=a.annotation,e=d.points;return c.findEdgePoint(e[b],e[0],new m(d.chart,a,d.midPointOptions()))}};c.findEdgePoint=function(b,a,c){a=Math.atan2(c.plotY-a.plotY,c.plotX-a.plotX);return{x:b.plotX+1E7*Math.cos(a),
y:b.plotY+1E7*Math.sin(a)}};c.middleLineEdgePoint=function(b){var a=b.annotation;return h.findEdgePoint(a.points[0],new m(a.chart,b,a.midPointOptions()))};c.prototype.midPointOptions=function(){var b=this.points;return{x:(b[1].x+b[2].x)/2,y:(b[1].y+b[2].y)/2,xAxis:b[0].series.xAxis,yAxis:b[0].series.yAxis}};c.prototype.addShapes=function(){this.addLines();this.addBackgrounds()};c.prototype.addLines=function(){this.initShape({type:"path",points:[this.points[0],c.middleLineEdgePoint]},!1);this.initShape({type:"path",
points:[this.points[1],c.topLineEdgePoint]},!1);this.initShape({type:"path",points:[this.points[2],c.bottomLineEdgePoint]},!1)};c.prototype.addBackgrounds=function(){var b=this.shapes,a=this.options.typeOptions,c=this.initShape(r(a.innerBackground,{type:"path",points:[function(a){var b=a.annotation;a=b.points;b=b.midPointOptions();return{x:(a[1].x+b.x)/2,y:(a[1].y+b.y)/2,xAxis:b.xAxis,yAxis:b.yAxis}},b[1].points[1],b[2].points[1],function(a){var b=a.annotation;a=b.points;b=b.midPointOptions();return{x:(b.x+
a[2].x)/2,y:(b.y+a[2].y)/2,xAxis:b.xAxis,yAxis:b.yAxis}}]}));b=this.initShape(r(a.outerBackground,{type:"path",points:[this.points[1],b[1].points[1],b[2].points[1],this.points[2]]}));a.innerBackground=c.options;a.outerBackground=b.options};c.topLineEdgePoint=c.outerLineEdgePoint(1);c.bottomLineEdgePoint=c.outerLineEdgePoint(0);return c}(h);k.prototype.defaultOptions=r(h.prototype.defaultOptions,{typeOptions:{innerBackground:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0},outerBackground:{fill:"rgba(156, 229, 161, 0.4)",
strokeWidth:0}}});return e.types.pitchfork=k});t(e,"Extensions/Annotations/Types/VerticalLine.js",[e["Extensions/Annotations/Annotations.js"],e["Extensions/Annotations/MockPoint.js"],e["Core/Utilities.js"]],function(e,h,m){var f=this&&this.__extends||function(){var e=function(d,c){e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(b,a){b.__proto__=a}||function(b,a){for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c])};return e(d,c)};return function(d,c){function b(){this.constructor=d}e(d,
c);d.prototype=null===c?Object.create(c):(b.prototype=c.prototype,new b)}}(),n=m.merge;m=function(e){function d(c,b){return e.call(this,c,b)||this}f(d,e);d.connectorFirstPoint=function(c){c=c.annotation;var b=c.points[0],a=h.pointToPixels(b,!0),d=a.y,e=c.options.typeOptions.label.offset;c.chart.inverted&&(d=a.x);return{x:b.x,xAxis:b.series.xAxis,y:d+e}};d.connectorSecondPoint=function(c){var b=c.annotation;c=b.options.typeOptions;var a=b.points[0],d=c.yOffset;b=h.pointToPixels(a,!0)[b.chart.inverted?
"x":"y"];0>c.label.offset&&(d*=-1);return{x:a.x,xAxis:a.series.xAxis,y:b+d}};d.prototype.getPointsOptions=function(){return[this.options.typeOptions.point]};d.prototype.addShapes=function(){var c=this.options.typeOptions,b=this.initShape(n(c.connector,{type:"path",points:[d.connectorFirstPoint,d.connectorSecondPoint]}),!1);c.connector=b.options};d.prototype.addLabels=function(){var c=this.options.typeOptions,b=c.label,a=0,d=b.offset,e=0>b.offset?"bottom":"top",f="center";this.chart.inverted&&(a=b.offset,
d=0,e="middle",f=0>b.offset?"right":"left");b=this.initLabel(n(b,{verticalAlign:e,align:f,x:a,y:d}));c.label=b.options};return d}(e);m.prototype.defaultOptions=n(e.prototype.defaultOptions,{typeOptions:{yOffset:10,label:{offset:-40,point:function(e){return e.annotation.points[0]},allowOverlap:!0,backgroundColor:"none",borderWidth:0,crop:!0,overflow:"none",shape:"rect",text:"{y:.2f}"},connector:{strokeWidth:1,markerEnd:"arrow"}}});return e.types.verticalLine=m});t(e,"Extensions/Annotations/Types/Measure.js",
[e["Extensions/Annotations/Annotations.js"],e["Extensions/Annotations/ControlPoint.js"],e["Core/Utilities.js"]],function(e,h,m){var f=this&&this.__extends||function(){var b=function(a,c){b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])};return b(a,c)};return function(a,c){function d(){this.constructor=a}b(a,c);a.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}(),n=m.extend,r=m.isNumber,
d=m.merge,c=function(b){function a(a,c){return b.call(this,a,c)||this}f(a,b);a.prototype.init=function(b,c,d){e.prototype.init.call(this,b,c,d);this.resizeY=this.resizeX=this.offsetY=this.offsetX=0;a.calculations.init.call(this);this.addValues();this.addShapes()};a.prototype.setClipAxes=function(){this.clipXAxis=this.chart.xAxis[this.options.typeOptions.xAxis];this.clipYAxis=this.chart.yAxis[this.options.typeOptions.yAxis]};a.prototype.pointsOptions=function(){return this.options.points};a.prototype.shapePointsOptions=
function(){var a=this.options.typeOptions,b=a.xAxis;a=a.yAxis;return[{x:this.xAxisMin,y:this.yAxisMin,xAxis:b,yAxis:a},{x:this.xAxisMax,y:this.yAxisMin,xAxis:b,yAxis:a},{x:this.xAxisMax,y:this.yAxisMax,xAxis:b,yAxis:a},{x:this.xAxisMin,y:this.yAxisMax,xAxis:b,yAxis:a}]};a.prototype.addControlPoints=function(){var a=this.options.typeOptions.selectType;var b=new h(this.chart,this,this.options.controlPointOptions,0);this.controlPoints.push(b);"xy"!==a&&(b=new h(this.chart,this,this.options.controlPointOptions,
1),this.controlPoints.push(b))};a.prototype.addValues=function(b){var c=this.options.typeOptions,d=c.label.formatter;a.calculations.recalculate.call(this,b);c.label.enabled&&(0<this.labels.length?this.labels[0].text=d&&d.call(this)||a.calculations.defaultFormatter.call(this):this.initLabel(n({shape:"rect",backgroundColor:"none",color:"black",borderWidth:0,dashStyle:"dash",overflow:"none",align:"left",vertical:"top",crop:!0,point:function(a){a=a.annotation;var b=a.chart,d=b.inverted,e=b.yAxis[c.yAxis],
g=b.plotTop,f=b.plotLeft;return{x:(d?g:10)+b.xAxis[c.xAxis].toPixels(a.xAxisMin,!d),y:(d?-f+10:g)+e.toPixels(a.yAxisMin)}},text:d&&d.call(this)||a.calculations.defaultFormatter.call(this)},c.label)))};a.prototype.addShapes=function(){this.addCrosshairs();this.addBackground()};a.prototype.addBackground=function(){"undefined"!==typeof this.shapePointsOptions()[0].x&&this.initShape(n({type:"path",points:this.shapePointsOptions()},this.options.typeOptions.background),!1)};a.prototype.addCrosshairs=function(){var a=
this.chart,b=this.options.typeOptions,c=this.options.typeOptions.point,e=a.xAxis[b.xAxis],f=a.yAxis[b.yAxis],k=a.inverted;a=e.toPixels(this.xAxisMin);e=e.toPixels(this.xAxisMax);var h=f.toPixels(this.yAxisMin),p=f.toPixels(this.yAxisMax),q={point:c,type:"path"};c=[];f=[];k&&(k=a,a=h,h=k,k=e,e=p,p=k);b.crosshairX.enabled&&(c=[["M",a,h+(p-h)/2],["L",e,h+(p-h)/2]]);b.crosshairY.enabled&&(f=[["M",a+(e-a)/2,h],["L",a+(e-a)/2,p]]);0<this.shapes.length?(this.shapes[0].options.d=c,this.shapes[1].options.d=
f):(a=d(q,b.crosshairX),b=d(q,b.crosshairY),this.initShape(n({d:c},a),!1),this.initShape(n({d:f},b),!1))};a.prototype.onDrag=function(a){var b=this.mouseMoveToTranslation(a),c=this.options.typeOptions.selectType;a="y"===c?0:b.x;b="x"===c?0:b.y;this.translate(a,b);this.offsetX+=a;this.offsetY+=b;this.redraw(!1,!1,!0)};a.prototype.resize=function(b,c,d,e){var g=this.shapes[2];"x"===e?0===d?(g.translatePoint(b,0,0),g.translatePoint(b,c,3)):(g.translatePoint(b,0,1),g.translatePoint(b,c,2)):"y"===e?0===
d?(g.translatePoint(0,c,0),g.translatePoint(0,c,1)):(g.translatePoint(0,c,2),g.translatePoint(0,c,3)):(g.translatePoint(b,0,1),g.translatePoint(b,c,2),g.translatePoint(0,c,3));a.calculations.updateStartPoints.call(this,!1,!0,d,b,c);this.options.typeOptions.background.height=Math.abs(this.startYMax-this.startYMin);this.options.typeOptions.background.width=Math.abs(this.startXMax-this.startXMin)};a.prototype.redraw=function(b,c,d){this.linkPoints();this.graphic||this.render();d&&a.calculations.updateStartPoints.call(this,
!0,!1);this.clipRect&&this.clipRect.animate(this.getClipBox());this.addValues(c);this.addCrosshairs();this.redrawItems(this.shapes,b);this.redrawItems(this.labels,b);this.controlPoints.forEach(function(a){a.redraw()})};a.prototype.translate=function(a,b){this.shapes.forEach(function(c){c.translate(a,b)});this.options.typeOptions.point.x=this.startXMin;this.options.typeOptions.point.y=this.startYMin};a.calculations={init:function(){var b=this.options.typeOptions,c=this.chart,d=a.calculations.getPointPos,
e=c.inverted,f=c.xAxis[b.xAxis];c=c.yAxis[b.yAxis];var h=b.background,k=e?h.height:h.width;h=e?h.width:h.height;var p=b.selectType,q=e?f.left:c.top;e=e?c.top:f.left;this.startXMin=b.point.x;this.startYMin=b.point.y;r(k)?this.startXMax=this.startXMin+k:this.startXMax=d(f,this.startXMin,parseFloat(k));r(h)?this.startYMax=this.startYMin-h:this.startYMax=d(c,this.startYMin,parseFloat(h));"x"===p?(this.startYMin=c.toValue(q),this.startYMax=c.toValue(q+c.len)):"y"===p&&(this.startXMin=f.toValue(e),this.startXMax=
f.toValue(e+f.len))},recalculate:function(b){var c=a.calculations,d=this.options.typeOptions,e=this.chart.xAxis[d.xAxis];d=this.chart.yAxis[d.yAxis];var g=a.calculations.getPointPos,f=this.offsetX,h=this.offsetY;this.xAxisMin=g(e,this.startXMin,f);this.xAxisMax=g(e,this.startXMax,f);this.yAxisMin=g(d,this.startYMin,h);this.yAxisMax=g(d,this.startYMax,h);this.min=c.min.call(this);this.max=c.max.call(this);this.average=c.average.call(this);this.bins=c.bins.call(this);b&&this.resize(0,0)},getPointPos:function(a,
b,c){return a.toValue(a.toPixels(b)+c)},updateStartPoints:function(b,c,d,e,f){var g=this.options.typeOptions,h=g.selectType,p=this.chart.xAxis[g.xAxis];g=this.chart.yAxis[g.yAxis];var q=a.calculations.getPointPos,k=this.startXMin,u=this.startXMax,B=this.startYMin,z=this.startYMax,C=this.offsetX,l=this.offsetY;c&&("x"===h?0===d?this.startXMin=q(p,k,e):this.startXMax=q(p,u,e):"y"===h?0===d?this.startYMin=q(g,B,f):this.startYMax=q(g,z,f):(this.startXMax=q(p,u,e),this.startYMax=q(g,z,f)));b&&(this.startXMin=
q(p,k,C),this.startXMax=q(p,u,C),this.startYMin=q(g,B,l),this.startYMax=q(g,z,l),this.offsetY=this.offsetX=0)},defaultFormatter:function(){return"Min: "+this.min+"<br>Max: "+this.max+"<br>Average: "+this.average+"<br>Bins: "+this.bins},getExtremes:function(a,b,c,d){return{xAxisMin:Math.min(b,a),xAxisMax:Math.max(b,a),yAxisMin:Math.min(d,c),yAxisMax:Math.max(d,c)}},min:function(){var b=Infinity,c=this.chart.series,d=a.calculations.getExtremes(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),
e=!1;c.forEach(function(a){a.visible&&"highcharts-navigator-series"!==a.options.id&&a.points.forEach(function(a){!a.isNull&&a.y<b&&a.x>d.xAxisMin&&a.x<=d.xAxisMax&&a.y>d.yAxisMin&&a.y<=d.yAxisMax&&(b=a.y,e=!0)})});e||(b="");return b},max:function(){var b=-Infinity,c=this.chart.series,d=a.calculations.getExtremes(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),e=!1;c.forEach(function(a){a.visible&&"highcharts-navigator-series"!==a.options.id&&a.points.forEach(function(a){!a.isNull&&a.y>b&&
a.x>d.xAxisMin&&a.x<=d.xAxisMax&&a.y>d.yAxisMin&&a.y<=d.yAxisMax&&(b=a.y,e=!0)})});e||(b="");return b},average:function(){var a="";""!==this.max&&""!==this.min&&(a=(this.max+this.min)/2);return a},bins:function(){var b=0,c=this.chart.series,d=a.calculations.getExtremes(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),e=!1;c.forEach(function(a){a.visible&&"highcharts-navigator-series"!==a.options.id&&a.points.forEach(function(a){!a.isNull&&a.x>d.xAxisMin&&a.x<=d.xAxisMax&&a.y>d.yAxisMin&&a.y<=
d.yAxisMax&&(b++,e=!0)})});e||(b="");return b}};return a}(e);c.prototype.defaultOptions=d(e.prototype.defaultOptions,{typeOptions:{selectType:"xy",xAxis:0,yAxis:0,background:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0,stroke:void 0},crosshairX:{enabled:!0,zIndex:6,dashStyle:"Dash",markerEnd:"arrow"},crosshairY:{enabled:!0,zIndex:6,dashStyle:"Dash",markerEnd:"arrow"},label:{enabled:!0,style:{fontSize:"11px",color:"#666666"},formatter:void 0}},controlPointOptions:{positioner:function(b){var a=this.index,
d=b.chart,e=b.options,f=e.typeOptions,h=f.selectType;e=e.controlPointOptions;var k=d.inverted,m=d.xAxis[f.xAxis];d=d.yAxis[f.yAxis];f=b.xAxisMax;var n=b.yAxisMax,p=c.calculations.getExtremes(b.xAxisMin,b.xAxisMax,b.yAxisMin,b.yAxisMax);"x"===h&&(n=(p.yAxisMax-p.yAxisMin)/2,0===a&&(f=b.xAxisMin));"y"===h&&(f=p.xAxisMin+(p.xAxisMax-p.xAxisMin)/2,0===a&&(n=b.yAxisMin));k?(b=d.toPixels(n),a=m.toPixels(f)):(b=m.toPixels(f),a=d.toPixels(n));return{x:b-e.width/2,y:a-e.height/2}},events:{drag:function(b,
a){var c=this.mouseMoveToTranslation(b);b=a.options.typeOptions.selectType;var d="y"===b?0:c.x;c="x"===b?0:c.y;a.resize(d,c,this.index,b);a.resizeX+=d;a.resizeY+=c;a.redraw(!1,!0)}}}});return e.types.measure=c});t(e,"Mixins/Navigation.js",[],function(){return{initUpdate:function(e){e.navigation||(e.navigation={updates:[],update:function(e,f){this.updates.forEach(function(h){h.update.call(h.context,e,f)})}})},addUpdate:function(e,h){h.navigation||this.initUpdate(h);h.navigation.updates.push({update:e,
context:h})}}});t(e,"Extensions/Annotations/NavigationBindings.js",[e["Extensions/Annotations/Annotations.js"],e["Core/Chart/Chart.js"],e["Mixins/Navigation.js"],e["Core/Globals.js"],e["Core/Utilities.js"]],function(e,h,m,k,n){function f(b){var c=b.prototype.defaultOptions.events&&b.prototype.defaultOptions.events.click;y(!0,b.prototype.defaultOptions.events,{click:function(b){var d=this,e=d.chart.navigationBindings,f=e.activeAnnotation;c&&c.call(d,b);f!==d?(e.deselectAnnotation(),e.activeAnnotation=
d,d.setControlPointsVisibility(!0),a(e,"showPopup",{annotation:d,formType:"annotation-toolbar",options:e.annotationToFields(d),onSubmit:function(a){var b={};"remove"===a.actionType?(e.activeAnnotation=!1,e.chart.removeAnnotation(d)):(e.fieldsToOptions(a.fields,b),e.deselectAnnotation(),a=b.typeOptions,"measure"===d.options.type&&(a.crosshairY.enabled=0!==a.crosshairY.strokeWidth,a.crosshairX.enabled=0!==a.crosshairX.strokeWidth),d.update(b))}})):a(e,"closePopup");b.activeAnnotation=!0}})}var d=n.addEvent,
c=n.attr,b=n.format,a=n.fireEvent,g=n.isArray,l=n.isFunction,t=n.isNumber,v=n.isObject,y=n.merge,x=n.objectEach,D=n.pick;n=n.setOptions;var p=k.doc,q=k.win,A=function(){function e(a,b){this.selectedButton=this.boundClassNames=void 0;this.chart=a;this.options=b;this.eventsToUnbind=[];this.container=p.getElementsByClassName(this.options.bindingsClassName||"")}e.prototype.initEvents=function(){var a=this,b=a.chart,c=a.container,e=a.options;a.boundClassNames={};x(e.bindings||{},function(b){a.boundClassNames[b.className]=
b});[].forEach.call(c,function(b){a.eventsToUnbind.push(d(b,"click",function(c){var e=a.getButtonEvents(b,c);e&&a.bindingsButtonClick(e.button,e.events,c)}))});x(e.events||{},function(b,c){l(b)&&a.eventsToUnbind.push(d(a,c,b,{passive:!1}))});a.eventsToUnbind.push(d(b.container,"click",function(c){!b.cancelClick&&b.isInsidePlot(c.chartX-b.plotLeft,c.chartY-b.plotTop)&&a.bindingsChartClick(this,c)}));a.eventsToUnbind.push(d(b.container,k.isTouchDevice?"touchmove":"mousemove",function(b){a.bindingsContainerMouseMove(this,
b)},k.isTouchDevice?{passive:!1}:void 0))};e.prototype.initUpdate=function(){var a=this;m.addUpdate(function(b){a.update(b)},this.chart)};e.prototype.bindingsButtonClick=function(b,c,e){var d=this.chart;this.selectedButtonElement&&(a(this,"deselectButton",{button:this.selectedButtonElement}),this.nextEvent&&(this.currentUserDetails&&"annotations"===this.currentUserDetails.coll&&d.removeAnnotation(this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1));this.selectedButton=c;this.selectedButtonElement=
b;a(this,"selectButton",{button:b});c.init&&c.init.call(this,b,e);(c.start||c.steps)&&d.renderer.boxWrapper.addClass("highcharts-draw-mode")};e.prototype.bindingsChartClick=function(b,c){b=this.chart;var e=this.selectedButton;b=b.renderer.boxWrapper;var d;if(d=this.activeAnnotation&&!c.activeAnnotation&&c.target.parentNode){a:{d=c.target;var f=q.Element.prototype,g=f.matches||f.msMatchesSelector||f.webkitMatchesSelector,p=null;if(f.closest)p=f.closest.call(d,".highcharts-popup");else{do{if(g.call(d,
".highcharts-popup"))break a;d=d.parentElement||d.parentNode}while(null!==d&&1===d.nodeType)}d=p}d=!d}d&&a(this,"closePopup");e&&e.start&&(this.nextEvent?(this.nextEvent(c,this.currentUserDetails),this.steps&&(this.stepIndex++,e.steps[this.stepIndex]?this.mouseMoveEvent=this.nextEvent=e.steps[this.stepIndex]:(a(this,"deselectButton",{button:this.selectedButtonElement}),b.removeClass("highcharts-draw-mode"),e.end&&e.end.call(this,c,this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1,this.selectedButton=
null))):(this.currentUserDetails=e.start.call(this,c),e.steps?(this.stepIndex=0,this.steps=!0,this.mouseMoveEvent=this.nextEvent=e.steps[this.stepIndex]):(a(this,"deselectButton",{button:this.selectedButtonElement}),b.removeClass("highcharts-draw-mode"),this.steps=!1,this.selectedButton=null,e.end&&e.end.call(this,c,this.currentUserDetails))))};e.prototype.bindingsContainerMouseMove=function(a,b){this.mouseMoveEvent&&this.mouseMoveEvent(b,this.currentUserDetails)};e.prototype.fieldsToOptions=function(a,
b){x(a,function(a,c){var e=parseFloat(a),d=c.split("."),f=b,g=d.length-1;!t(e)||a.match(/px/g)||c.match(/format/g)||(a=e);""!==a&&"undefined"!==a&&d.forEach(function(b,c){var e=D(d[c+1],"");g===c?f[b]=a:(f[b]||(f[b]=e.match(/\d/g)?[]:{}),f=f[b])})});return b};e.prototype.deselectAnnotation=function(){this.activeAnnotation&&(this.activeAnnotation.setControlPointsVisibility(!1),this.activeAnnotation=!1)};e.prototype.annotationToFields=function(a){function c(e,d,f,h){if(f&&e&&-1===k.indexOf(d)&&(0<=
(f.indexOf&&f.indexOf(d))||f[d]||!0===f))if(g(e))h[d]=[],e.forEach(function(a,b){v(a)?(h[d][b]={},x(a,function(a,e){c(a,e,p[d],h[d][b])})):c(a,0,p[d],h[d])});else if(v(e)){var u={};g(h)?(h.push(u),u[d]={},u=u[d]):h[d]=u;x(e,function(a,b){c(a,b,0===d?f:p[d],u)})}else"format"===d?h[d]=[b(e,a.labels[0].points[0]).toString(),"text"]:g(h)?h.push([e,q(e)]):h[d]=[e,q(e)]}var d=a.options,f=e.annotationsEditable,p=f.nestedOptions,q=this.utils.getFieldType,h=D(d.type,d.shapes&&d.shapes[0]&&d.shapes[0].type,
d.labels&&d.labels[0]&&d.labels[0].itemType,"label"),k=e.annotationsNonEditable[d.langKey]||[],u={langKey:d.langKey,type:h};x(d,function(a,b){"typeOptions"===b?(u[b]={},x(d[b],function(a,d){c(a,d,p,u[b],!0)})):c(a,b,f[h],u)});return u};e.prototype.getClickedClassNames=function(a,b){var d=b.target;b=[];for(var e;d&&((e=c(d,"class"))&&(b=b.concat(e.split(" ").map(function(a){return[a,d]}))),d=d.parentNode,d!==a););return b};e.prototype.getButtonEvents=function(a,b){var c=this,d;this.getClickedClassNames(a,
b).forEach(function(a){c.boundClassNames[a[0]]&&!d&&(d={events:c.boundClassNames[a[0]],button:a[1]})});return d};e.prototype.update=function(a){this.options=y(!0,this.options,a);this.removeEvents();this.initEvents()};e.prototype.removeEvents=function(){this.eventsToUnbind.forEach(function(a){a()})};e.prototype.destroy=function(){this.removeEvents()};e.annotationsEditable={nestedOptions:{labelOptions:["style","format","backgroundColor"],labels:["style"],label:["style"],style:["fontSize","color"],background:["fill",
"strokeWidth","stroke"],innerBackground:["fill","strokeWidth","stroke"],outerBackground:["fill","strokeWidth","stroke"],shapeOptions:["fill","strokeWidth","stroke"],shapes:["fill","strokeWidth","stroke"],line:["strokeWidth","stroke"],backgroundColors:[!0],connector:["fill","strokeWidth","stroke"],crosshairX:["strokeWidth","stroke"],crosshairY:["strokeWidth","stroke"]},circle:["shapes"],verticalLine:[],label:["labelOptions"],measure:["background","crosshairY","crosshairX"],fibonacci:[],tunnel:["background",
"line","height"],pitchfork:["innerBackground","outerBackground"],rect:["shapes"],crookedLine:[],basicAnnotation:["shapes","labelOptions"]};e.annotationsNonEditable={rectangle:["crosshairX","crosshairY","label"]};return e}();A.prototype.utils={updateRectSize:function(a,b){var c=b.chart,d=b.options.typeOptions,e=c.pointer.getCoordinates(a);a=e.xAxis[0].value-d.point.x;d=d.point.y-e.yAxis[0].value;b.update({typeOptions:{background:{width:c.inverted?d:a,height:c.inverted?a:d}}})},getFieldType:function(a){return{string:"text",
number:"number","boolean":"checkbox"}[typeof a]}};h.prototype.initNavigationBindings=function(){var a=this.options;a&&a.navigation&&a.navigation.bindings&&(this.navigationBindings=new A(this,a.navigation),this.navigationBindings.initEvents(),this.navigationBindings.initUpdate())};d(h,"load",function(){this.initNavigationBindings()});d(h,"destroy",function(){this.navigationBindings&&this.navigationBindings.destroy()});d(A,"deselectButton",function(){this.selectedButtonElement=null});d(e,"remove",function(){this.chart.navigationBindings&&
this.chart.navigationBindings.deselectAnnotation()});k.Annotation&&(f(e),x(e.types,function(a){f(a)}));n({lang:{navigation:{popup:{simpleShapes:"Simple shapes",lines:"Lines",circle:"Circle",rectangle:"Rectangle",label:"Label",shapeOptions:"Shape options",typeOptions:"Details",fill:"Fill",format:"Text",strokeWidth:"Line width",stroke:"Line color",title:"Title",name:"Name",labelOptions:"Label options",labels:"Labels",backgroundColor:"Background color",backgroundColors:"Background colors",borderColor:"Border color",
borderRadius:"Border radius",borderWidth:"Border width",style:"Style",padding:"Padding",fontSize:"Font size",color:"Color",height:"Height",shapes:"Shape options"}}},navigation:{bindingsClassName:"highcharts-bindings-container",bindings:{circleAnnotation:{className:"highcharts-circle-annotation",start:function(a){a=this.chart.pointer.getCoordinates(a);var b=this.chart.options.navigation;return this.chart.addAnnotation(y({langKey:"circle",type:"basicAnnotation",shapes:[{type:"circle",point:{xAxis:0,
yAxis:0,x:a.xAxis[0].value,y:a.yAxis[0].value},r:5}]},b.annotationsOptions,b.bindings.circleAnnotation.annotationsOptions))},steps:[function(a,b){var c=b.options.shapes[0].point,d=this.chart.xAxis[0].toPixels(c.x);c=this.chart.yAxis[0].toPixels(c.y);var e=this.chart.inverted;b.update({shapes:[{r:Math.max(Math.sqrt(Math.pow(e?c-a.chartX:d-a.chartX,2)+Math.pow(e?d-a.chartY:c-a.chartY,2)),5)}]})}]},rectangleAnnotation:{className:"highcharts-rectangle-annotation",start:function(a){var b=this.chart.pointer.getCoordinates(a);
a=this.chart.options.navigation;var c=b.xAxis[0].value;b=b.yAxis[0].value;return this.chart.addAnnotation(y({langKey:"rectangle",type:"basicAnnotation",shapes:[{type:"path",points:[{xAxis:0,yAxis:0,x:c,y:b},{xAxis:0,yAxis:0,x:c,y:b},{xAxis:0,yAxis:0,x:c,y:b},{xAxis:0,yAxis:0,x:c,y:b}]}]},a.annotationsOptions,a.bindings.rectangleAnnotation.annotationsOptions))},steps:[function(a,b){var c=b.options.shapes[0].points,d=this.chart.pointer.getCoordinates(a);a=d.xAxis[0].value;d=d.yAxis[0].value;c[1].x=
a;c[2].x=a;c[2].y=d;c[3].y=d;b.update({shapes:[{points:c}]})}]},labelAnnotation:{className:"highcharts-label-annotation",start:function(a){a=this.chart.pointer.getCoordinates(a);var b=this.chart.options.navigation;return this.chart.addAnnotation(y({langKey:"label",type:"basicAnnotation",labelOptions:{format:"{y:.2f}"},labels:[{point:{xAxis:0,yAxis:0,x:a.xAxis[0].value,y:a.yAxis[0].value},overflow:"none",crop:!0}]},b.annotationsOptions,b.bindings.labelAnnotation.annotationsOptions))}}},events:{},annotationsOptions:{animation:{defer:0}}}});
d(A,"closePopup",function(){this.deselectAnnotation()});return A});t(e,"Extensions/Annotations/Popup.js",[e["Core/Globals.js"],e["Extensions/Annotations/NavigationBindings.js"],e["Core/Pointer.js"],e["Core/Utilities.js"]],function(e,h,m,k){var f=e.isFirefox,r=k.addEvent,d=k.createElement,c=k.defined,b=k.fireEvent,a=k.getOptions,g=k.isArray,l=k.isObject,t=k.isString,v=k.objectEach,y=k.pick,x=k.stableSort;k=k.wrap;var D=/\d/g;k(m.prototype,"onContainerMouseDown",function(a,b){var c=b.target&&b.target.className;
t(c)&&0<=c.indexOf("highcharts-popup-field")||a.apply(this,Array.prototype.slice.call(arguments,1))});e.Popup=function(a,b,c){this.init(a,b,c)};e.Popup.prototype={init:function(a,b,c){this.chart=c;this.container=d("div",{className:"highcharts-popup"},null,a);this.lang=this.getLangpack();this.iconsURL=b;this.addCloseBtn()},addCloseBtn:function(){var a=this;var c=d("div",{className:"highcharts-popup-close"},null,this.container);c.style["background-image"]="url("+this.iconsURL+"close.svg)";["click",
"touchstart"].forEach(function(d){r(c,d,function(){b(a.chart.navigationBindings,"closePopup")})})},addColsContainer:function(a){var b=d("div",{className:"highcharts-popup-lhs-col"},null,a);a=d("div",{className:"highcharts-popup-rhs-col"},null,a);d("div",{className:"highcharts-popup-rhs-col-wrapper"},null,a);return{lhsCol:b,rhsCol:a}},addInput:function(a,b,c,e){var f=a.split(".");f=f[f.length-1];var g=this.lang;b="highcharts-"+b+"-"+f;b.match(D)||d("label",{innerHTML:g[f]||f,htmlFor:b},null,c);d("input",
{name:b,value:e[0],type:e[1],className:"highcharts-popup-field"},null,c).setAttribute("highcharts-data-name",a)},addButton:function(a,b,c,e,f){var g=this,h=this.closePopup,p=this.getFields;var k=d("button",{innerHTML:b},null,a);["click","touchstart"].forEach(function(a){r(k,a,function(){h.call(g);return e(p(f,c))})});return k},getFields:function(a,b){var c=a.querySelectorAll("input"),d=a.querySelectorAll("#highcharts-select-series > option:checked")[0];a=a.querySelectorAll("#highcharts-select-volume > option:checked")[0];
var e,f;var g={actionType:b,linkedTo:d&&d.getAttribute("value"),fields:{}};[].forEach.call(c,function(a){f=a.getAttribute("highcharts-data-name");(e=a.getAttribute("highcharts-data-series-id"))?g.seriesId=a.value:f?g.fields[f]=a.value:g.type=a.value});a&&(g.fields["params.volumeSeriesID"]=a.getAttribute("value"));return g},showPopup:function(){var a=this.container,b=a.querySelectorAll(".highcharts-popup-close")[0];a.innerHTML="";0<=a.className.indexOf("highcharts-annotation-toolbar")&&(a.classList.remove("highcharts-annotation-toolbar"),
a.removeAttribute("style"));a.appendChild(b);a.style.display="block"},closePopup:function(){this.popup.container.style.display="none"},showForm:function(a,b,c,d){this.popup=b.navigationBindings.popup;this.showPopup();"indicators"===a&&this.indicators.addForm.call(this,b,c,d);"annotation-toolbar"===a&&this.annotations.addToolbar.call(this,b,c,d);"annotation-edit"===a&&this.annotations.addForm.call(this,b,c,d);"flag"===a&&this.annotations.addForm.call(this,b,c,d,!0)},getLangpack:function(){return a().lang.navigation.popup},
annotations:{addToolbar:function(a,b,c){var e=this,f=this.lang,g=this.popup.container,h=this.showForm;-1===g.className.indexOf("highcharts-annotation-toolbar")&&(g.className+=" highcharts-annotation-toolbar");g.style.top=a.plotTop+10+"px";d("span",{innerHTML:y(f[b.langKey]||b.langKey,b.shapes&&b.shapes[0].type)},null,g);var k=this.addButton(g,f.removeButton||"remove","remove",c,g);k.className+=" highcharts-annotation-remove-button";k.style["background-image"]="url("+this.iconsURL+"destroy.svg)";k=
this.addButton(g,f.editButton||"edit","edit",function(){h.call(e,"annotation-edit",a,b,c)},g);k.className+=" highcharts-annotation-edit-button";k.style["background-image"]="url("+this.iconsURL+"edit.svg)"},addForm:function(a,b,c,e){var f=this.popup.container,g=this.lang;d("h2",{innerHTML:g[b.langKey]||b.langKey,className:"highcharts-popup-main-title"},null,f);var h=d("div",{className:"highcharts-popup-lhs-col highcharts-popup-lhs-full"},null,f);var k=d("div",{className:"highcharts-popup-bottom-row"},
null,f);this.annotations.addFormFields.call(this,h,a,"",b,[],!0);this.addButton(k,e?g.addButton||"add":g.saveButton||"save",e?"add":"save",c,f)},addFormFields:function(a,b,c,e,h,k){var p=this,q=this.annotations.addFormFields,m=this.addInput,n=this.lang,u,A;v(e,function(d,e){u=""!==c?c+"."+e:e;l(d)&&(!g(d)||g(d)&&l(d[0])?(A=n[e]||e,A.match(D)||h.push([!0,A,a]),q.call(p,a,b,u,d,h,!1)):h.push([p,u,"annotation",a,d]))});k&&(x(h,function(a){return a[1].match(/format/g)?-1:1}),f&&h.reverse(),h.forEach(function(a){!0===
a[0]?d("span",{className:"highcharts-annotation-title",innerHTML:a[1]},null,a[2]):m.apply(a[0],a.splice(1))}))}},indicators:{addForm:function(a,b,c){var d=this.indicators,e=this.lang;this.tabs.init.call(this,a);b=this.popup.container.querySelectorAll(".highcharts-tab-item-content");this.addColsContainer(b[0]);d.addIndicatorList.call(this,a,b[0],"add");var f=b[0].querySelectorAll(".highcharts-popup-rhs-col")[0];this.addButton(f,e.addButton||"add","add",c,f);this.addColsContainer(b[1]);d.addIndicatorList.call(this,
a,b[1],"edit");f=b[1].querySelectorAll(".highcharts-popup-rhs-col")[0];this.addButton(f,e.saveButton||"save","edit",c,f);this.addButton(f,e.removeButton||"remove","remove",c,f)},addIndicatorList:function(a,b,c){var e=this,f=b.querySelectorAll(".highcharts-popup-lhs-col")[0];b=b.querySelectorAll(".highcharts-popup-rhs-col")[0];var g="edit"===c,h=g?a.series:a.options.plotOptions,k=this.indicators.addFormFields,p;var l=d("ul",{className:"highcharts-indicator-list"},null,f);var q=b.querySelectorAll(".highcharts-popup-rhs-col-wrapper")[0];
v(h,function(b,c){var f=b.options;if(b.params||f&&f.params){var m=e.indicators.getNameType(b,c),n=m.type;p=d("li",{className:"highcharts-indicator-list",innerHTML:m.name},null,l);["click","touchstart"].forEach(function(c){r(p,c,function(){k.call(e,a,g?b:h[n],m.type,q);g&&b.options&&d("input",{type:"hidden",name:"highcharts-id-"+n,value:b.options.id},null,q).setAttribute("highcharts-data-series-id",b.options.id)})})}});0<l.childNodes.length&&l.childNodes[0].click()},getNameType:function(a,b){var c=
a.options,d=e.seriesTypes;d=d[b]&&d[b].prototype.nameBase||b.toUpperCase();c&&c.type&&(b=a.options.type,d=a.name);return{name:d,type:b}},listAllSeries:function(a,b,e,f,g){a="highcharts-"+b+"-type-"+a;var h;d("label",{innerHTML:this.lang[b]||b,htmlFor:a},null,f);var k=d("select",{name:a,className:"highcharts-popup-field"},null,f);k.setAttribute("id","highcharts-select-"+b);e.series.forEach(function(a){h=a.options;!h.params&&h.id&&"highcharts-navigator-series"!==h.id&&d("option",{innerHTML:h.name||
h.id,value:h.id},null,k)});c(g)&&(k.value=g)},addFormFields:function(a,b,c,e){var f=b.params||b.options.params,g=this.indicators.getNameType;e.innerHTML="";d("h3",{className:"highcharts-indicator-title",innerHTML:g(b,c).name},null,e);d("input",{type:"hidden",name:"highcharts-type-"+c,value:c},null,e);this.indicators.listAllSeries.call(this,c,"series",a,e,b.linkedParent&&f.volumeSeriesID);f.volumeSeriesID&&this.indicators.listAllSeries.call(this,c,"volume",a,e,b.linkedParent&&b.linkedParent.options.id);
this.indicators.addParamInputs.call(this,a,"params",f,c,e)},addParamInputs:function(a,b,c,d,e){var f=this,g=this.indicators.addParamInputs,h=this.addInput,k;v(c,function(c,p){k=b+"."+p;l(c)?g.call(f,a,k,c,d,e):"params.volumeSeriesID"!==k&&h.call(f,k,d,e,[c,"text"])})},getAmount:function(){var a=0;this.series.forEach(function(b){var c=b.options;(b.params||c&&c.params)&&a++});return a}},tabs:{init:function(a){var b=this.tabs;a=this.indicators.getAmount.call(a);var c=b.addMenuItem.call(this,"add");b.addMenuItem.call(this,
"edit",a);b.addContentItem.call(this,"add");b.addContentItem.call(this,"edit");b.switchTabs.call(this,a);b.selectTab.call(this,c,0)},addMenuItem:function(a,b){var c=this.popup.container,e="highcharts-tab-item",f=this.lang;0===b&&(e+=" highcharts-tab-disabled");b=d("span",{innerHTML:f[a+"Button"]||a,className:e},null,c);b.setAttribute("highcharts-data-tab-type",a);return b},addContentItem:function(){return d("div",{className:"highcharts-tab-item-content"},null,this.popup.container)},switchTabs:function(a){var b=
this,c;this.popup.container.querySelectorAll(".highcharts-tab-item").forEach(function(d,e){c=d.getAttribute("highcharts-data-tab-type");"edit"===c&&0===a||["click","touchstart"].forEach(function(a){r(d,a,function(){b.tabs.deselectAll.call(b);b.tabs.selectTab.call(b,this,e)})})})},selectTab:function(a,b){var c=this.popup.container.querySelectorAll(".highcharts-tab-item-content");a.className+=" highcharts-tab-item-active";c[b].className+=" highcharts-tab-item-show"},deselectAll:function(){var a=this.popup.container,
b=a.querySelectorAll(".highcharts-tab-item");a=a.querySelectorAll(".highcharts-tab-item-content");var c;for(c=0;c<b.length;c++)b[c].classList.remove("highcharts-tab-item-active"),a[c].classList.remove("highcharts-tab-item-show")}}};r(h,"showPopup",function(a){this.popup||(this.popup=new e.Popup(this.chart.container,this.chart.options.navigation.iconsURL||this.chart.options.stockTools&&this.chart.options.stockTools.gui.iconsURL||"https://code.highcharts.com/9.0.1/gfx/stock-icons/",this.chart));this.popup.showForm(a.formType,
this.chart,a.options,a.onSubmit)});r(h,"closePopup",function(){this.popup&&this.popup.closePopup()})});t(e,"masters/modules/annotations-advanced.src.js",[],function(){})});
//# sourceMappingURL=annotations-advanced.js.map