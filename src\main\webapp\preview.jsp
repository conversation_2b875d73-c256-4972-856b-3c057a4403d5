<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
<title>异常指标预览</title>
 <link href="res/css/layui.css" type="text/css" rel="stylesheet">
<style>
        body {
            background-color: rgba(255, 255, 255, 0.5); 
        },
        xm-select > .xm-body .scroll-body {
    max-width: 100px;
};
    </style>
</head>

<body >
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
      <legend>得分详情</legend>
    </fieldset>
    <table class="layui-hide" id="score_detail" ></table>
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
      <legend>异常指标汇总</legend>
    </fieldset>
	<table class="layui-hide" id="preview" ></table>
    <script type="text/javascript" src="res/jquery.min.js"></script>
    <script type="text/javascript" src="res/layui.js"></script>
    <script type="text/javascript" src="res/inittestzs.js?v=11"></script>
    <script>
        // 获取URL中的查询字符串部分
        const queryString = window.location.search;
        // 解析查询字符串
        const urlParams = new URLSearchParams(queryString);
        // 获取参数值
        const dataid = urlParams.get('dataid');
        const buildno = urlParams.get('buildno');
		console.log(buildno);
        layui.use('table', function(){
          var table = layui.table;

          table.render({
            elem: '#score_detail'
            ,url: 'https://wxiot.zte.com.cn/iwork/score_detail/'+dataid+'/'+encodeURIComponent (buildno) 
            ,page: false //开启分页
            ,cols: [[ //表头
              {field: '测试编号', title: '测试编号',width: '10%'}
              ,{field: '指标类型', title: '指标类型',width: '30%'}
              ,{field: '正常', title: '正常',width: '10%'}
              ,{field: '继续观察', title: '继续观察',width: '10%'}
              ,{field: '恶化', title: '恶化',width: '10%'}
              ,{field: '改善', title: '改善',width: '10%'}
              ,{field: '总计', title: '总计',width: '10%'}
              ,{field: '得分', title: '得分',width: '10%'}
            ]]
            ,text:{none:'当前任务暂无得分详情！'},
          });
        });
        layui.use('table', function(){
          var table = layui.table;

          table.render({
            elem: '#preview'
            ,url: 'https://wxiot.zte.com.cn/iwork/abnormal_kpi/'+dataid+'/'+encodeURIComponent (buildno) 
            ,page: false //开启分页
            ,cols: [[ //表头
              {field: '测试编号', title: '测试编号',width: '3%'}
              ,{field: '序号', title: '序号',width: '3%'}
              ,{field: '类别', title: '类别',width: '5%'}
              ,{field: '类型', title: '类型',width: '5%'}
              ,{field: 'KPI指标', title: 'KPI指标',width: '16%'}
              ,{field: '单位', title: '单位',width: '5%'}
              ,{field: '升级前', title: '升级前',width: '10%'}
              ,{field: '升级后', title: '升级后',width: '10%'}
              ,{field: '归属', title: '归属',width: '5%'}
              ,{field: '差值', title: '差值',width: '10%'}
              ,{field: '辐度', title: '辐度',width: '10%'}
              ,{field: '趋势', title: '趋势',width: '3%'}
              ,{field: '标准差异常', title: '标准差异常',width: '5%'}
              ,{field: '结果', title: '结果',width: '5%',sort: true,templet: function(row) {
                    if(row.结果 == '恶化'){
                     return '<span style="color: red;">恶化</span>';
                    } else if(row.结果 == '改善'){
                     return '<span style="color: green;">改善</span>';
                    }
              }}
              ,{field: '修订结果', title: '修订结果',width: '5%'}
            ]]
            ,text:{none:'当前任务暂无异常指标！'},
          });
        });
    </script>
</body>
</html>