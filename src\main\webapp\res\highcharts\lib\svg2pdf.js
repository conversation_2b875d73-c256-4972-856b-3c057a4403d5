/**
 * Modules in this bundle
 * @license
 *
 * svg2pdf.js:
 *   license: MIT (http://opensource.org/licenses/MIT)
 *   author: yFiles for HTML Support Team <<EMAIL>>
 *   homepage: https://github.com/yWorks/svg2pdf.js#readme
 *   version: 1.3.1
 *
 * cssesc:
 *   license: MIT (http://opensource.org/licenses/MIT)
 *   author: <PERSON>
 *   homepage: https://mths.be/cssesc
 *   version: 2.0.0
 *
 * font-family:
 *   license: MIT (http://opensource.org/licenses/MIT)
 *   author: <PERSON><PERSON> <<EMAIL>>
 *   homepage: https://github.com/hanamura/font-family
 *   version: 0.2.0
 *
 * svgpath:
 *   license: MIT (http://opensource.org/licenses/MIT)
 *   homepage: https://github.com/fontello/svgpath#readme
 *   version: 2.2.1
 *
 * This header is generated by licensify (https://github.com/twada/licensify)
 */
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).svg2pdf=t()}}(function(){return function n(s,o,h){function c(e,t){if(!o[e]){if(!s[e]){var r="function"==typeof require&&require;if(!t&&r)return r(e,!0);if(u)return u(e,!0);var a=new Error("Cannot find module '"+e+"'");throw a.code="MODULE_NOT_FOUND",a}var i=o[e]={exports:{}};s[e][0].call(i.exports,function(t){return c(s[e][1][t]||t)},i,i.exports,n,s,o,h)}return o[e].exports}for(var u="function"==typeof require&&require,t=0;t<h.length;t++)c(h[t]);return c}({1:[function(t,e,r){"use strict";e.exports=t("./lib/svgpath")},{"./lib/svgpath":6}],2:[function(t,e,r){"use strict";var C=2*Math.PI;function I(t,e,r,a){var i=t*a-e*r<0?-1:1,n=(t*r+e*a)/(Math.sqrt(t*t+e*e)*Math.sqrt(t*t+e*e));return 1<n&&(n=1),n<-1&&(n=-1),i*Math.acos(n)}e.exports=function(t,e,r,a,i,n,s,o,h){var c=Math.sin(h*C/360),u=Math.cos(h*C/360),l=u*(t-r)/2+c*(e-a)/2,f=-c*(t-r)/2+u*(e-a)/2;if(0===l&&0===f)return[];if(0===s||0===o)return[];s=Math.abs(s),o=Math.abs(o);var d=l*l/(s*s)+f*f/(o*o);1<d&&(s*=Math.sqrt(d),o*=Math.sqrt(d));var p,g,x,b,m,v,y,k=function(t,e,r,a,i,n,s,o,h,c){var u=c*(t-r)/2+h*(e-a)/2,l=-h*(t-r)/2+c*(e-a)/2,f=s*s,d=o*o,p=u*u,g=l*l,x=f*d-f*g-d*p;x<0&&(x=0),x/=f*g+d*p;var b=(x=Math.sqrt(x)*(i===n?-1:1))*s/o*l,m=x*-o/s*u,v=c*b-h*m+(t+r)/2,y=h*b+c*m+(e+a)/2,k=(u-b)/s,w=(l-m)/o,M=(-u-b)/s,A=(-l-m)/o,S=I(1,0,k,w),F=I(k,w,M,A);return 0===n&&0<F&&(F-=C),1===n&&F<0&&(F+=C),[v,y,S,F]}(t,e,r,a,i,n,s,o,c,u),w=[],M=k[2],A=k[3],S=Math.max(Math.ceil(Math.abs(A)/(C/4)),1);A/=S;for(var F=0;F<S;F++)w.push((p=M,g=A,void 0,x=4/3*Math.tan(g/4),b=Math.cos(p),m=Math.sin(p),v=Math.cos(p+g),y=Math.sin(p+g),[b,m,b-m*x,m+b*x,v+y*x,y-v*x,v,y])),M+=A;return w.map(function(t){for(var e=0;e<t.length;e+=2){var r=t[e+0],a=t[e+1],i=u*(r*=s)-c*(a*=o),n=c*r+u*a;t[e+0]=i+k[0],t[e+1]=n+k[1]}return t})}},{}],3:[function(t,e,r){"use strict";var l=1e-10,f=Math.PI/180;function a(t,e,r){if(!(this instanceof a))return new a(t,e,r);this.rx=t,this.ry=e,this.ax=r}a.prototype.transform=function(t){var e=Math.cos(this.ax*f),r=Math.sin(this.ax*f),a=[this.rx*(t[0]*e+t[2]*r),this.rx*(t[1]*e+t[3]*r),this.ry*(-t[0]*r+t[2]*e),this.ry*(-t[1]*r+t[3]*e)],i=a[0]*a[0]+a[2]*a[2],n=a[1]*a[1]+a[3]*a[3],s=((a[0]-a[3])*(a[0]-a[3])+(a[2]+a[1])*(a[2]+a[1]))*((a[0]+a[3])*(a[0]+a[3])+(a[2]-a[1])*(a[2]-a[1])),o=(i+n)/2;if(s<l*o)return this.rx=this.ry=Math.sqrt(o),this.ax=0,this;var h=a[0]*a[1]+a[2]*a[3],c=o+(s=Math.sqrt(s))/2,u=o-s/2;return this.ax=Math.abs(h)<l&&Math.abs(c-n)<l?90:180*Math.atan(Math.abs(h)>Math.abs(c-n)?(c-i)/h:h/(c-n))/Math.PI,0<=this.ax?(this.rx=Math.sqrt(c),this.ry=Math.sqrt(u)):(this.ax+=90,this.rx=Math.sqrt(u),this.ry=Math.sqrt(c)),this},a.prototype.isDegenerate=function(){return this.rx<l*this.ry||this.ry<l*this.rx},e.exports=a},{}],4:[function(t,e,r){"use strict";function a(){if(!(this instanceof a))return new a;this.queue=[],this.cache=null}a.prototype.matrix=function(t){return 1===t[0]&&0===t[1]&&0===t[2]&&1===t[3]&&0===t[4]&&0===t[5]||(this.cache=null,this.queue.push(t)),this},a.prototype.translate=function(t,e){return 0===t&&0===e||(this.cache=null,this.queue.push([1,0,0,1,t,e])),this},a.prototype.scale=function(t,e){return 1===t&&1===e||(this.cache=null,this.queue.push([t,0,0,e,0,0])),this},a.prototype.rotate=function(t,e,r){var a,i,n;return 0!==t&&(this.translate(e,r),a=t*Math.PI/180,i=Math.cos(a),n=Math.sin(a),this.queue.push([i,n,-n,i,0,0]),this.cache=null,this.translate(-e,-r)),this},a.prototype.skewX=function(t){return 0!==t&&(this.cache=null,this.queue.push([1,0,Math.tan(t*Math.PI/180),1,0,0])),this},a.prototype.skewY=function(t){return 0!==t&&(this.cache=null,this.queue.push([1,Math.tan(t*Math.PI/180),0,1,0,0])),this},a.prototype.toArray=function(){if(this.cache)return this.cache;if(!this.queue.length)return this.cache=[1,0,0,1,0,0],this.cache;if(this.cache=this.queue[0],1===this.queue.length)return this.cache;for(var t=1;t<this.queue.length;t++)this.cache=(e=this.cache,r=this.queue[t],[e[0]*r[0]+e[2]*r[1],e[1]*r[0]+e[3]*r[1],e[0]*r[2]+e[2]*r[3],e[1]*r[2]+e[3]*r[3],e[0]*r[4]+e[2]*r[5]+e[4],e[1]*r[4]+e[3]*r[5]+e[5]]);var e,r;return this.cache},a.prototype.calc=function(t,e,r){var a;return this.queue.length?(this.cache||(this.cache=this.toArray()),[t*(a=this.cache)[0]+e*a[2]+(r?0:a[4]),t*a[1]+e*a[3]+(r?0:a[5])]):[t,e]},e.exports=a},{}],5:[function(t,e,r){"use strict";var s={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0},a=[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279];function c(t){return 48<=t&&t<=57}function i(t){this.index=0,this.path=t,this.max=t.length,this.result=[],this.param=0,this.err="",this.segmentStart=0,this.data=[]}function o(t){for(;t.index<t.max&&(10===(e=t.path.charCodeAt(t.index))||13===e||8232===e||8233===e||32===e||9===e||11===e||12===e||160===e||5760<=e&&0<=a.indexOf(e));)t.index++;var e}function h(t){var e,r=t.index,a=r,i=t.max,n=!1,s=!1,o=!1,h=!1;if(i<=a)t.err="SvgPath: missed param (at pos "+a+")";else if(43!==(e=t.path.charCodeAt(a))&&45!==e||(e=++a<i?t.path.charCodeAt(a):0),c(e)||46===e){if(46!==e){if(n=48===e,e=++a<i?t.path.charCodeAt(a):0,n&&a<i&&e&&c(e))return void(t.err="SvgPath: numbers started with `0` such as `09` are ilegal (at pos "+r+")");for(;a<i&&c(t.path.charCodeAt(a));)a++,s=!0;e=a<i?t.path.charCodeAt(a):0}if(46===e){for(h=!0,a++;c(t.path.charCodeAt(a));)a++,o=!0;e=a<i?t.path.charCodeAt(a):0}if(101===e||69===e){if(h&&!s&&!o)return void(t.err="SvgPath: invalid float exponent (at pos "+a+")");if(43!==(e=++a<i?t.path.charCodeAt(a):0)&&45!==e||a++,!(a<i&&c(t.path.charCodeAt(a))))return void(t.err="SvgPath: invalid float exponent (at pos "+a+")");for(;a<i&&c(t.path.charCodeAt(a));)a++}t.index=a,t.param=parseFloat(t.path.slice(r,a))+0}else t.err="SvgPath: param should start with 0..9 or `.` (at pos "+a+")"}function u(t){var e,r;r=(e=t.path[t.segmentStart]).toLowerCase();var a=t.data;if("m"===r&&2<a.length&&(t.result.push([e,a[0],a[1]]),a=a.slice(2),r="l",e="m"===e?"l":"L"),"r"===r)t.result.push([e].concat(a));else for(;a.length>=s[r]&&(t.result.push([e].concat(a.splice(0,s[r]))),s[r]););}function n(t){var e,r,a,i=t.max;if(t.segmentStart=t.index,function(t){switch(32|t){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:case 114:return!0}return!1}(t.path.charCodeAt(t.index)))if(r=s[t.path[t.index].toLowerCase()],t.index++,o(t),t.data=[],r){for(e=!1;;){for(a=r;0<a;a--){if(h(t),t.err.length)return;t.data.push(t.param),o(t),e=!1,t.index<i&&44===t.path.charCodeAt(t.index)&&(t.index++,o(t),e=!0)}if(!e){if(t.index>=t.max)break;if(!(48<=(n=t.path.charCodeAt(t.index))&&n<=57||43===n||45===n||46===n))break}}var n;u(t)}else u(t);else t.err="SvgPath: bad command "+t.path[t.index]+" (at pos "+t.index+")"}e.exports=function(t){var e=new i(t),r=e.max;for(o(e);e.index<r&&!e.err.length;)n(e);return e.err.length?e.result=[]:e.result.length&&("mM".indexOf(e.result[0][0])<0?(e.err="SvgPath: string should start with `M` or `m`",e.result=[]):e.result[0][0]="M"),{err:e.err,segments:e.result}}},{}],6:[function(t,e,r){"use strict";var a=t("./path_parse"),i=t("./transform_parse"),n=t("./matrix"),c=t("./a2c"),d=t("./ellipse");function s(t){if(!(this instanceof s))return new s(t);var e=a(t);this.segments=e.segments,this.err=e.err,this.__stack=[]}s.prototype.__matrix=function(u){var l,f=this;u.queue.length&&this.iterate(function(t,e,r,a){var i,n,s,o;switch(t[0]){case"v":n=0===(i=u.calc(0,t[1],!0))[0]?["v",i[1]]:["l",i[0],i[1]];break;case"V":n=(i=u.calc(r,t[1],!1))[0]===u.calc(r,a,!1)[0]?["V",i[1]]:["L",i[0],i[1]];break;case"h":n=0===(i=u.calc(t[1],0,!0))[1]?["h",i[0]]:["l",i[0],i[1]];break;case"H":n=(i=u.calc(t[1],a,!1))[1]===u.calc(r,a,!1)[1]?["H",i[0]]:["L",i[0],i[1]];break;case"a":case"A":var h=u.toArray(),c=d(t[1],t[2],t[3]).transform(h);if(h[0]*h[3]-h[1]*h[2]<0&&(t[5]=t[5]?"0":"1"),i=u.calc(t[6],t[7],"a"===t[0]),"A"===t[0]&&t[6]===r&&t[7]===a||"a"===t[0]&&0===t[6]&&0===t[7]){n=["a"===t[0]?"l":"L",i[0],i[1]];break}n=c.isDegenerate()?["a"===t[0]?"l":"L",i[0],i[1]]:[t[0],c.rx,c.ry,c.ax,t[4],t[5],i[0],i[1]];break;case"m":o=0<e,n=["m",(i=u.calc(t[1],t[2],o))[0],i[1]];break;default:for(n=[s=t[0]],o=s.toLowerCase()===s,l=1;l<t.length;l+=2)i=u.calc(t[l],t[l+1],o),n.push(i[0],i[1])}f.segments[e]=n},!0)},s.prototype.__evaluateStack=function(){var t,e;if(this.__stack.length){if(1===this.__stack.length)return this.__matrix(this.__stack[0]),void(this.__stack=[]);for(t=n(),e=this.__stack.length;0<=--e;)t.matrix(this.__stack[e].toArray());this.__matrix(t),this.__stack=[]}},s.prototype.toString=function(){var t,e,r=[];this.__evaluateStack();for(var a=0;a<this.segments.length;a++)e=this.segments[a][0],t=0<a&&"m"!==e&&"M"!==e&&e===this.segments[a-1][0],r=r.concat(t?this.segments[a].slice(1):this.segments[a]);return r.join(" ").replace(/ ?([achlmqrstvz]) ?/gi,"$1").replace(/ \-/g,"-").replace(/zm/g,"z m")},s.prototype.translate=function(t,e){return this.__stack.push(n().translate(t,e||0)),this},s.prototype.scale=function(t,e){return this.__stack.push(n().scale(t,e||0===e?e:t)),this},s.prototype.rotate=function(t,e,r){return this.__stack.push(n().rotate(t,e||0,r||0)),this},s.prototype.skewX=function(t){return this.__stack.push(n().skewX(t)),this},s.prototype.skewY=function(t){return this.__stack.push(n().skewY(t)),this},s.prototype.matrix=function(t){return this.__stack.push(n().matrix(t)),this},s.prototype.transform=function(t){return t.trim()&&this.__stack.push(i(t)),this},s.prototype.round=function(a){var e,i=0,n=0,s=0,o=0;return a=a||0,this.__evaluateStack(),this.segments.forEach(function(r){var t=r[0].toLowerCase()===r[0];switch(r[0]){case"H":case"h":return t&&(r[1]+=s),s=r[1]-r[1].toFixed(a),void(r[1]=+r[1].toFixed(a));case"V":case"v":return t&&(r[1]+=o),o=r[1]-r[1].toFixed(a),void(r[1]=+r[1].toFixed(a));case"Z":case"z":return s=i,void(o=n);case"M":case"m":return t&&(r[1]+=s,r[2]+=o),s=r[1]-r[1].toFixed(a),o=r[2]-r[2].toFixed(a),i=s,n=o,r[1]=+r[1].toFixed(a),void(r[2]=+r[2].toFixed(a));case"A":case"a":return t&&(r[6]+=s,r[7]+=o),s=r[6]-r[6].toFixed(a),o=r[7]-r[7].toFixed(a),r[1]=+r[1].toFixed(a),r[2]=+r[2].toFixed(a),r[3]=+r[3].toFixed(a+2),r[6]=+r[6].toFixed(a),void(r[7]=+r[7].toFixed(a));default:return e=r.length,t&&(r[e-2]+=s,r[e-1]+=o),s=r[e-2]-r[e-2].toFixed(a),o=r[e-1]-r[e-1].toFixed(a),void r.forEach(function(t,e){e&&(r[e]=+r[e].toFixed(a))})}}),this},s.prototype.iterate=function(i,t){var e,r,a,n=this.segments,s={},o=!1,h=0,c=0,u=0,l=0;if(t||this.__evaluateStack(),n.forEach(function(t,e){var r=i(t,e,h,c);Array.isArray(r)&&(s[e]=r,o=!0);var a=t[0]===t[0].toLowerCase();switch(t[0]){case"m":case"M":return h=t[1]+(a?h:0),c=t[2]+(a?c:0),u=h,void(l=c);case"h":case"H":return void(h=t[1]+(a?h:0));case"v":case"V":return void(c=t[1]+(a?c:0));case"z":case"Z":return h=u,void(c=l);default:h=t[t.length-2]+(a?h:0),c=t[t.length-1]+(a?c:0)}}),!o)return this;for(a=[],e=0;e<n.length;e++)if(void 0!==s[e])for(r=0;r<s[e].length;r++)a.push(s[e][r]);else a.push(n[e]);return this.segments=a,this},s.prototype.abs=function(){return this.iterate(function(t,e,r,a){var i,n=t[0],s=n.toUpperCase();if(n!==s)switch(t[0]=s,n){case"v":return void(t[1]+=a);case"a":return t[6]+=r,void(t[7]+=a);default:for(i=1;i<t.length;i++)t[i]+=i%2?r:a}},!0),this},s.prototype.rel=function(){return this.iterate(function(t,e,r,a){var i,n=t[0],s=n.toLowerCase();if(n!==s&&(0!==e||"M"!==n))switch(t[0]=s,n){case"V":return void(t[1]-=a);case"A":return t[6]-=r,void(t[7]-=a);default:for(i=1;i<t.length;i++)t[i]-=i%2?r:a}},!0),this},s.prototype.unarc=function(){return this.iterate(function(t,e,r,a){var i,n,s,o=[],h=t[0];return"A"!==h&&"a"!==h?null:("a"===h?(n=r+t[6],s=a+t[7]):(n=t[6],s=t[7]),0===(i=c(r,a,n,s,t[4],t[5],t[1],t[2],t[3])).length?[["a"===t[0]?"l":"L",t[6],t[7]]]:(i.forEach(function(t){o.push(["C",t[2],t[3],t[4],t[5],t[6],t[7]])}),o))}),this},s.prototype.unshort=function(){var o,h,c,u,l,f=this.segments;return this.iterate(function(t,e,r,a){var i,n=t[0],s=n.toUpperCase();e&&("T"===s?(i="t"===n,"Q"===(c=f[e-1])[0]?(o=c[1]-r,h=c[2]-a):"q"===c[0]?(o=c[1]-c[3],h=c[2]-c[4]):h=o=0,u=-o,l=-h,i||(u+=r,l+=a),f[e]=[i?"q":"Q",u,l,t[1],t[2]]):"S"===s&&(i="s"===n,"C"===(c=f[e-1])[0]?(o=c[3]-r,h=c[4]-a):"c"===c[0]?(o=c[3]-c[5],h=c[4]-c[6]):h=o=0,u=-o,l=-h,i||(u+=r,l+=a),f[e]=[i?"c":"C",u,l,t[1],t[2],t[3],t[4]]))}),this},e.exports=s},{"./a2c":2,"./ellipse":3,"./matrix":4,"./path_parse":5,"./transform_parse":7}],7:[function(t,e,r){"use strict";var i=t("./matrix"),n={matrix:!0,scale:!0,rotate:!0,translate:!0,skewX:!0,skewY:!0},s=/\s*(matrix|translate|scale|rotate|skewX|skewY)\s*\(\s*(.+?)\s*\)[\s,]*/,o=/[\s,]+/;e.exports=function(t){var e,r,a=new i;return t.split(s).forEach(function(t){if(t.length)if(void 0===n[t])switch(r=t.split(o).map(function(t){return+t||0}),e){case"matrix":return void(6===r.length&&a.matrix(r));case"scale":return void(1===r.length?a.scale(r[0],r[0]):2===r.length&&a.scale(r[0],r[1]));case"rotate":return void(1===r.length?a.rotate(r[0],0,0):3===r.length&&a.rotate(r[0],r[1],r[2]));case"translate":return void(1===r.length?a.translate(r[0],0):2===r.length&&a.translate(r[0],r[1]));case"skewX":return void(1===r.length&&a.skewX(r[0]));case"skewY":return void(1===r.length&&a.skewY(r[0]))}else e=t}),a}},{"./matrix":4}],8:[function(t,e,r){"use strict";var d={}.hasOwnProperty,p=/[ -,\.\/;-@\[-\^`\{-~]/,g=/[ -,\.\/;-@\[\]\^`\{-~]/,x=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,a=function t(e,r){"single"!=(r=function(t,e){if(!t)return e;var r={};for(var a in e)r[a]=d.call(t,a)?t[a]:e[a];return r}(r,t.options)).quotes&&"double"!=r.quotes&&(r.quotes="single");for(var a="double"==r.quotes?'"':"'",i=r.isIdentifier,n=e.charAt(0),s="",o=0,h=e.length;o<h;){var c=e.charAt(o++),u=c.charCodeAt(),l=void 0;if(u<32||126<u){if(55296<=u&&u<=56319&&o<h){var f=e.charCodeAt(o++);56320==(64512&f)?u=((1023&u)<<10)+(1023&f)+65536:o--}l="\\"+u.toString(16).toUpperCase()+" "}else l=r.escapeEverything?p.test(c)?"\\"+c:"\\"+u.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B:]/.test(c)?i||":"!=c?"\\"+u.toString(16).toUpperCase()+" ":c:"\\"==c||!i&&('"'==c&&a==c||"'"==c&&a==c)||i&&g.test(c)?"\\"+c:c;s+=l}return i&&(/^_/.test(s)?s="\\_"+s.slice(1):/^-[-\d]/.test(s)?s="\\-"+s.slice(1):/\d/.test(n)&&(s="\\3"+n+" "+s.slice(1))),s=s.replace(x,function(t,e,r){return e&&e.length%2?t:(e||"")+r}),!i&&r.wrap?a+s+a:s};a.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},a.version="1.0.1",e.exports=a},{}],9:[function(t,e,r){var h=/[a-z0-9_-]/i,c=/[\s\t]/,o=/[^a-z0-9_-]/i;e.exports={parse:function(t){for(var e,r,a=!0,i=0,n="",s=0,o=[];;){if(r=t[s],0===i){if(!r&&a)break;if(!r&&!a)throw new Error("Parse error");if('"'===r||"'"===r)e=r,a=!(i=1);else if(c.test(r));else{if(!h.test(r))throw new Error("Parse error");a=!(i=3),s--}}else if(1===i){if(!r)throw new Error("Parse Error");"\\"===r?i=2:r===e?(o.push(n),n="",i=4):n+=r}else if(2===i){if(r!==e&&"\\"!==r)throw new Error("Parse error");n+=r,i=1}else if(3===i){if(!r){o.push(n);break}if(h.test(r))n+=r;else if(","===r)o.push(n),n="",i=0;else{if(!c.test(r))throw new Error("Parse error");o.push(n),n="",i=4}}else if(4===i){if(!r)break;if(","===r)i=0;else if(!c.test(r))throw new Error("Parse error")}s++}return o},stringify:function(t,e){var r=e&&e.quote||'"';if('"'!==r&&"'"!==r)throw new Error("Quote must be `'` or `\"`");for(var a=new RegExp(r,"g"),i=[],n=0;n<t.length;++n){var s=t[n];o.test(s)&&(s=r+(s=s.replace(/\\/g,"\\\\").replace(a,"\\"+r))+r),i.push(s)}return i.join(", ")}}},{}],10:[function(t,e,r){
/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * @link   http://www.phpied.com/rgb-color-parser-in-javascript/
 * @license Use it if you like it
 */
!function(t){function f(t){this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var u={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};for(var e in u)t==e&&(t=u[e]);for(var l=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],r=0;r<l.length;r++){var a=l[r].re,i=l[r].process,n=a.exec(t);if(n){var s=i(n);this.r=s[0],this.g=s[1],this.b=s[2],this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:255<this.r?255:this.r,this.g=this.g<0||isNaN(this.g)?0:255<this.g?255:this.g,this.b=this.b<0||isNaN(this.b)?0:255<this.b?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,e=0;e<l.length;e++)for(var r=l[e].example,a=0;a<r.length;a++)t[t.length]=r[a];for(var i in u)t[t.length]=i;var n=document.createElement("ul");n.setAttribute("id","rgbcolor-examples");for(e=0;e<t.length;e++)try{var s=document.createElement("li"),o=new f(t[e]),h=document.createElement("div");h.style.cssText="margin: 3px; border: 1px solid black; background:"+o.toHex()+"; color:"+o.toHex(),h.appendChild(document.createTextNode("test"));var c=document.createTextNode(" "+t[e]+" -> "+o.toRGB()+" -> "+o.toHex());s.appendChild(h),s.appendChild(c),n.appendChild(s)}catch(t){}return n}}void 0!==e&&e.exports?e.exports=f:t.RGBColor=f}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||this)},{}],11:[function(r,p,t){!function(t){var rt,u,c,i,at,it=/url\(["']?#([^"']+)["']?\)/,nt=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*\s*)$/i,h="http://www.w3.org/2000/svg",st=function(t){var e=t.getAttribute("d");u&&(e=u(e).unshort().unarc().abs().toString(),t.setAttribute("d",e));var r=t.pathSegList;if(r)return r;r=[];for(var a,i=/([a-df-zA-DF-Z])([^a-df-zA-DF-Z]*)/g;a=i.exec(e);){var n=kt(a[2]),s=a[1],o=0<="zZ".indexOf(s)?0:0<="hHvV".indexOf(s)?1:0<="mMlLtT".indexOf(s)?2:0<="sSqQ".indexOf(s)?4:0<="aA".indexOf(s)?7:0<="cC".indexOf(s)?6:-1,h=0;do{var c={pathSegTypeAsLetter:s};switch(s){case"h":case"H":c.x=n[h];break;case"v":case"V":c.y=n[h];break;case"c":case"C":c.x1=n[h+o-6],c.y1=n[h+o-5];case"s":case"S":c.x2=n[h+o-4],c.y2=n[h+o-3];case"t":case"T":case"l":case"L":case"m":case"M":c.x=n[h+o-2],c.y=n[h+o-1];break;case"q":case"Q":c.x1=n[h],c.y1=n[h+1],c.x=n[h+2],c.y=n[h+3];break;case"a":case"A":throw new Error("Cannot convert Arcs without SvgPath package")}r.push(c),"m"===s?s="l":"M"===s&&(s="L"),h+=o}while(h<n.length)}return r.getItem=function(t){return this[t]},r.numberOfItems=r.length,r},ot=function(t,e,r){return r=r||e,t.getAttribute(e)||t.style&&t.style[r]},ht=function(t,e){return 0<=e.split(",").indexOf(t.tagName.toLowerCase())},O=function(t,e){for(var r=[],a=0;a<t.childNodes.length;a++){var i=t.childNodes[a];"#"!==i.nodeName.charAt(0)&&r.push(i)}for(a=0;a<r.length;a++)e(a,r[a])},ct=function(t,e){return Math.atan2(e[1]-t[1],e[0]-t[0])};function ut(t){var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]);return[t[0]/e,t[1]/e]}function lt(t,e){return ut([e[0]-t[0],e[1]-t[1]])}function ft(t,e){return[t[0]+e[0],t[1]+e[1]]}var s=function(t,e){var r=e[0]-t[0],a=e[1]-t[1];return[t[0]+2*r,t[1]+2*a]},dt=function(t,e){return[2/3*(e[0]-t[0])+t[0],2/3*(e[1]-t[1])+t[1]]},pt=function(t,e,r,a,i){var n=r.getItem(t-1);return 0<t&&("C"===n.pathSegTypeAsLetter||"S"===n.pathSegTypeAsLetter)?s([n.x2,n.y2],e):0<t&&("c"===n.pathSegTypeAsLetter||"s"===n.pathSegTypeAsLetter)?s([n.x2+a,n.y2+i],e):[e[0],e[1]]};function o(t){this.renderedElements={},this.rootSvg=t}o.prototype.getRendered=function(t){if(this.renderedElements.hasOwnProperty(t))return this.renderedElements[t];var e=this.rootSvg.querySelector("#"+i(t,{isIdentifier:!0}));if(ht(e,"lineargradient"))n(e,"axial",[e.getAttribute("x1")||0,e.getAttribute("y1")||0,e.getAttribute("x2")||1,e.getAttribute("y2")||0]);else if(ht(e,"radialgradient"))n(e,"radial",[e.getAttribute("fx")||e.getAttribute("cx")||.5,e.getAttribute("fy")||e.getAttribute("cy")||.5,0,e.getAttribute("cx")||.5,e.getAttribute("cy")||.5,e.getAttribute("r")||.5]);else if(ht(e,"pattern"))f(e,this,gt.default());else if(ht(e,"marker")){var r=mt(e),a=Mt(e);at.beginFormObject(a[0],a[1],a[2],a[3],r),St(e,at.unitMatrix,this,!1,!1,gt.default()),at.endFormObject(e.getAttribute("id"))}else ht(e,"clippath")||It(e,at.unitMatrix,this,!0,!1,gt.default());return this.renderedElements[t]=e};var gt=function(){this.xmlSpace=null,this.color=null,this.fill=null,this.fillOpacity=1,this.fontFamily=null,this.fontSize=16,this.fontStyle=null,this.fontWeight=null,this.opacity=1,this.stroke=null,this.strokeDasharray=null,this.strokeDashoffset=null,this.strokeLinecap=null,this.strokeLinejoin=null,this.strokeMiterlimit=4,this.strokeOpacity=1,this.strokeWidth=1,this.textAnchor=null,this.visibility=null};function xt(){this.markers=[]}function bt(t,e,r){this.id=t,this.anchor=e,this.angle=r}function I(t){return t.replace(/[\n\r]/g,"")}function L(t){return t.replace(/[\t]/g," ")}function T(t){return t.replace(/ +/g," ")}function l(t,e,r,a,i,n){var s,o,h=e[0],c=e[1],u=e[2],l=e[3],f=i/u,d=n/l,p=t.getAttribute("preserveAspectRatio");if(p){var g=p.split(" ");"defer"===g[0]&&(g=g.slice(1)),s=g[0],o=g[1]||"meet"}else s="xMidYMid",o="meet";"none"!==s&&("meet"===o?f=d=Math.min(f,d):"slice"===o&&(f=d=Math.max(f,d)));var x=r-h*f,b=a-c*d;0<=s.indexOf("xMid")?x+=(i-u*f)/2:0<=s.indexOf("xMax")&&(x+=i-u*f),0<=s.indexOf("yMid")?b+=(n-l*d)/2:0<=s.indexOf("yMax")&&(b+=n-l*d);var m=new at.Matrix(1,0,0,1,x,b),v=new at.Matrix(f,0,0,d,0,0);return at.matrixMult(v,m)}gt.default=function(){var t=new gt;return t.xmlSpace="default",t.fill=new rt("rgb(0, 0, 0)"),t.fillOpacity=1,t.fontFamily="times",t.fontSize=16,t.fontStyle="normal",t.fontWeight="normal",t.opacity=1,t.stroke=null,t.strokeDasharray=null,t.strokeDashoffset=null,t.strokeLinecap="butt",t.strokeLinejoin="miter",t.strokeMiterlimit=4,t.strokeOpacity=1,t.strokeWidth=1,t.textAnchor="start",t.visibility="visible",t},gt.prototype.clone=function(){var t=new gt;return t.xmlSpace=this.xmlSpace,t.fill=this.fill,t.fillOpacity=this.fillOpacity,t.fontFamily=this.fontFamily,t.fontSize=this.fontSize,t.fontStyle=this.fontStyle,t.fontWeight=this.fontWeight,t.opacity=this.opacity,t.stroke=this.stroke,t.strokeDasharray=this.strokeDasharray,t.strokeDashoffset=this.strokeDashoffset,t.strokeLinecap=this.strokeLinecap,t.strokeLinejoin=this.strokeLinejoin,t.strokeMiterlimit=this.strokeMiterlimit,t.strokeOpacity=this.strokeOpacity,t.strokeWidth=this.strokeWidth,t.textAnchor=this.textAnchor,t.visibility=this.visibility,t},xt.prototype.addMarker=function(t){this.markers.push(t)},xt.prototype.draw=function(t,e,r){for(var a=0;a<this.markers.length;a++){var i,n=this.markers[a],s=n.angle,o=n.anchor,h=Math.cos(s),c=Math.sin(s);i=new at.Matrix(h,c,-c,h,o[0],o[1]),i=at.matrixMult(new at.Matrix(r.strokeWidth,0,0,r.strokeWidth,0,0),i),i=at.matrixMult(i,t),at.saveGraphicsState(),at.setLineWidth(1),e.getRendered(n.id),at.doFormObject(n.id,i),at.restoreGraphicsState()}};var mt=function(t){var e,r,a,i=at.unitMatrix;if(ht(t,"svg,g"))if(r=parseFloat(t.getAttribute("x"))||0,a=parseFloat(t.getAttribute("y"))||0,e=t.getAttribute("viewBox")){var n=kt(e);i=l(t,n,r,a,parseFloat(t.getAttribute("width"))||n[2],parseFloat(t.getAttribute("height"))||n[3])}else i=new at.Matrix(1,0,0,1,r,a);else if(ht(t,"marker"))if(r=parseFloat(t.getAttribute("refX"))||0,a=parseFloat(t.getAttribute("refY"))||0,e=t.getAttribute("viewBox")){var s=kt(e);s[0]=s[1]=0,i=l(t,s,0,0,parseFloat(t.getAttribute("markerWidth"))||3,parseFloat(t.getAttribute("markerHeight"))||3),i=at.matrixMult(new at.Matrix(1,0,0,1,-r,-a),i)}else i=new at.Matrix(1,0,0,1,-r,-a);var o=t.getAttribute("transform");return o?at.matrixMult(i,yt(o)):i},vt=function(t){for(var e=kt(t),r=[],a=0;a<e.length-1;a+=2){var i=e[a],n=e[a+1];r.push([i,n])}return r},yt=function(t){if(!t)return at.unitMatrix;for(var e,r=/^\s*matrix\(([^\)]+)\)\s*/,a=/^\s*translate\(([^\)]+)\)\s*/,i=/^\s*rotate\(([^\)]+)\)\s*/,n=/^\s*scale\(([^\)]+)\)\s*/,s=/^\s*skewX\(([^\)]+)\)\s*/,o=/^\s*skewY\(([^\)]+)\)\s*/,h=at.unitMatrix;0<t.length;){var c=r.exec(t);if(c&&(e=kt(c[1]),h=at.matrixMult(new at.Matrix(e[0],e[1],e[2],e[3],e[4],e[5]),h),t=t.substr(c[0].length)),c=i.exec(t)){e=kt(c[1]);var u=Math.PI*e[0]/180;if(h=at.matrixMult(new at.Matrix(Math.cos(u),Math.sin(u),-Math.sin(u),Math.cos(u),0,0),h),e[1]&&e[2]){var l=new at.Matrix(1,0,0,1,e[1],e[2]),f=new at.Matrix(1,0,0,1,-e[1],-e[2]);h=at.matrixMult(f,at.matrixMult(h,l))}t=t.substr(c[0].length)}(c=a.exec(t))&&(e=kt(c[1]),h=at.matrixMult(new at.Matrix(1,0,0,1,e[0],e[1]||0),h),t=t.substr(c[0].length)),(c=n.exec(t))&&((e=kt(c[1]))[1]||(e[1]=e[0]),h=at.matrixMult(new at.Matrix(e[0],0,0,e[1],0,0),h),t=t.substr(c[0].length)),(c=s.exec(t))&&(e=parseFloat(c[1]),h=at.matrixMult(new at.Matrix(1,0,Math.tan(e),1,0,0),h),t=t.substr(c[0].length)),(c=o.exec(t))&&(e=parseFloat(c[1]),h=at.matrixMult(new at.Matrix(1,Math.tan(e),0,1,0,0),h),t=t.substr(c[0].length))}return h},kt=function(t){for(var e,r=[],a=/[+-]?(?:(?:\d+\.?\d*)|(?:\d*\.?\d+))(?:[eE][+-]?\d+)?/g;e=a.exec(t);)r.push(parseFloat(e[0]));return r},wt=function(t,e){var r=t[0],a=t[1];return[e.a*r+e.c*a+e.e,e.b*r+e.d*a+e.f]},Mt=function(t){if("none"===ot(t,"display"))return[0,0,0,0];var e,r,a,i,n,s,o,h,c=parseFloat;if(ht(t,"polygon,polyline")){var u=vt(t.getAttribute("points"));for(r=Number.POSITIVE_INFINITY,a=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY,n=Number.NEGATIVE_INFINITY,e=0;e<u.length;e++){var l=u[e];r=Math.min(r,l[0]),i=Math.max(i,l[0]),a=Math.min(a,l[1]),n=Math.max(n,l[1])}h=[r,a,i-r,n-a]}else if(ht(t,"path")){var f=st(t);r=Number.POSITIVE_INFINITY,a=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY,n=Number.NEGATIVE_INFINITY;var d,p,g,x,b,m,v,y=0,k=0;for(e=0;e<f.numberOfItems;e++){var w=f.getItem(e),M=w.pathSegTypeAsLetter;switch(M){case"H":g=w.x,x=k;break;case"h":g=w.x+y,x=k;break;case"V":g=y,x=w.y;break;case"v":g=y,x=w.y+k;break;case"C":b=[w.x1,w.y1],m=[w.x2,w.y2],v=[w.x,w.y];break;case"c":b=[w.x1+y,w.y1+k],m=[w.x2+y,w.y2+k],v=[w.x+y,w.y+k];break;case"S":b=pt(e,[y,k],f,d,p),m=[w.x2,w.y2],v=[w.x,w.y];break;case"s":b=pt(e,[y,k],f,d,p),m=[w.x2+y,w.y2+k],v=[w.x+y,w.y+k];break;case"Q":c=[w.x1,w.y1],b=dt([y,k],c),m=dt([w.x,w.y],c),v=[w.x,w.y];break;case"q":c=[w.x1+y,w.y1+k],b=dt([y,k],c),m=dt([y+w.x,k+w.y],c),v=[w.x+y,w.y+k];break;case"T":b=pt(e,[y,k],f,d,p),b=dt([y,k],c),m=dt([w.x,w.y],c),v=[w.x,w.y];break;case"t":c=pt(e,[y,k],f,d,p),b=dt([y,k],c),m=dt([y+w.x,k+w.y],c),v=[w.x+y,w.y+k]}0<="sScCqQtT".indexOf(M)&&(d=y,p=k),0<="MLCSQT".indexOf(M)?(y=w.x,k=w.y):0<="mlcsqt".indexOf(M)?(y=w.x+y,k=w.y+k):"zZ".indexOf(M)<0&&(y=g,k=x),0<="CSQTcsqt".indexOf(M)?(r=Math.min(r,y,b[0],m[0],v[0]),i=Math.max(i,y,b[0],m[0],v[0]),a=Math.min(a,k,b[1],m[1],v[1]),n=Math.max(n,k,b[1],m[1],v[1])):(r=Math.min(r,y),i=Math.max(i,y),a=Math.min(a,k),n=Math.max(n,k))}h=[r,a,i-r,n-a]}else{if(ht(t,"svg"))return(s=t.getAttribute("viewBox"))&&(o=kt(s)),[c(t.getAttribute("x"))||o&&o[0]||0,c(t.getAttribute("y"))||o&&o[1]||0,c(t.getAttribute("width"))||o&&o[2]||0,c(t.getAttribute("height"))||o&&o[3]||0];if(ht(t,"g,clippath"))h=[0,0,0,0],O(t,function(t,e){var r=Mt(e);h=[Math.min(h[0],r[0]),Math.min(h[1],r[1]),Math.max(h[0]+h[2],r[0]+r[2])-Math.min(h[0],r[0]),Math.max(h[1]+h[3],r[1]+r[3])-Math.min(h[1],r[1])]});else{if(ht(t,"marker"))return(s=t.getAttribute("viewBox"))&&(o=kt(s)),[o&&o[0]||0,o&&o[1]||0,o&&o[2]||c(t.getAttribute("marker-width"))||0,o&&o[3]||c(t.getAttribute("marker-height"))||0];if(ht(t,"pattern"))return[c(t.getAttribute("x"))||0,c(t.getAttribute("y"))||0,c(t.getAttribute("width"))||0,c(t.getAttribute("height"))||0];var A=c(t.getAttribute("x1"))||c(t.getAttribute("x"))||c(t.getAttribute("cx")-c(t.getAttribute("r")))||0,S=c(t.getAttribute("x2"))||A+c(t.getAttribute("width"))||c(t.getAttribute("cx"))+c(t.getAttribute("r"))||0,F=c(t.getAttribute("y1"))||c(t.getAttribute("y"))||c(t.getAttribute("cy"))-c(t.getAttribute("r"))||0,C=c(t.getAttribute("y2"))||F+c(t.getAttribute("height"))||c(t.getAttribute("cy"))+c(t.getAttribute("r"))||0;h=[Math.min(A,S),Math.min(F,C),Math.max(A,S)-Math.min(A,S),Math.max(F,C)-Math.min(F,C)]}}if(!ht(t,"marker,svg,g")){var I=ot(t,"stroke-width")||1;return ot(t,"stroke-miterlimit")&&(I*=.5/Math.sin(Math.PI/12)),[h[0]-I,h[1]-I,h[2]+2*I,h[3]+2*I]}return h},_=function(t,e){switch(ot(t,"text-transform")){case"uppercase":return e.toUpperCase();case"lowercase":return e.toLowerCase();default:return e}},a=function(){function i(t,e,r,a,i){var n=document.createElement("canvas").getContext("2d");return n.font=[a,i,r,e].join(" "),n.measureText(t).width}function n(t,e,r,a,i){var n=document.createElementNS(h,"text");n.setAttribute("font-family",e),n.setAttribute("font-size",r),n.setAttribute("font-style",a),n.setAttribute("font-weight",i),n.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),n.appendChild(document.createTextNode(t));var s=document.createElementNS(h,"svg");s.appendChild(n),s.setAttribute("visibility","hidden"),document.body.appendChild(s);var o=n.getBBox().width;return document.body.removeChild(s),o}var s="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ 0123456789!\"$%&/()=?'\\+*-_.:,;^}][{#~|<>",o={};return function(t){var e=o[t];if(!e){var r=i(s,t,"16px","normal","normal"),a=n(s,t,"16px","normal","normal");e=Math.abs(r-a)<.1?i:n,o[t]=e}return e}}();function N(t,e){if(0===t.length)return 0;var r=e.fontFamily;return a(r)(t,e.fontFamily,e.fontSize+"px",e.fontStyle,e.fontWeight)}function q(t,e,r){this.texts=[],this.textNodes=[],this.textAnchor=t,this.originX=e,this.originY=r}function P(t,e){var r;return(r=t&&t.toString().match(/^([\-0-9.]+)em$/))?parseFloat(r[1])*e:(r=t&&t.toString().match(/^([\-0-9.]+)(px|)$/))?parseFloat(r[1]):0}q.prototype.add=function(t,e){this.texts.push(e),this.textNodes.push(t)},q.prototype.put=function(t,e){var r,a,i,n=[],s=[],o=[],h=this.originX,c=this.originY,u=h,l=h;for(r=0;r<this.textNodes.length;r++){var f=h,d=c;if("#text"===(a=this.textNodes[r]).nodeName)p=e;else{var p=e.clone(),g=ot(a,"fill");Ft(a,g&&new rt(g),p);var x=a.getAttribute("dx");null!==x&&(f+=P(x,p.fontSize));var b=a.getAttribute("dy");null!==b&&(d+=P(b,p.fontSize))}o[r]=p,n[r]=f,s[r]=d,h=f+N(this.texts[r],p),c=d,u=Math.min(u,f),l=Math.max(l,h)}switch(this.textAnchor){case"start":i=0;break;case"middle":i=(l-u)/2;break;case"end":i=l-u}for(r=0;r<this.textNodes.length;r++){if("#text"!==(a=this.textNodes[r]).nodeName)if("hidden"===(ot(a,"visibility")||e.visibility))continue;at.saveGraphicsState(),Ct(o[r],e),at.text(n[r]-i,s[r],this.texts[r],void 0,t),at.restoreGraphicsState()}return[h,c]};var At=function(t,e,r,a,i){at.saveGraphicsState();var n,s,o=0,h=at.getFontSize(),c=P(t.getAttribute("x"),h),u=P(t.getAttribute("y"),h);n=P(t.getAttribute("dx"),h),s=P(t.getAttribute("dy"),h);var l,f,d=i.visibility,p=t.childElementCount;if(0===p){var g=(l=t.textContent,f=i,l=L(l=I(l)),"default"===f.xmlSpace&&(l=T(l=l.trim())),l),x=_(t,g);o=function(t,e){var r=e.textAnchor;if("start"===r)return 0;var a=N(t,e),i=0;switch(r){case"end":i=a;break;case"middle":i=a/2}return i}(x,i),"visible"===d&&at.text(c+n-o,u+s,x,void 0,e)}else{for(var b=new q(i.textAnchor,c+n,u+s),m=0;m<t.childNodes.length;m++){var v=t.childNodes[m];if(v.textContent){var y=i.xmlSpace;if("#text"===v.nodeName);else if(ht(v,"tspan")){var k,w=v,M=w.getAttribute("x");if(null!==M){var A=P(M,h);k=b.put(e,i),b=new q(w.getAttribute("text-anchor")||i.textAnchor,A,k[1])}var S=w.getAttribute("y");if(null!==S){var F=P(S,h);k=b.put(e,i),b=new q(w.getAttribute("text-anchor")||i.textAnchor,k[0],F)}var C=w.getAttribute("xml:space");C&&(y=C)}g=L(g=I(g=v.textContent)),"default"===y&&(0===m&&(g=g.replace(/^\s+/,"")),m===p-1&&(g=g.replace(/\s+$/,"")),g=T(g)),x=_(t,g),b.add(v,x)}}b.put(e,i)}at.restoreGraphicsState()},St=function(t,r,a,i,n,s){O(t,function(t,e){It(e,r,a,i,n,s)})},n=function(t,e,r){var a,i=[],n=0,s=!1;O(t,function(t,e){if("stop"===e.tagName.toLowerCase()){var r=new rt(ot(e,"stop-color"));i.push({offset:parseFloat(e.getAttribute("offset")),color:[r.r,r.g,r.b]});var a=ot(e,"stop-opacity");a&&1!=a&&(n+=parseFloat(a),s=!0)}}),s&&(a=new at.GState({opacity:n/i.length}));var o=new at.ShadingPattern(e,r,i,a),h=t.getAttribute("id");at.addShadingPattern(h,o)},f=function(t,e,r){var a=t.getAttribute("id"),i=Mt(t),n=new at.TilingPattern([i[0],i[1],i[0]+i[2],i[1]+i[3]],i[2],i[3],null,at.unitMatrix);at.beginTilingPattern(n),St(t,at.unitMatrix,e,!1,!1,r),at.endTilingPattern(a,n)},d={"sans-serif":"helvetica",verdana:"helvetica",arial:"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",serif:"times",cursive:"times",fantasy:"times"};function Ft(t,e,r){e&&e.ok&&(r.fill=e);var a=ot(t,"font-weight");a&&(r.fontWeight=a);var i=ot(t,"font-style");i&&(r.fontStyle=i);var n=ot(t,"font-family");if(n){var s=c.parse(n);r.fontFamily=function(t,e){var r="";"bold"===t.fontWeight&&(r="bold"),"italic"===t.fontStyle&&(r+="italic"),""===r&&(r="normal");var a=at.getFontList(),i="";return e.some(function(t){var e=a[t];return e&&0<=e.indexOf(r)?(i=t,!0):(t=t.toLowerCase(),!!d.hasOwnProperty(t)&&(i=t,!0))})||(i="times"),i}(r,s)}var o=ot(t,"font-size");o&&(r.fontSize=parseFloat(o));var h=ot(t,"text-anchor");h&&(r.textAnchor=h)}function Ct(t,e){if(t.fontFamily!==e.fontFamily&&(d.hasOwnProperty(t.fontFamily)?at.setFont(d[t.fontFamily]):at.setFont(t.fontFamily)),t.fill!==e.fill&&t.fill.ok){var r=t.fill;at.setTextColor(r.r,r.g,r.b)}if(t.fontWeight!==e.fontWeight||t.fontStyle!==e.fontStyle){var a="";"bold"===t.fontWeight&&(a="bold"),"italic"===t.fontStyle&&(a+="italic"),""===a&&(a="normal"),at.setFontType(a)}t.fontSize!==e.fontSize&&at.setFontSize(t.fontSize)}var It=function(t,e,r,a,i,n){var s=n;if((n=n.clone(),!ht(t,"defs,clippath,pattern,lineargradient,radialgradient,marker"))&&("none"!==ot(t,"display")&&("hidden"!==(n.visibility=ot(t,"visibility")||n.visibility)||ht(t,"svg,g,marker,a,pattern,defs,text")))){var o,h,c=!1,u=null,l="inherit",f="inherit",d=void 0,p=a&&!ht(t,"lineargradient,radialgradient,pattern,clippath");p?(o=mt(t),h=Mt(t),at.beginFormObject(h[0],h[1],h[2],h[3],o),o=at.unitMatrix,a=!1):(o=at.matrixMult(mt(t),e),i||at.saveGraphicsState());var g=t.hasAttribute("clip-path")&&"none"!==t.getAttribute("clip-path");if(g){var x=it.exec(t.getAttribute("clip-path")),b=r.getRendered(x[1]),m=o;b.hasAttribute("clipPathUnits")&&"objectboundingbox"===b.getAttribute("clipPathUnits").toLowerCase()&&(h=Mt(t),m=at.matrixMult(new at.Matrix(h[2],0,0,h[3],h[0],h[1]),m)),m=at.matrixMult(mt(b),m),at.saveGraphicsState(),at.setCurrentTransformationMatrix(m),St(b,at.unitMatrix,r,!1,!0,n),at.clip().discardPath(),at.setCurrentTransformationMatrix(m.inversed())}if(ht(t,"g,path,rect,text,ellipse,line,circle,polygon,polyline")){var v=ot(t,"fill");if(v){var y=it.exec(v);if(y){var k=y[1],w=r.getRendered(k);if(w&&ht(w,"lineargradient,radialgradient")){var M;w.hasAttribute("gradientUnits")&&"objectboundingbox"!==w.getAttribute("gradientUnits").toLowerCase()?M=at.unitMatrix:(h||(h=Mt(t)),M=new at.Matrix(h[2],0,0,h[3],h[0],h[1]));var A=yt(w.getAttribute("gradientTransform"));d={key:k,matrix:at.matrixMult(A,M)},l=!0}else if(w&&ht(w,"pattern")){var S,F,C,I,O;d={key:k};var L=at.unitMatrix;w.hasAttribute("patternUnits")&&"objectboundingbox"!==w.getAttribute("patternUnits").toLowerCase()||(h||(h=Mt(t)),L=new at.Matrix(1,0,0,1,h[0],h[1]),O=(S=Mt(w))[0]*h[0],F=S[1]*h[1],C=S[2]*h[2],I=S[3]*h[3],d.boundingBox=[O,F,O+C,F+I],d.xStep=C,d.yStep=I);var T=at.unitMatrix;w.hasAttribute("patternContentUnits")&&"objectboundingbox"===w.getAttribute("patternContentUnits").toLowerCase()&&(h||(h=Mt(t)),T=new at.Matrix(h[2],0,0,h[3],0,0),O=(S=d.boundingBox||Mt(w))[0]/h[0],F=S[1]/h[1],C=S[2]/h[2],I=S[3]/h[3],d.boundingBox=[O,F,O+C,F+I],d.xStep=C,d.yStep=I);var _=at.unitMatrix;w.hasAttribute("patternTransform")&&(_=yt(w.getAttribute("patternTransform")));var N=T;N=at.matrixMult(N,L),N=at.matrixMult(N,_),N=at.matrixMult(N,o),d.matrix=N,l=!0}else u=new rt("rgb(0, 0, 0)"),l=c=!0}else u=function(t){if("transparent"===t){var e=new rt("rgb(0,0,0)");return e.a=0,e}var r=/\s*rgba\(((?:[^,\)]*,){3}[^,\)]*)\)\s*/.exec(t);if(r){var a=kt(r[1]),i=new rt("rgb("+a.slice(0,3).join(",")+")");return i.a=a[3],i}return new rt(t)}(v),l=!!u.ok&&(c=!0)}var q=1,P=1,E=ot(t,"fill-opacity");E&&(q*=parseFloat(E)),u&&"number"==typeof u.a&&(q*=u.a);var z=ot(t,"stroke-opacity");z&&(P*=parseFloat(z)),R&&"number"==typeof R.a&&(P*=R.a);var j=ot(t,"opacity");if(j){var G=parseFloat(j);P*=G,q*=G}var W=q<1,Y=P<1;if(W||Y){var D={};W&&(D.opacity=q),Y&&(D["stroke-opacity"]=P),at.setGState(new at.GState(D))}}if(ht(t,"g,path,rect,ellipse,line,circle,polygon,polyline")){c&&(n.fill=u,at.setFillColor(u.r,u.g,u.b));var V=ot(t,"stroke");if(V){var U=ot(t,"stroke-width");void 0!==U&&""!==U?(U=Math.abs(parseFloat(U)),n.strokeWidth=U,at.setLineWidth(U)):U=n.strokeWidth;var R=new rt(V);R.ok&&(n.stroke=R,at.setDrawColor(R.r,R.g,R.b),f=0!==U);var H=ot(t,"stroke-linecap");H&&at.setLineCap(n.strokeLinecap=H);var B=ot(t,"stroke-linejoin");B&&at.setLineJoin(n.strokeLinejoin=B);var X=ot(t,"stroke-dasharray");if(X){X=kt(X);var Q=parseInt(ot(t,"stroke-dashoffset"))||0;n.strokeDasharray=X,n.strokeDashoffset=Q,at.setLineDashPattern(X,Q)}var Z=ot(t,"stroke-miterlimit");void 0!==Z&&""!==Z&&at.setLineMiterLimit(n.strokeMiterlimit=parseFloat(Z))}}"inherit"===l&&(l=null!==n.fill),"inherit"===f&&(f=null!==n.stroke);var $,J,K,tt,et=t.getAttribute("xml:space");switch(et&&(n.xmlSpace=et),Ft(t,u,n),Ct(n,s),t.tagName.toLowerCase()){case"svg":case"g":case"a":St(t,o,r,a,!1,n);break;case"use":!function(t,e,r){var a=t.getAttribute("href")||t.getAttribute("xlink:href");if(a){var i=a.substring(1);r.getRendered(i);var n=at.getFormObject(i),s=t.getAttribute("x")||0,o=t.getAttribute("y")||0,h=t.getAttribute("width")||n.width,c=t.getAttribute("height")||n.height,u=new at.Matrix(h/n.width||0,0,0,c/n.height||0,s,o);u=at.matrixMult(u,e),at.doFormObject(i,u)}}(t,o,r);break;case"line":i||(at.setCurrentTransformationMatrix(o),function(t,e,r){var a=[parseFloat(t.getAttribute("x1")||0),parseFloat(t.getAttribute("y1")||0)],i=[parseFloat(t.getAttribute("x2")||0),parseFloat(t.getAttribute("y2")||0)];null!==r.stroke&&at.line(a[0],a[1],i[0],i[1]);var n=t.getAttribute("marker-start"),s=t.getAttribute("marker-end");if(n||s){var o=new xt,h=ct(a,i);n&&o.addMarker(new bt(it.exec(n)[1],a,h)),s&&o.addMarker(new bt(it.exec(s)[1],i,h)),o.draw(at.unitMatrix,e,r)}}(t,r,n));break;case"rect":i||at.setCurrentTransformationMatrix(o),tt=t,at.roundedRect(parseFloat(tt.getAttribute("x"))||0,parseFloat(tt.getAttribute("y"))||0,parseFloat(tt.getAttribute("width")),parseFloat(tt.getAttribute("height")),parseFloat(tt.getAttribute("rx"))||0,parseFloat(tt.getAttribute("ry"))||0);break;case"ellipse":i||at.setCurrentTransformationMatrix(o),K=t,at.ellipse(parseFloat(K.getAttribute("cx"))||0,parseFloat(K.getAttribute("cy"))||0,parseFloat(K.getAttribute("rx")),parseFloat(K.getAttribute("ry")));break;case"circle":i||at.setCurrentTransformationMatrix(o),$=t,J=parseFloat($.getAttribute("r"))||0,at.ellipse(parseFloat($.getAttribute("cx"))||0,parseFloat($.getAttribute("cy"))||0,J,J);break;case"text":At(t,o,0,0,n);break;case"path":i||at.setCurrentTransformationMatrix(o),function(t,A,e,S,r){var F=st(t),C=t.getAttribute("marker-end"),I=t.getAttribute("marker-start"),O=t.getAttribute("marker-mid");C&&(C=it.exec(C)[1]),I&&(I=it.exec(I)[1]),O&&(O=it.exec(O)[1]);var a=function(){for(var t,e,r,a,i,n,s,o,h,c,u=0,l=0,f=u,d=l,p=[],g=new xt,x=[0,0],b=0;b<F.numberOfItems;b++){var m=F.getItem(b),v=m.pathSegTypeAsLetter;switch(v){case"M":f=u,d=l,i=[m.x,m.y],h="m";break;case"m":f=u,d=l,i=[m.x+u,m.y+l],h="m";break;case"L":i=[m.x,m.y],h="l";break;case"l":i=[m.x+u,m.y+l],h="l";break;case"H":i=[m.x,l],h="l",r=m.x,a=l;break;case"h":i=[m.x+u,l],h="l",r=m.x+u,a=l;break;case"V":i=[u,m.y],h="l",r=u,a=m.y;break;case"v":i=[u,m.y+l],h="l",r=u,a=m.y+l;break;case"C":s=[m.x1,m.y1],o=[m.x2,m.y2],i=[m.x,m.y];break;case"c":s=[m.x1+u,m.y1+l],o=[m.x2+u,m.y2+l],i=[m.x+u,m.y+l];break;case"S":s=pt(b,[u,l],F,t,e),o=[m.x2,m.y2],i=[m.x,m.y];break;case"s":s=pt(b,[u,l],F,t,e),o=[m.x2+u,m.y2+l],i=[m.x+u,m.y+l];break;case"Q":n=[m.x1,m.y1],s=dt([u,l],n),o=dt([m.x,m.y],n),i=[m.x,m.y];break;case"q":n=[m.x1+u,m.y1+l],s=dt([u,l],n),o=dt([u+m.x,l+m.y],n),i=[m.x+u,m.y+l];break;case"T":n=pt(b,[u,l],F,t,e),s=dt([u,l],n),o=dt([m.x,m.y],n),i=[m.x,m.y];break;case"t":n=pt(b,[u,l],F,t,e),s=dt([u,l],n),o=dt([u+m.x,l+m.y],n),i=[m.x+u,m.y+l];break;case"Z":case"z":u=f,l=d,p.push({op:"h"})}var y=I&&(1===b||"mM".indexOf(v)<0&&0<="mM".indexOf(F.getItem(b-1).pathSegTypeAsLetter)),k=C&&(b===F.numberOfItems-1||"mM".indexOf(v)<0&&0<="mM".indexOf(F.getItem(b+1).pathSegTypeAsLetter)),w=O&&0<b&&!(1===b&&0<="mM".indexOf(F.getItem(b-1).pathSegTypeAsLetter));if(0<="sScCqQtT".indexOf(v))y&&g.addMarker(new bt(I,[u,l],ct([u,l],s))),k&&g.addMarker(new bt(C,i,ct(o,i))),w&&(c=lt([u,l],s),c=0<="mM".indexOf(F.getItem(b-1).pathSegTypeAsLetter)?c:ut(ft(x,c)),g.addMarker(new bt(O,[u,l],Math.atan2(c[1],c[0])))),x=lt(o,i),t=u,e=l,S&&(s=wt(s,A),o=wt(o,A),i=wt(i,A)),p.push({op:"c",c:[s[0],s[1],o[0],o[1],i[0],i[1]]});else if(0<="lLhHvVmM".indexOf(v)){if(c=lt([u,l],i),y&&g.addMarker(new bt(I,[u,l],Math.atan2(c[1],c[0]))),k&&g.addMarker(new bt(C,i,Math.atan2(c[1],c[0]))),w){var M=0<="mM".indexOf(v)?x:0<="mM".indexOf(F.getItem(b-1).pathSegTypeAsLetter)?c:ut(ft(x,c));g.addMarker(new bt(O,[u,l],Math.atan2(M[1],M[0])))}x=c,S&&(i=wt(i,A)),p.push({op:h,c:i})}0<="MLCSQT".indexOf(v)?(u=m.x,l=m.y):0<="mlcsqt".indexOf(v)?(u=m.x+u,l=m.y+l):"zZ".indexOf(v)<0&&(u=r,l=a)}return{lines:p,markers:g}}();0<a.lines.length&&at.path(a.lines),(C||I||O)&&a.markers.draw(at.unitMatrix,e,r)}(t,o,r,i,n);break;case"polygon":case"polyline":i||at.setCurrentTransformationMatrix(o),function(t,e,r,a){if(t.hasAttribute("points")&&""!==t.getAttribute("points")){var i,n,s=vt(t.getAttribute("points")),o=[{op:"m",c:s[0]}];for(i=1;i<s.length;i++)o.push({op:"l",c:s[i]});a&&o.push({op:"h"}),at.path(o);var h=t.getAttribute("marker-end"),c=t.getAttribute("marker-start"),u=t.getAttribute("marker-mid");if(c||u||h){var l=o.length,f=new xt;if(c&&(c=it.exec(c)[1],n=ft(lt(o[0].c,o[1].c),lt(o[l-2].c,o[0].c)),f.addMarker(new bt(c,o[0].c,Math.atan2(n[1],n[0])))),u){u=it.exec(u)[1];var d,p=lt(o[0].c,o[1].c);for(i=1;i<o.length-2;i++)n=ft(p,d=lt(o[i].c,o[i+1].c)),f.addMarker(new bt(u,o[i].c,Math.atan2(n[1],n[0]))),p=d;n=ft(p,d=lt(o[l-2].c,o[0].c)),f.addMarker(new bt(u,o[l-2].c,Math.atan2(n[1],n[0])))}h&&(h=it.exec(h)[1],n=ft(lt(o[0].c,o[1].c),lt(o[l-2].c,o[0].c)),f.addMarker(new bt(h,o[0].c,Math.atan2(n[1],n[0])))),f.draw(at.unitMatrix,e,r)}}}(t,r,n,"polygon"===t.tagName.toLowerCase());break;case"image":at.setCurrentTransformationMatrix(o),function(t){var e=parseFloat(t.getAttribute("width")),r=parseFloat(t.getAttribute("height")),a=parseFloat(t.getAttribute("x")||0),i=parseFloat(t.getAttribute("y")||0),n=t.getAttribute("xlink:href")||t.getAttribute("href"),s=n.match(nt);if(s&&"image/svg+xml"===s[2]){var o=s[5];o="base64"===s[4]?atob(o):decodeURIComponent(o);var h=(new DOMParser).parseFromString(o,"image/svg+xml").firstElementChild,c=t.getAttribute("preserveAspectRatio");return(!c||c.indexOf("defer")<0||!h.getAttribute("preserveAspectRatio"))&&h.setAttribute("preserveAspectRatio",c),h.setAttribute("x",String(a)),h.setAttribute("y",String(i)),h.setAttribute("width",String(e)),h.setAttribute("height",String(r)),It(h,at.unitMatrix,{},!1,!1,gt.default())}try{at.addImage(n,"",a,i,e,r)}catch(t){"object"==typeof console&&console.warn&&console.warn('svg2pdfjs: Images with external resource link are not supported! ("'+n+'")')}}(t)}ht(t,"path,rect,ellipse,circle,polygon,polyline")&&!i&&(l&&f?at.fillStroke(d):l?at.fill(d):f?at.stroke():at.discardPath()),p?at.endFormObject(t.getAttribute("id")):i||at.restoreGraphicsState(),g&&at.restoreGraphicsState()}},e=function(a,t,e){at=t;var i=e.scale||1,n=e.xOffset||0,s=e.yOffset||0;return at.advancedAPI(function(){at.saveGraphicsState(),at.setCurrentTransformationMatrix(new at.Matrix(i,0,0,i,n,s));var t=gt.default();at.setLineWidth(t.strokeWidth);var e=t.fill;at.setFillColor(e.r,e.g,e.b),at.setFont(t.fontFamily),at.setFontSize(t.fontSize);var r=new o(a);It(a.cloneNode(!0),at.unitMatrix,r,!1,!1,t),at.restoreGraphicsState()}),at};void 0!==p&&p.exports?(rt=r("./rgbcolor.js"),u=r("SvgPath"),c=r("font-family"),i=r("cssesc"),p.exports=e):(u=t.SvgPath,rt=t.RGBColor,c=t.FontFamily,i=t.cssesc,t.svg2pdf=e,t.svgElementToPdf=e)}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||this)},{"./rgbcolor.js":10,SvgPath:1,cssesc:8,"font-family":9}]},{},[11])(11)});
//# sourceMappingURL=svg2pdf.min.js.map