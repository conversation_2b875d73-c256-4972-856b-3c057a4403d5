/*
 Highstock JS v9.0.1 (2021-02-15)

 Advanced Highstock tools

 (c) 2010-2021 Highsoft AS
 Author: Torstein Honsi

 License: www.highcharts.com/license
*/
(function(c){"object"===typeof module&&module.exports?(c["default"]=c,module.exports=c):"function"===typeof define&&define.amd?define("highcharts/modules/stock-tools",["highcharts","highcharts/modules/stock"],function(r){c(r);c.Highcharts=r;return c}):c("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(c){function r(a,c,p,k){a.hasOwnProperty(c)||(a[c]=k.apply(null,p))}c=c?c._modules:{};r(c,"Extensions/Annotations/Mixins/EventEmitterMixin.js",[c["Core/Globals.js"],c["Core/Utilities.js"]],
function(a,c){var u=c.addEvent,k=c.fireEvent,h=c.objectEach,D=c.pick,q=c.removeEvent;return{addEvents:function(){var d=this,f=function(f){u(f,a.isTouchDevice?"touchstart":"mousedown",function(f){d.onMouseDown(f)},{passive:!1})};f(this.graphic.element);(d.labels||[]).forEach(function(e){e.options.useHTML&&e.graphic.text&&f(e.graphic.text.element)});h(d.options.events,function(f,e){var m=function(m){"click"===e&&d.cancelClick||f.call(d,d.chart.pointer.normalize(m),d.target)};if(-1===(d.nonDOMEvents||
[]).indexOf(e))d.graphic.on(e,m);else u(d,e,m,{passive:!1})});if(d.options.draggable&&(u(d,"drag",d.onDrag),!d.graphic.renderer.styledMode)){var e={cursor:{x:"ew-resize",y:"ns-resize",xy:"move"}[d.options.draggable]};d.graphic.css(e);(d.labels||[]).forEach(function(f){f.options.useHTML&&f.graphic.text&&f.graphic.text.css(e)})}d.isUpdating||k(d,"add")},removeDocEvents:function(){this.removeDrag&&(this.removeDrag=this.removeDrag());this.removeMouseUp&&(this.removeMouseUp=this.removeMouseUp())},onMouseDown:function(d){var f=
this,e=f.chart.pointer;d.preventDefault&&d.preventDefault();if(2!==d.button){d=e.normalize(d);var n=d.chartX;var m=d.chartY;f.cancelClick=!1;f.chart.hasDraggedAnnotation=!0;f.removeDrag=u(a.doc,a.isTouchDevice?"touchmove":"mousemove",function(d){f.hasDragged=!0;d=e.normalize(d);d.prevChartX=n;d.prevChartY=m;k(f,"drag",d);n=d.chartX;m=d.chartY},a.isTouchDevice?{passive:!1}:void 0);f.removeMouseUp=u(a.doc,a.isTouchDevice?"touchend":"mouseup",function(e){f.cancelClick=f.hasDragged;f.hasDragged=!1;f.chart.hasDraggedAnnotation=
!1;k(D(f.target,f),"afterUpdate");f.onMouseUp(e)},a.isTouchDevice?{passive:!1}:void 0)}},onMouseUp:function(d){var f=this.chart;d=this.target||this;var e=f.options.annotations;f=f.annotations.indexOf(d);this.removeDocEvents();e[f]=d.options},onDrag:function(d){if(this.chart.isInsidePlot(d.chartX-this.chart.plotLeft,d.chartY-this.chart.plotTop)){var f=this.mouseMoveToTranslation(d);"x"===this.options.draggable&&(f.y=0);"y"===this.options.draggable&&(f.x=0);this.points.length?this.translate(f.x,f.y):
(this.shapes.forEach(function(e){e.translate(f.x,f.y)}),this.labels.forEach(function(e){e.translate(f.x,f.y)}));this.redraw(!1)}},mouseMoveToRadians:function(d,f,e){var n=d.prevChartY-e,m=d.prevChartX-f;e=d.chartY-e;d=d.chartX-f;this.chart.inverted&&(f=m,m=n,n=f,f=d,d=e,e=f);return Math.atan2(e,d)-Math.atan2(n,m)},mouseMoveToTranslation:function(d){var f=d.chartX-d.prevChartX;d=d.chartY-d.prevChartY;if(this.chart.inverted){var e=d;d=f;f=e}return{x:f,y:d}},mouseMoveToScale:function(d,f,e){f=(d.chartX-
f||1)/(d.prevChartX-f||1);d=(d.chartY-e||1)/(d.prevChartY-e||1);this.chart.inverted&&(e=d,d=f,f=e);return{x:f,y:d}},destroy:function(){this.removeDocEvents();q(this);this.hcEvents=null}}});r(c,"Extensions/Annotations/ControlPoint.js",[c["Core/Utilities.js"],c["Extensions/Annotations/Mixins/EventEmitterMixin.js"]],function(a,c){var u=a.merge,k=a.pick;return function(){function a(a,q,d,f){this.addEvents=c.addEvents;this.graphic=void 0;this.mouseMoveToRadians=c.mouseMoveToRadians;this.mouseMoveToScale=
c.mouseMoveToScale;this.mouseMoveToTranslation=c.mouseMoveToTranslation;this.onDrag=c.onDrag;this.onMouseDown=c.onMouseDown;this.onMouseUp=c.onMouseUp;this.removeDocEvents=c.removeDocEvents;this.nonDOMEvents=["drag"];this.chart=a;this.target=q;this.options=d;this.index=k(d.index,f)}a.prototype.setVisibility=function(a){this.graphic.attr("visibility",a?"visible":"hidden");this.options.visible=a};a.prototype.render=function(){var a=this.chart,q=this.options;this.graphic=a.renderer.symbol(q.symbol,0,
0,q.width,q.height).add(a.controlPointsGroup).css(q.style);this.setVisibility(q.visible);this.addEvents()};a.prototype.redraw=function(a){this.graphic[a?"animate":"attr"](this.options.positioner.call(this,this.target))};a.prototype.destroy=function(){c.destroy.call(this);this.graphic&&(this.graphic=this.graphic.destroy());this.options=this.target=this.chart=null};a.prototype.update=function(a){var q=this.chart,d=this.target,f=this.index;a=u(!0,this.options,a);this.destroy();this.constructor(q,d,a,
f);this.render(q.controlPointsGroup);this.redraw()};return a}()});r(c,"Extensions/Annotations/MockPoint.js",[c["Core/Series/Series.js"],c["Core/Utilities.js"],c["Core/Axis/Axis.js"]],function(a,c,p){var k=c.defined,h=c.fireEvent;return function(){function c(q,d,f){this.y=this.x=this.plotY=this.plotX=this.isInside=void 0;this.mock=!0;this.series={visible:!0,chart:q,getPlotBox:a.prototype.getPlotBox};this.target=d||null;this.options=f;this.applyOptions(this.getOptions())}c.fromPoint=function(a){return new c(a.series.chart,
null,{x:a.x,y:a.y,xAxis:a.series.xAxis,yAxis:a.series.yAxis})};c.pointToPixels=function(a,d){var f=a.series,e=f.chart,n=a.plotX,m=a.plotY;e.inverted&&(a.mock?(n=a.plotY,m=a.plotX):(n=e.plotWidth-a.plotY,m=e.plotHeight-a.plotX));f&&!d&&(a=f.getPlotBox(),n+=a.translateX,m+=a.translateY);return{x:n,y:m}};c.pointToOptions=function(a){return{x:a.x,y:a.y,xAxis:a.series.xAxis,yAxis:a.series.yAxis}};c.prototype.hasDynamicOptions=function(){return"function"===typeof this.options};c.prototype.getOptions=function(){return this.hasDynamicOptions()?
this.options(this.target):this.options};c.prototype.applyOptions=function(a){this.command=a.command;this.setAxis(a,"x");this.setAxis(a,"y");this.refresh()};c.prototype.setAxis=function(a,d){d+="Axis";a=a[d];var f=this.series.chart;this.series[d]=a instanceof p?a:k(a)?f[d][a]||f.get(a):null};c.prototype.toAnchor=function(){var a=[this.plotX,this.plotY,0,0];this.series.chart.inverted&&(a[0]=this.plotY,a[1]=this.plotX);return a};c.prototype.getLabelConfig=function(){return{x:this.x,y:this.y,point:this}};
c.prototype.isInsidePlot=function(){var a=this.plotX,d=this.plotY,f=this.series.xAxis,e=this.series.yAxis,n={x:a,y:d,isInsidePlot:!0};f&&(n.isInsidePlot=k(a)&&0<=a&&a<=f.len);e&&(n.isInsidePlot=n.isInsidePlot&&k(d)&&0<=d&&d<=e.len);h(this.series.chart,"afterIsInsidePlot",n);return n.isInsidePlot};c.prototype.refresh=function(){var a=this.series,d=a.xAxis;a=a.yAxis;var f=this.getOptions();d?(this.x=f.x,this.plotX=d.toPixels(f.x,!0)):(this.x=null,this.plotX=f.x);a?(this.y=f.y,this.plotY=a.toPixels(f.y,
!0)):(this.y=null,this.plotY=f.y);this.isInside=this.isInsidePlot()};c.prototype.translate=function(a,d,f,e){this.hasDynamicOptions()||(this.plotX+=f,this.plotY+=e,this.refreshOptions())};c.prototype.scale=function(a,d,f,e){if(!this.hasDynamicOptions()){var n=this.plotY*e;this.plotX=(1-f)*a+this.plotX*f;this.plotY=(1-e)*d+n;this.refreshOptions()}};c.prototype.rotate=function(a,d,f){if(!this.hasDynamicOptions()){var e=Math.cos(f);f=Math.sin(f);var n=this.plotX,m=this.plotY;n-=a;m-=d;this.plotX=n*e-
m*f+a;this.plotY=n*f+m*e+d;this.refreshOptions()}};c.prototype.refreshOptions=function(){var a=this.series,d=a.xAxis;a=a.yAxis;this.x=this.options.x=d?this.options.x=d.toValue(this.plotX,!0):this.plotX;this.y=this.options.y=a?a.toValue(this.plotY,!0):this.plotY};return c}()});r(c,"Extensions/Annotations/Mixins/ControllableMixin.js",[c["Extensions/Annotations/ControlPoint.js"],c["Extensions/Annotations/MockPoint.js"],c["Core/Tooltip.js"],c["Core/Utilities.js"]],function(a,c,p,k){var h=k.isObject,u=
k.isString,q=k.merge,d=k.splat;return{init:function(a,e,d){this.annotation=a;this.chart=a.chart;this.options=e;this.points=[];this.controlPoints=[];this.index=d;this.linkPoints();this.addControlPoints()},attr:function(){this.graphic.attr.apply(this.graphic,arguments)},getPointsOptions:function(){var a=this.options;return a.points||a.point&&d(a.point)},attrsFromOptions:function(a){var e=this.constructor.attrsMap,f={},m,d=this.chart.styledMode;for(m in a){var c=e[m];!c||d&&-1!==["fill","stroke","stroke-width"].indexOf(c)||
(f[c]=a[m])}return f},anchor:function(a){var e=a.series.getPlotBox(),f=a.series.chart,m=a.mock?a.toAnchor():p.prototype.getAnchor.call({chart:a.series.chart},a);m={x:m[0]+(this.options.x||0),y:m[1]+(this.options.y||0),height:m[2]||0,width:m[3]||0};return{relativePosition:m,absolutePosition:q(m,{x:m.x+(a.mock?e.translateX:f.plotLeft),y:m.y+(a.mock?e.translateY:f.plotTop)})}},point:function(a,e){if(a&&a.series)return a;e&&null!==e.series||(h(a)?e=new c(this.chart,this,a):u(a)?e=this.chart.get(a)||null:
"function"===typeof a&&(e=a.call(e,this),e=e.series?e:new c(this.chart,this,a)));return e},linkPoints:function(){var a=this.getPointsOptions(),e=this.points,d=a&&a.length||0,m;for(m=0;m<d;m++){var c=this.point(a[m],e[m]);if(!c){e.length=0;return}c.mock&&c.refresh();e[m]=c}return e},addControlPoints:function(){var d=this.options.controlPoints;(d||[]).forEach(function(e,f){e=q(this.options.controlPointOptions,e);e.index||(e.index=f);d[f]=e;this.controlPoints.push(new a(this.chart,this,e))},this)},shouldBeDrawn:function(){return!!this.points.length},
render:function(a){this.controlPoints.forEach(function(a){a.render()})},redraw:function(a){this.controlPoints.forEach(function(e){e.redraw(a)})},transform:function(a,e,d,m,c){if(this.chart.inverted){var f=e;e=d;d=f}this.points.forEach(function(f,b){this.transformPoint(a,e,d,m,c,b)},this)},transformPoint:function(a,e,d,m,h,t){var f=this.points[t];f.mock||(f=this.points[t]=c.fromPoint(f));f[a](e,d,m,h)},translate:function(a,e){this.transform("translate",null,null,a,e)},translatePoint:function(a,e,d){this.transformPoint("translate",
null,null,a,e,d)},translateShape:function(a,e){var d=this.annotation.chart,m=this.annotation.userOptions,f=d.annotations.indexOf(this.annotation);d=d.options.annotations[f];this.translatePoint(a,e,0);d[this.collection][this.index].point=this.options.point;m[this.collection][this.index].point=this.options.point},rotate:function(a,d,c){this.transform("rotate",a,d,c)},scale:function(a,d,c,m){this.transform("scale",a,d,c,m)},setControlPointsVisibility:function(a){this.controlPoints.forEach(function(d){d.setVisibility(a)})},
destroy:function(){this.graphic&&(this.graphic=this.graphic.destroy());this.tracker&&(this.tracker=this.tracker.destroy());this.controlPoints.forEach(function(a){a.destroy()});this.options=this.controlPoints=this.points=this.chart=null;this.annotation&&(this.annotation=null)},update:function(a){var d=this.annotation;a=q(!0,this.options,a);var c=this.graphic.parentGroup;this.destroy();this.constructor(d,a);this.render(c);this.redraw()}}});r(c,"Extensions/Annotations/Mixins/MarkerMixin.js",[c["Core/Chart/Chart.js"],
c["Core/Renderer/SVG/SVGRenderer.js"],c["Core/Utilities.js"]],function(a,c,p){function k(a){return function(d){this.attr(a,"url(#"+d+")")}}var h=p.addEvent,u=p.defined,q=p.merge,d=p.objectEach,f=p.uniqueKey,e={arrow:{tagName:"marker",attributes:{display:"none",id:"arrow",refY:5,refX:9,markerWidth:10,markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 0 L 10 5 L 0 10 Z","stroke-width":0}}]},"reverse-arrow":{tagName:"marker",attributes:{display:"none",id:"reverse-arrow",refY:5,refX:1,markerWidth:10,
markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 5 L 10 0 L 10 10 Z","stroke-width":0}}]}};c.prototype.addMarker=function(a,d){var e={attributes:{id:a}},c={stroke:d.color||"none",fill:d.color||"rgba(0, 0, 0, 0.75)"};e.children=d.children.map(function(a){return q(c,a)});d=q(!0,{attributes:{markerWidth:20,markerHeight:20,refX:0,refY:0,orient:"auto"}},d,e);d=this.definition(d);d.id=a;return d};c={markerEndSetter:k("marker-end"),markerStartSetter:k("marker-start"),setItemMarkers:function(a){var d=
a.options,e=a.chart,c=e.options.defs,g=d.fill,b=u(g)&&"none"!==g?g:d.stroke;["markerStart","markerEnd"].forEach(function(l){var m,g=d[l],n;if(g){for(n in c){var w=c[n];if((g===(null===(m=w.attributes)||void 0===m?void 0:m.id)||g===w.id)&&"marker"===w.tagName){var y=w;break}}y&&(m=a[l]=e.renderer.addMarker((d.id||f())+"-"+g,q(y,{color:b})),a.attr(l,m.getAttribute("id")))}})}};h(a,"afterGetContainer",function(){this.options.defs=q(e,this.options.defs||{});d(this.options.defs,function(a){var d=a.attributes;
"marker"===a.tagName&&d&&"none"!==d.display&&this.renderer.addMarker(d.id,a)},this)});return c});r(c,"Extensions/Annotations/Controllables/ControllablePath.js",[c["Extensions/Annotations/Mixins/ControllableMixin.js"],c["Core/Globals.js"],c["Extensions/Annotations/Mixins/MarkerMixin.js"],c["Core/Utilities.js"]],function(a,c,p,k){var h=k.extend,u="rgba(192,192,192,"+(c.svg?.0001:.002)+")";return function(){function c(d,c,e){this.addControlPoints=a.addControlPoints;this.anchor=a.anchor;this.attr=a.attr;
this.attrsFromOptions=a.attrsFromOptions;this.destroy=a.destroy;this.getPointsOptions=a.getPointsOptions;this.init=a.init;this.linkPoints=a.linkPoints;this.point=a.point;this.rotate=a.rotate;this.scale=a.scale;this.setControlPointsVisibility=a.setControlPointsVisibility;this.setMarkers=p.setItemMarkers;this.transform=a.transform;this.transformPoint=a.transformPoint;this.translate=a.translate;this.translatePoint=a.translatePoint;this.translateShape=a.translateShape;this.update=a.update;this.type="path";
this.init(d,c,e);this.collection="shapes"}c.prototype.toD=function(){var a=this.options.d;if(a)return"function"===typeof a?a.call(this):a;a=this.points;var c=a.length,e=c,n=a[0],m=e&&this.anchor(n).absolutePosition,h=0,t=[];if(m)for(t.push(["M",m.x,m.y]);++h<c&&e;)n=a[h],e=n.command||"L",m=this.anchor(n).absolutePosition,"M"===e?t.push([e,m.x,m.y]):"L"===e?t.push([e,m.x,m.y]):"Z"===e&&t.push([e]),e=n.series.visible;return e?this.chart.renderer.crispLine(t,this.graphic.strokeWidth()):null};c.prototype.shouldBeDrawn=
function(){return a.shouldBeDrawn.call(this)||!!this.options.d};c.prototype.render=function(d){var c=this.options,e=this.attrsFromOptions(c);this.graphic=this.annotation.chart.renderer.path([["M",0,0]]).attr(e).add(d);c.className&&this.graphic.addClass(c.className);this.tracker=this.annotation.chart.renderer.path([["M",0,0]]).addClass("highcharts-tracker-line").attr({zIndex:2}).add(d);this.annotation.chart.styledMode||this.tracker.attr({"stroke-linejoin":"round",stroke:u,fill:u,"stroke-width":this.graphic.strokeWidth()+
2*c.snap});a.render.call(this);h(this.graphic,{markerStartSetter:p.markerStartSetter,markerEndSetter:p.markerEndSetter});this.setMarkers(this)};c.prototype.redraw=function(d){var c=this.toD(),e=d?"animate":"attr";c?(this.graphic[e]({d:c}),this.tracker[e]({d:c})):(this.graphic.attr({d:"M 0 -9000000000"}),this.tracker.attr({d:"M 0 -9000000000"}));this.graphic.placed=this.tracker.placed=!!c;a.redraw.call(this,d)};c.attrsMap={dashStyle:"dashstyle",strokeWidth:"stroke-width",stroke:"stroke",fill:"fill",
zIndex:"zIndex"};return c}()});r(c,"Extensions/Annotations/Controllables/ControllableRect.js",[c["Extensions/Annotations/Mixins/ControllableMixin.js"],c["Extensions/Annotations/Controllables/ControllablePath.js"],c["Core/Utilities.js"]],function(a,c,p){var k=p.merge;return function(){function h(c,h,d){this.addControlPoints=a.addControlPoints;this.anchor=a.anchor;this.attr=a.attr;this.attrsFromOptions=a.attrsFromOptions;this.destroy=a.destroy;this.getPointsOptions=a.getPointsOptions;this.init=a.init;
this.linkPoints=a.linkPoints;this.point=a.point;this.rotate=a.rotate;this.scale=a.scale;this.setControlPointsVisibility=a.setControlPointsVisibility;this.shouldBeDrawn=a.shouldBeDrawn;this.transform=a.transform;this.transformPoint=a.transformPoint;this.translatePoint=a.translatePoint;this.translateShape=a.translateShape;this.update=a.update;this.type="rect";this.translate=a.translateShape;this.init(c,h,d);this.collection="shapes"}h.prototype.render=function(c){var h=this.attrsFromOptions(this.options);
this.graphic=this.annotation.chart.renderer.rect(0,-9E9,0,0).attr(h).add(c);a.render.call(this)};h.prototype.redraw=function(c){var h=this.anchor(this.points[0]).absolutePosition;if(h)this.graphic[c?"animate":"attr"]({x:h.x,y:h.y,width:this.options.width,height:this.options.height});else this.attr({x:0,y:-9E9});this.graphic.placed=!!h;a.redraw.call(this,c)};h.attrsMap=k(c.attrsMap,{width:"width",height:"height"});return h}()});r(c,"Extensions/Annotations/Controllables/ControllableCircle.js",[c["Extensions/Annotations/Mixins/ControllableMixin.js"],
c["Extensions/Annotations/Controllables/ControllablePath.js"],c["Core/Utilities.js"]],function(a,c,p){var k=p.merge;return function(){function h(c,h,d){this.addControlPoints=a.addControlPoints;this.anchor=a.anchor;this.attr=a.attr;this.attrsFromOptions=a.attrsFromOptions;this.destroy=a.destroy;this.getPointsOptions=a.getPointsOptions;this.init=a.init;this.linkPoints=a.linkPoints;this.point=a.point;this.rotate=a.rotate;this.scale=a.scale;this.setControlPointsVisibility=a.setControlPointsVisibility;
this.shouldBeDrawn=a.shouldBeDrawn;this.transform=a.transform;this.transformPoint=a.transformPoint;this.translatePoint=a.translatePoint;this.translateShape=a.translateShape;this.update=a.update;this.type="circle";this.translate=a.translateShape;this.init(c,h,d);this.collection="shapes"}h.prototype.render=function(c){var h=this.attrsFromOptions(this.options);this.graphic=this.annotation.chart.renderer.circle(0,-9E9,0).attr(h).add(c);a.render.call(this)};h.prototype.redraw=function(c){var h=this.anchor(this.points[0]).absolutePosition;
if(h)this.graphic[c?"animate":"attr"]({x:h.x,y:h.y,r:this.options.r});else this.graphic.attr({x:0,y:-9E9});this.graphic.placed=!!h;a.redraw.call(this,c)};h.prototype.setRadius=function(a){this.options.r=a};h.attrsMap=k(c.attrsMap,{r:"r"});return h}()});r(c,"Extensions/Annotations/Controllables/ControllableLabel.js",[c["Extensions/Annotations/Mixins/ControllableMixin.js"],c["Extensions/Annotations/MockPoint.js"],c["Core/Renderer/SVG/SVGRenderer.js"],c["Core/Tooltip.js"],c["Core/Utilities.js"]],function(a,
c,p,k,h){var u=h.extend,q=h.format,d=h.isNumber,f=h.pick;h=function(){function d(d,c,e){this.addControlPoints=a.addControlPoints;this.attr=a.attr;this.attrsFromOptions=a.attrsFromOptions;this.destroy=a.destroy;this.getPointsOptions=a.getPointsOptions;this.init=a.init;this.linkPoints=a.linkPoints;this.point=a.point;this.rotate=a.rotate;this.scale=a.scale;this.setControlPointsVisibility=a.setControlPointsVisibility;this.shouldBeDrawn=a.shouldBeDrawn;this.transform=a.transform;this.transformPoint=a.transformPoint;
this.translateShape=a.translateShape;this.update=a.update;this.init(d,c,e);this.collection="labels"}d.alignedPosition=function(a,d){var c=a.align,e=a.verticalAlign,g=(d.x||0)+(a.x||0),b=(d.y||0)+(a.y||0),l,m;"right"===c?l=1:"center"===c&&(l=2);l&&(g+=(d.width-(a.width||0))/l);"bottom"===e?m=1:"middle"===e&&(m=2);m&&(b+=(d.height-(a.height||0))/m);return{x:Math.round(g),y:Math.round(b)}};d.justifiedOptions=function(a,d,c,e){var g=c.align,b=c.verticalAlign,l=d.box?0:d.padding||0,m=d.getBBox();d={align:g,
verticalAlign:b,x:c.x,y:c.y,width:d.width,height:d.height};c=e.x-a.plotLeft;var f=e.y-a.plotTop;e=c+l;0>e&&("right"===g?d.align="left":d.x=-e);e=c+m.width-l;e>a.plotWidth&&("left"===g?d.align="right":d.x=a.plotWidth-e);e=f+l;0>e&&("bottom"===b?d.verticalAlign="top":d.y=-e);e=f+m.height-l;e>a.plotHeight&&("top"===b?d.verticalAlign="bottom":d.y=a.plotHeight-e);return d};d.prototype.translatePoint=function(d,c){a.translatePoint.call(this,d,c,0)};d.prototype.translate=function(a,d){var c=this.annotation.chart,
e=this.annotation.userOptions,g=c.annotations.indexOf(this.annotation);g=c.options.annotations[g];c.inverted&&(c=a,a=d,d=c);this.options.x+=a;this.options.y+=d;g[this.collection][this.index].x=this.options.x;g[this.collection][this.index].y=this.options.y;e[this.collection][this.index].x=this.options.x;e[this.collection][this.index].y=this.options.y};d.prototype.render=function(c){var e=this.options,f=this.attrsFromOptions(e),h=e.style;this.graphic=this.annotation.chart.renderer.label("",0,-9999,
e.shape,null,null,e.useHTML,null,"annotation-label").attr(f).add(c);this.annotation.chart.styledMode||("contrast"===h.color&&(h.color=this.annotation.chart.renderer.getContrast(-1<d.shapesWithoutBackground.indexOf(e.shape)?"#FFFFFF":e.backgroundColor)),this.graphic.css(e.style).shadow(e.shadow));e.className&&this.graphic.addClass(e.className);this.graphic.labelrank=e.labelrank;a.render.call(this)};d.prototype.redraw=function(d){var c=this.options,e=this.text||c.format||c.text,f=this.graphic,g=this.points[0];
f.attr({text:e?q(e,g.getLabelConfig(),this.annotation.chart):c.formatter.call(g,this)});c=this.anchor(g);(e=this.position(c))?(f.alignAttr=e,e.anchorX=c.absolutePosition.x,e.anchorY=c.absolutePosition.y,f[d?"animate":"attr"](e)):f.attr({x:0,y:-9999});f.placed=!!e;a.redraw.call(this,d)};d.prototype.anchor=function(d){var c=a.anchor.apply(this,arguments),e=this.options.x||0,f=this.options.y||0;c.absolutePosition.x-=e;c.absolutePosition.y-=f;c.relativePosition.x-=e;c.relativePosition.y-=f;return c};
d.prototype.position=function(a){var e=this.graphic,h=this.annotation.chart,n=this.points[0],g=this.options,b=a.absolutePosition,l=a.relativePosition;if(a=n.series.visible&&c.prototype.isInsidePlot.call(n)){if(g.distance)var v=k.prototype.getPosition.call({chart:h,distance:f(g.distance,16)},e.width,e.height,{plotX:l.x,plotY:l.y,negative:n.negative,ttBelow:n.ttBelow,h:l.height||l.width});else g.positioner?v=g.positioner.call(this):(n={x:b.x,y:b.y,width:0,height:0},v=d.alignedPosition(u(g,{width:e.width,
height:e.height}),n),"justify"===this.options.overflow&&(v=d.alignedPosition(d.justifiedOptions(h,e,g,v),n)));g.crop&&(g=v.x-h.plotLeft,n=v.y-h.plotTop,a=h.isInsidePlot(g,n)&&h.isInsidePlot(g+e.width,n+e.height))}return a?v:null};d.attrsMap={backgroundColor:"fill",borderColor:"stroke",borderWidth:"stroke-width",zIndex:"zIndex",borderRadius:"r",padding:"padding"};d.shapesWithoutBackground=["connector"];return d}();p.prototype.symbols.connector=function(a,c,f,h,k){var e=k&&k.anchorX;k=k&&k.anchorY;
var b=f/2;if(d(e)&&d(k)){var l=[["M",e,k]];var v=c-k;0>v&&(v=-h-v);v<f&&(b=e<a+f/2?v:f-v);k>c+h?l.push(["L",a+b,c+h]):k<c?l.push(["L",a+b,c]):e<a?l.push(["L",a,c+h/2]):e>a+f&&l.push(["L",a+f,c+h/2])}return l||[]};return h});r(c,"Extensions/Annotations/Controllables/ControllableImage.js",[c["Extensions/Annotations/Controllables/ControllableLabel.js"],c["Extensions/Annotations/Mixins/ControllableMixin.js"]],function(a,c){return function(){function p(a,h,p){this.addControlPoints=c.addControlPoints;this.anchor=
c.anchor;this.attr=c.attr;this.attrsFromOptions=c.attrsFromOptions;this.destroy=c.destroy;this.getPointsOptions=c.getPointsOptions;this.init=c.init;this.linkPoints=c.linkPoints;this.point=c.point;this.rotate=c.rotate;this.scale=c.scale;this.setControlPointsVisibility=c.setControlPointsVisibility;this.shouldBeDrawn=c.shouldBeDrawn;this.transform=c.transform;this.transformPoint=c.transformPoint;this.translatePoint=c.translatePoint;this.translateShape=c.translateShape;this.update=c.update;this.type=
"image";this.translate=c.translateShape;this.init(a,h,p);this.collection="shapes"}p.prototype.render=function(a){var h=this.attrsFromOptions(this.options),k=this.options;this.graphic=this.annotation.chart.renderer.image(k.src,0,-9E9,k.width,k.height).attr(h).add(a);this.graphic.width=k.width;this.graphic.height=k.height;c.render.call(this)};p.prototype.redraw=function(k){var h=this.anchor(this.points[0]);if(h=a.prototype.position.call(this,h))this.graphic[k?"animate":"attr"]({x:h.x,y:h.y});else this.graphic.attr({x:0,
y:-9E9});this.graphic.placed=!!h;c.redraw.call(this,k)};p.attrsMap={width:"width",height:"height",zIndex:"zIndex"};return p}()});r(c,"Extensions/Annotations/Annotations.js",[c["Core/Animation/AnimationUtilities.js"],c["Core/Chart/Chart.js"],c["Extensions/Annotations/Mixins/ControllableMixin.js"],c["Extensions/Annotations/Controllables/ControllableRect.js"],c["Extensions/Annotations/Controllables/ControllableCircle.js"],c["Extensions/Annotations/Controllables/ControllablePath.js"],c["Extensions/Annotations/Controllables/ControllableImage.js"],
c["Extensions/Annotations/Controllables/ControllableLabel.js"],c["Extensions/Annotations/ControlPoint.js"],c["Extensions/Annotations/Mixins/EventEmitterMixin.js"],c["Core/Globals.js"],c["Extensions/Annotations/MockPoint.js"],c["Core/Pointer.js"],c["Core/Utilities.js"]],function(a,c,p,k,h,D,q,d,f,e,n,m,r,t){var g=a.getDeferredAnimation;a=c.prototype;var b=t.addEvent,l=t.defined,v=t.destroyObjectProperties,z=t.erase,A=t.extend,w=t.find,y=t.fireEvent,x=t.merge,B=t.pick,E=t.splat;t=t.wrap;var C=function(){function b(b,
a){this.annotation=void 0;this.coll="annotations";this.shapesGroup=this.labelsGroup=this.labelCollector=this.group=this.graphic=this.animationConfig=this.collection=void 0;this.chart=b;this.points=[];this.controlPoints=[];this.coll="annotations";this.labels=[];this.shapes=[];this.options=x(this.defaultOptions,a);this.userOptions=a;a=this.getLabelsAndShapesOptions(this.options,a);this.options.labels=a.labels;this.options.shapes=a.shapes;this.init(b,this.options)}b.prototype.init=function(){var b=this.chart,
a=this.options.animation;this.linkPoints();this.addControlPoints();this.addShapes();this.addLabels();this.setLabelCollector();this.animationConfig=g(b,a)};b.prototype.getLabelsAndShapesOptions=function(b,a){var c={};["labels","shapes"].forEach(function(d){b[d]&&(c[d]=E(a[d]).map(function(a,c){return x(b[d][c],a)}))});return c};b.prototype.addShapes=function(){(this.options.shapes||[]).forEach(function(b,a){b=this.initShape(b,a);x(!0,this.options.shapes[a],b.options)},this)};b.prototype.addLabels=
function(){(this.options.labels||[]).forEach(function(b,a){b=this.initLabel(b,a);x(!0,this.options.labels[a],b.options)},this)};b.prototype.addClipPaths=function(){this.setClipAxes();this.clipXAxis&&this.clipYAxis&&(this.clipRect=this.chart.renderer.clipRect(this.getClipBox()))};b.prototype.setClipAxes=function(){var b=this.chart.xAxis,a=this.chart.yAxis,c=(this.options.labels||[]).concat(this.options.shapes||[]).reduce(function(c,d){return[b[d&&d.point&&d.point.xAxis]||c[0],a[d&&d.point&&d.point.yAxis]||
c[1]]},[]);this.clipXAxis=c[0];this.clipYAxis=c[1]};b.prototype.getClipBox=function(){if(this.clipXAxis&&this.clipYAxis)return{x:this.clipXAxis.left,y:this.clipYAxis.top,width:this.clipXAxis.width,height:this.clipYAxis.height}};b.prototype.setLabelCollector=function(){var b=this;b.labelCollector=function(){return b.labels.reduce(function(b,a){a.options.allowOverlap||b.push(a.graphic);return b},[])};b.chart.labelCollectors.push(b.labelCollector)};b.prototype.setOptions=function(b){this.options=x(this.defaultOptions,
b)};b.prototype.redraw=function(b){this.linkPoints();this.graphic||this.render();this.clipRect&&this.clipRect.animate(this.getClipBox());this.redrawItems(this.shapes,b);this.redrawItems(this.labels,b);p.redraw.call(this,b)};b.prototype.redrawItems=function(b,a){for(var c=b.length;c--;)this.redrawItem(b[c],a)};b.prototype.renderItems=function(b){for(var a=b.length;a--;)this.renderItem(b[a])};b.prototype.render=function(){var b=this.chart.renderer;this.graphic=b.g("annotation").attr({opacity:0,zIndex:this.options.zIndex,
visibility:this.options.visible?"visible":"hidden"}).add();this.shapesGroup=b.g("annotation-shapes").add(this.graphic).clip(this.chart.plotBoxClip);this.labelsGroup=b.g("annotation-labels").attr({translateX:0,translateY:0}).add(this.graphic);this.addClipPaths();this.clipRect&&this.graphic.clip(this.clipRect);this.renderItems(this.shapes);this.renderItems(this.labels);this.addEvents();p.render.call(this)};b.prototype.setVisibility=function(b){var a=this.options;b=B(b,!a.visible);this.graphic.attr("visibility",
b?"visible":"hidden");b||this.setControlPointsVisibility(!1);a.visible=b};b.prototype.setControlPointsVisibility=function(b){var a=function(a){a.setControlPointsVisibility(b)};p.setControlPointsVisibility.call(this,b);this.shapes.forEach(a);this.labels.forEach(a)};b.prototype.destroy=function(){var b=this.chart,a=function(b){b.destroy()};this.labels.forEach(a);this.shapes.forEach(a);this.clipYAxis=this.clipXAxis=null;z(b.labelCollectors,this.labelCollector);e.destroy.call(this);p.destroy.call(this);
v(this,b)};b.prototype.remove=function(){return this.chart.removeAnnotation(this)};b.prototype.update=function(b,a){var c=this.chart,d=this.getLabelsAndShapesOptions(this.userOptions,b),l=c.annotations.indexOf(this);b=x(!0,this.userOptions,b);b.labels=d.labels;b.shapes=d.shapes;this.destroy();this.constructor(c,b);c.options.annotations[l]=b;this.isUpdating=!0;B(a,!0)&&c.redraw();y(this,"afterUpdate");this.isUpdating=!1};b.prototype.initShape=function(a,c){a=x(this.options.shapeOptions,{controlPointOptions:this.options.controlPointOptions},
a);c=new b.shapesMap[a.type](this,a,c);c.itemType="shape";this.shapes.push(c);return c};b.prototype.initLabel=function(b,a){b=x(this.options.labelOptions,{controlPointOptions:this.options.controlPointOptions},b);a=new d(this,b,a);a.itemType="label";this.labels.push(a);return a};b.prototype.redrawItem=function(b,a){b.linkPoints();b.shouldBeDrawn()?(b.graphic||this.renderItem(b),b.redraw(B(a,!0)&&b.graphic.placed),b.points.length&&this.adjustVisibility(b)):this.destroyItem(b)};b.prototype.adjustVisibility=
function(b){var a=!1,c=b.graphic;b.points.forEach(function(b){!1!==b.series.visible&&!1!==b.visible&&(a=!0)});a?"hidden"===c.visibility&&c.show():c.hide()};b.prototype.destroyItem=function(b){z(this[b.itemType+"s"],b);b.destroy()};b.prototype.renderItem=function(b){b.render("label"===b.itemType?this.labelsGroup:this.shapesGroup)};b.ControlPoint=f;b.MockPoint=m;b.shapesMap={rect:k,circle:h,path:D,image:q};b.types={};return b}();x(!0,C.prototype,p,e,x(C.prototype,{nonDOMEvents:["add","afterUpdate",
"drag","remove"],defaultOptions:{visible:!0,animation:{},draggable:"xy",labelOptions:{align:"center",allowOverlap:!1,backgroundColor:"rgba(0, 0, 0, 0.75)",borderColor:"black",borderRadius:3,borderWidth:1,className:"",crop:!1,formatter:function(){return l(this.y)?this.y:"Annotation label"},includeInDataExport:!0,overflow:"justify",padding:5,shadow:!1,shape:"callout",style:{fontSize:"11px",fontWeight:"normal",color:"contrast"},useHTML:!1,verticalAlign:"bottom",x:0,y:-16},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",
strokeWidth:1,fill:"rgba(0, 0, 0, 0.75)",r:0,snap:2},controlPointOptions:{symbol:"circle",width:10,height:10,style:{stroke:"black","stroke-width":2,fill:"white"},visible:!1,events:{}},events:{},zIndex:6}}));n.extendAnnotation=function(b,a,c,d){a=a||C;x(!0,b.prototype,a.prototype,c);b.prototype.defaultOptions=x(b.prototype.defaultOptions,d||{})};A(a,{initAnnotation:function(b){b=new (C.types[b.type]||C)(this,b);this.annotations.push(b);return b},addAnnotation:function(b,a){b=this.initAnnotation(b);
this.options.annotations.push(b.options);B(a,!0)&&(b.redraw(),b.graphic.attr({opacity:1}));return b},removeAnnotation:function(b){var a=this.annotations,c="annotations"===b.coll?b:w(a,function(a){return a.options.id===b});c&&(y(c,"remove"),z(this.options.annotations,c.options),z(a,c),c.destroy())},drawAnnotations:function(){this.plotBoxClip.attr(this.plotBox);this.annotations.forEach(function(b){b.redraw();b.graphic.animate({opacity:1},b.animationConfig)})}});a.collectionsWithUpdate.push("annotations");
a.collectionsWithInit.annotations=[a.addAnnotation];b(c,"afterInit",function(){this.annotations=[];this.options.annotations||(this.options.annotations=[])});a.callbacks.push(function(a){a.plotBoxClip=this.renderer.clipRect(this.plotBox);a.controlPointsGroup=a.renderer.g("control-points").attr({zIndex:99}).clip(a.plotBoxClip).add();a.options.annotations.forEach(function(b,c){if(!a.annotations.some(function(a){return a.options===b})){var d=a.initAnnotation(b);a.options.annotations[c]=d.options}});a.drawAnnotations();
b(a,"redraw",a.drawAnnotations);b(a,"destroy",function(){a.plotBoxClip.destroy();a.controlPointsGroup.destroy()});b(a,"exportData",function(b){var c,d,l,e,f,g,w,y,v=a.annotations,h=(this.options.exporting&&this.options.exporting.csv||{}).columnHeaderFormatter,x=!b.dataRows[1].xValues,B=null===(d=null===(c=a.options.lang)||void 0===c?void 0:c.exportData)||void 0===d?void 0:d.annotationHeader;c=function(b){if(h){var a=h(b);if(!1!==a)return a}a=B+" "+b;return x?{columnTitle:a,topLevelColumnTitle:a}:
a};var k=b.dataRows[0].length,m=null===(f=null===(e=null===(l=a.options.exporting)||void 0===l?void 0:l.csv)||void 0===e?void 0:e.annotations)||void 0===f?void 0:f.itemDelimiter,z=null===(y=null===(w=null===(g=a.options.exporting)||void 0===g?void 0:g.csv)||void 0===w?void 0:w.annotations)||void 0===y?void 0:y.join;v.forEach(function(a){a.options.labelOptions.includeInDataExport&&a.labels.forEach(function(a){if(a.options.text){var c=a.options.text;a.points.forEach(function(a){var d=a.x,l=a.series.xAxis?
a.series.xAxis.options.index:-1,e=!1;if(-1===l){a=b.dataRows[0].length;for(var f=Array(a),g=0;g<a;++g)f[g]="";f.push(c);f.xValues=[];f.xValues[l]=d;b.dataRows.push(f);e=!0}e||b.dataRows.forEach(function(b,a){!e&&b.xValues&&void 0!==l&&d===b.xValues[l]&&(z&&b.length>k?b[b.length-1]+=m+c:b.push(c),e=!0)});if(!e){a=b.dataRows[0].length;f=Array(a);for(g=0;g<a;++g)f[g]="";f[0]=d;f.push(c);f.xValues=[];void 0!==l&&(f.xValues[l]=d);b.dataRows.push(f)}})}})});var n=0;b.dataRows.forEach(function(b){n=Math.max(n,
b.length)});l=n-b.dataRows[0].length;for(e=0;e<l;e++)f=c(e+1),x?(b.dataRows[0].push(f.topLevelColumnTitle),b.dataRows[1].push(f.columnTitle)):b.dataRows[0].push(f)})});t(r.prototype,"onContainerMouseDown",function(b){this.chart.hasDraggedAnnotation||b.apply(this,Array.prototype.slice.call(arguments,1))});return n.Annotation=C});r(c,"Mixins/Navigation.js",[],function(){return{initUpdate:function(a){a.navigation||(a.navigation={updates:[],update:function(a,c){this.updates.forEach(function(k){k.update.call(k.context,
a,c)})}})},addUpdate:function(a,c){c.navigation||this.initUpdate(c);c.navigation.updates.push({update:a,context:c})}}});r(c,"Extensions/Annotations/NavigationBindings.js",[c["Extensions/Annotations/Annotations.js"],c["Core/Chart/Chart.js"],c["Mixins/Navigation.js"],c["Core/Globals.js"],c["Core/Utilities.js"]],function(a,c,p,k,h){function u(b){var a=b.prototype.defaultOptions.events&&b.prototype.defaultOptions.events.click;g(!0,b.prototype.defaultOptions.events,{click:function(b){var c=this,d=c.chart.navigationBindings,
l=d.activeAnnotation;a&&a.call(c,b);l!==c?(d.deselectAnnotation(),d.activeAnnotation=c,c.setControlPointsVisibility(!0),e(d,"showPopup",{annotation:c,formType:"annotation-toolbar",options:d.annotationToFields(c),onSubmit:function(b){var a={};"remove"===b.actionType?(d.activeAnnotation=!1,d.chart.removeAnnotation(c)):(d.fieldsToOptions(b.fields,a),d.deselectAnnotation(),b=a.typeOptions,"measure"===c.options.type&&(b.crosshairY.enabled=0!==b.crosshairY.strokeWidth,b.crosshairX.enabled=0!==b.crosshairX.strokeWidth),
c.update(a))}})):e(d,"closePopup");b.activeAnnotation=!0}})}var q=h.addEvent,d=h.attr,f=h.format,e=h.fireEvent,n=h.isArray,m=h.isFunction,r=h.isNumber,t=h.isObject,g=h.merge,b=h.objectEach,l=h.pick;h=h.setOptions;var v=k.doc,z=k.win,A=function(){function a(b,a){this.selectedButton=this.boundClassNames=void 0;this.chart=b;this.options=a;this.eventsToUnbind=[];this.container=v.getElementsByClassName(this.options.bindingsClassName||"")}a.prototype.initEvents=function(){var a=this,c=a.chart,d=a.container,
l=a.options;a.boundClassNames={};b(l.bindings||{},function(b){a.boundClassNames[b.className]=b});[].forEach.call(d,function(b){a.eventsToUnbind.push(q(b,"click",function(c){var d=a.getButtonEvents(b,c);d&&a.bindingsButtonClick(d.button,d.events,c)}))});b(l.events||{},function(b,c){m(b)&&a.eventsToUnbind.push(q(a,c,b,{passive:!1}))});a.eventsToUnbind.push(q(c.container,"click",function(b){!c.cancelClick&&c.isInsidePlot(b.chartX-c.plotLeft,b.chartY-c.plotTop)&&a.bindingsChartClick(this,b)}));a.eventsToUnbind.push(q(c.container,
k.isTouchDevice?"touchmove":"mousemove",function(b){a.bindingsContainerMouseMove(this,b)},k.isTouchDevice?{passive:!1}:void 0))};a.prototype.initUpdate=function(){var b=this;p.addUpdate(function(a){b.update(a)},this.chart)};a.prototype.bindingsButtonClick=function(b,a,c){var d=this.chart;this.selectedButtonElement&&(e(this,"deselectButton",{button:this.selectedButtonElement}),this.nextEvent&&(this.currentUserDetails&&"annotations"===this.currentUserDetails.coll&&d.removeAnnotation(this.currentUserDetails),
this.mouseMoveEvent=this.nextEvent=!1));this.selectedButton=a;this.selectedButtonElement=b;e(this,"selectButton",{button:b});a.init&&a.init.call(this,b,c);(a.start||a.steps)&&d.renderer.boxWrapper.addClass("highcharts-draw-mode")};a.prototype.bindingsChartClick=function(b,a){b=this.chart;var c=this.selectedButton;b=b.renderer.boxWrapper;var d;if(d=this.activeAnnotation&&!a.activeAnnotation&&a.target.parentNode){a:{d=a.target;var l=z.Element.prototype,f=l.matches||l.msMatchesSelector||l.webkitMatchesSelector,
g=null;if(l.closest)g=l.closest.call(d,".highcharts-popup");else{do{if(f.call(d,".highcharts-popup"))break a;d=d.parentElement||d.parentNode}while(null!==d&&1===d.nodeType)}d=g}d=!d}d&&e(this,"closePopup");c&&c.start&&(this.nextEvent?(this.nextEvent(a,this.currentUserDetails),this.steps&&(this.stepIndex++,c.steps[this.stepIndex]?this.mouseMoveEvent=this.nextEvent=c.steps[this.stepIndex]:(e(this,"deselectButton",{button:this.selectedButtonElement}),b.removeClass("highcharts-draw-mode"),c.end&&c.end.call(this,
a,this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1,this.selectedButton=null))):(this.currentUserDetails=c.start.call(this,a),c.steps?(this.stepIndex=0,this.steps=!0,this.mouseMoveEvent=this.nextEvent=c.steps[this.stepIndex]):(e(this,"deselectButton",{button:this.selectedButtonElement}),b.removeClass("highcharts-draw-mode"),this.steps=!1,this.selectedButton=null,c.end&&c.end.call(this,a,this.currentUserDetails))))};a.prototype.bindingsContainerMouseMove=function(b,a){this.mouseMoveEvent&&
this.mouseMoveEvent(a,this.currentUserDetails)};a.prototype.fieldsToOptions=function(a,c){b(a,function(b,a){var d=parseFloat(b),e=a.split("."),f=c,g=e.length-1;!r(d)||b.match(/px/g)||a.match(/format/g)||(b=d);""!==b&&"undefined"!==b&&e.forEach(function(a,c){var d=l(e[c+1],"");g===c?f[a]=b:(f[a]||(f[a]=d.match(/\d/g)?[]:{}),f=f[a])})});return c};a.prototype.deselectAnnotation=function(){this.activeAnnotation&&(this.activeAnnotation.setControlPointsVisibility(!1),this.activeAnnotation=!1)};a.prototype.annotationToFields=
function(c){function d(a,l,e,g){if(e&&a&&-1===y.indexOf(l)&&(0<=(e.indexOf&&e.indexOf(l))||e[l]||!0===e))if(n(a))g[l]=[],a.forEach(function(a,c){t(a)?(g[l][c]={},b(a,function(b,a){d(b,a,v[l],g[l][c])})):d(a,0,v[l],g[l])});else if(t(a)){var w={};n(g)?(g.push(w),w[l]={},w=w[l]):g[l]=w;b(a,function(b,a){d(b,a,0===l?e:v[l],w)})}else"format"===l?g[l]=[f(a,c.labels[0].points[0]).toString(),"text"]:n(g)?g.push([a,h(a)]):g[l]=[a,h(a)]}var e=c.options,g=a.annotationsEditable,v=g.nestedOptions,h=this.utils.getFieldType,
w=l(e.type,e.shapes&&e.shapes[0]&&e.shapes[0].type,e.labels&&e.labels[0]&&e.labels[0].itemType,"label"),y=a.annotationsNonEditable[e.langKey]||[],k={langKey:e.langKey,type:w};b(e,function(a,c){"typeOptions"===c?(k[c]={},b(e[c],function(b,a){d(b,a,v,k[c],!0)})):d(a,c,g[w],k)});return k};a.prototype.getClickedClassNames=function(b,a){var c=a.target;a=[];for(var l;c&&((l=d(c,"class"))&&(a=a.concat(l.split(" ").map(function(b){return[b,c]}))),c=c.parentNode,c!==b););return a};a.prototype.getButtonEvents=
function(b,a){var c=this,d;this.getClickedClassNames(b,a).forEach(function(b){c.boundClassNames[b[0]]&&!d&&(d={events:c.boundClassNames[b[0]],button:b[1]})});return d};a.prototype.update=function(b){this.options=g(!0,this.options,b);this.removeEvents();this.initEvents()};a.prototype.removeEvents=function(){this.eventsToUnbind.forEach(function(b){b()})};a.prototype.destroy=function(){this.removeEvents()};a.annotationsEditable={nestedOptions:{labelOptions:["style","format","backgroundColor"],labels:["style"],
label:["style"],style:["fontSize","color"],background:["fill","strokeWidth","stroke"],innerBackground:["fill","strokeWidth","stroke"],outerBackground:["fill","strokeWidth","stroke"],shapeOptions:["fill","strokeWidth","stroke"],shapes:["fill","strokeWidth","stroke"],line:["strokeWidth","stroke"],backgroundColors:[!0],connector:["fill","strokeWidth","stroke"],crosshairX:["strokeWidth","stroke"],crosshairY:["strokeWidth","stroke"]},circle:["shapes"],verticalLine:[],label:["labelOptions"],measure:["background",
"crosshairY","crosshairX"],fibonacci:[],tunnel:["background","line","height"],pitchfork:["innerBackground","outerBackground"],rect:["shapes"],crookedLine:[],basicAnnotation:["shapes","labelOptions"]};a.annotationsNonEditable={rectangle:["crosshairX","crosshairY","label"]};return a}();A.prototype.utils={updateRectSize:function(b,a){var c=a.chart,d=a.options.typeOptions,l=c.pointer.getCoordinates(b);b=l.xAxis[0].value-d.point.x;d=d.point.y-l.yAxis[0].value;a.update({typeOptions:{background:{width:c.inverted?
d:b,height:c.inverted?b:d}}})},getFieldType:function(b){return{string:"text",number:"number","boolean":"checkbox"}[typeof b]}};c.prototype.initNavigationBindings=function(){var b=this.options;b&&b.navigation&&b.navigation.bindings&&(this.navigationBindings=new A(this,b.navigation),this.navigationBindings.initEvents(),this.navigationBindings.initUpdate())};q(c,"load",function(){this.initNavigationBindings()});q(c,"destroy",function(){this.navigationBindings&&this.navigationBindings.destroy()});q(A,
"deselectButton",function(){this.selectedButtonElement=null});q(a,"remove",function(){this.chart.navigationBindings&&this.chart.navigationBindings.deselectAnnotation()});k.Annotation&&(u(a),b(a.types,function(b){u(b)}));h({lang:{navigation:{popup:{simpleShapes:"Simple shapes",lines:"Lines",circle:"Circle",rectangle:"Rectangle",label:"Label",shapeOptions:"Shape options",typeOptions:"Details",fill:"Fill",format:"Text",strokeWidth:"Line width",stroke:"Line color",title:"Title",name:"Name",labelOptions:"Label options",
labels:"Labels",backgroundColor:"Background color",backgroundColors:"Background colors",borderColor:"Border color",borderRadius:"Border radius",borderWidth:"Border width",style:"Style",padding:"Padding",fontSize:"Font size",color:"Color",height:"Height",shapes:"Shape options"}}},navigation:{bindingsClassName:"highcharts-bindings-container",bindings:{circleAnnotation:{className:"highcharts-circle-annotation",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;
return this.chart.addAnnotation(g({langKey:"circle",type:"basicAnnotation",shapes:[{type:"circle",point:{xAxis:0,yAxis:0,x:b.xAxis[0].value,y:b.yAxis[0].value},r:5}]},a.annotationsOptions,a.bindings.circleAnnotation.annotationsOptions))},steps:[function(b,a){var c=a.options.shapes[0].point,d=this.chart.xAxis[0].toPixels(c.x);c=this.chart.yAxis[0].toPixels(c.y);var l=this.chart.inverted;a.update({shapes:[{r:Math.max(Math.sqrt(Math.pow(l?c-b.chartX:d-b.chartX,2)+Math.pow(l?d-b.chartY:c-b.chartY,2)),
5)}]})}]},rectangleAnnotation:{className:"highcharts-rectangle-annotation",start:function(b){var a=this.chart.pointer.getCoordinates(b);b=this.chart.options.navigation;var c=a.xAxis[0].value;a=a.yAxis[0].value;return this.chart.addAnnotation(g({langKey:"rectangle",type:"basicAnnotation",shapes:[{type:"path",points:[{xAxis:0,yAxis:0,x:c,y:a},{xAxis:0,yAxis:0,x:c,y:a},{xAxis:0,yAxis:0,x:c,y:a},{xAxis:0,yAxis:0,x:c,y:a}]}]},b.annotationsOptions,b.bindings.rectangleAnnotation.annotationsOptions))},steps:[function(b,
a){var c=a.options.shapes[0].points,d=this.chart.pointer.getCoordinates(b);b=d.xAxis[0].value;d=d.yAxis[0].value;c[1].x=b;c[2].x=b;c[2].y=d;c[3].y=d;a.update({shapes:[{points:c}]})}]},labelAnnotation:{className:"highcharts-label-annotation",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;return this.chart.addAnnotation(g({langKey:"label",type:"basicAnnotation",labelOptions:{format:"{y:.2f}"},labels:[{point:{xAxis:0,yAxis:0,x:b.xAxis[0].value,y:b.yAxis[0].value},
overflow:"none",crop:!0}]},a.annotationsOptions,a.bindings.labelAnnotation.annotationsOptions))}}},events:{},annotationsOptions:{animation:{defer:0}}}});q(A,"closePopup",function(){this.deselectAnnotation()});return A});r(c,"Stock/StockToolsBindings.js",[c["Core/Globals.js"],c["Extensions/Annotations/NavigationBindings.js"],c["Core/Series/Series.js"],c["Core/Utilities.js"]],function(a,c,p,k){var h=k.correctFloat,r=k.defined,q=k.extend,d=k.fireEvent,f=k.getOptions,e=k.isNumber,n=k.merge,m=k.pick,u=
k.setOptions,t=k.uniqueKey,g=c.prototype.utils;g.addFlagFromForm=function(b){return function(a){var c=this,l=c.chart,e=l.stockTools,f=g.getFieldType;a=g.attractToPoint(a,l);var h={type:"flags",onSeries:a.series.id,shape:b,data:[{x:a.x,y:a.y}],point:{events:{click:function(){var b=this,a=b.options;d(c,"showPopup",{point:b,formType:"annotation-toolbar",options:{langKey:"flags",type:"flags",title:[a.title,f(a.title)],name:[a.name,f(a.name)]},onSubmit:function(a){"remove"===a.actionType?b.remove():b.update(c.fieldsToOptions(a.fields,
{}))}})}}}};e&&e.guiEnabled||l.addSeries(h);d(c,"showPopup",{formType:"flag",options:{langKey:"flags",type:"flags",title:["A",f("A")],name:["Flag A",f("Flag A")]},onSubmit:function(b){c.fieldsToOptions(b.fields,h.data[0]);l.addSeries(h)}})}};g.manageIndicators=function(b){var a=this.chart,c={linkedTo:b.linkedTo,type:b.type},e=["ad","cmf","mfi","vbp","vwap"],g="ad atr cci cmf macd mfi roc rsi ao aroon aroonoscillator trix apo dpo ppo natr williamsr stochastic slowstochastic linearRegression linearRegressionSlope linearRegressionIntercept linearRegressionAngle".split(" ");
if("edit"===b.actionType)this.fieldsToOptions(b.fields,c),(b=a.get(b.seriesId))&&b.update(c,!1);else if("remove"===b.actionType){if(b=a.get(b.seriesId)){var h=b.yAxis;b.linkedSeries&&b.linkedSeries.forEach(function(b){b.remove(!1)});b.remove(!1);0<=g.indexOf(b.type)&&(h.remove(!1),this.resizeYAxes())}}else{c.id=t();this.fieldsToOptions(b.fields,c);var k=a.get(c.linkedTo);var m=f().plotOptions;"undefined"!==typeof k&&k instanceof p&&"sum"===k.getDGApproximation()&&!r(m&&m[c.type]&&(null===(h=m.dataGrouping)||
void 0===h?void 0:h.approximation))&&(c.dataGrouping={approximation:"sum"});0<=g.indexOf(b.type)?(h=a.addAxis({id:t(),offset:0,opposite:!0,title:{text:""},tickPixelInterval:40,showLastLabel:!1,labels:{align:"left",y:-2}},!1,!1),c.yAxis=h.options.id,this.resizeYAxes()):c.yAxis=a.get(b.linkedTo).options.yAxis;0<=e.indexOf(b.type)&&(c.params.volumeSeriesID=a.series.filter(function(b){return"column"===b.options.type})[0].options.id);a.addSeries(c,!1)}d(this,"deselectButton",{button:this.selectedButtonElement});
a.redraw()};g.updateHeight=function(b,a){a.update({typeOptions:{height:this.chart.pointer.getCoordinates(b).yAxis[0].value-a.options.typeOptions.points[1].y}})};g.attractToPoint=function(b,a){b=a.pointer.getCoordinates(b);var c=b.xAxis[0].value;b=b.yAxis[0].value;var d=Number.MAX_VALUE,l;a.series.forEach(function(b){b.points.forEach(function(b){b&&d>Math.abs(b.x-c)&&(d=Math.abs(b.x-c),l=b)})});return{x:l.x,y:l.y,below:b<l.y,series:l.series,xAxis:l.series.xAxis.index||0,yAxis:l.series.yAxis.index||
0}};g.isNotNavigatorYAxis=function(b){return"highcharts-navigator-yaxis"!==b.userOptions.className};g.updateNthPoint=function(b){return function(a,c){var d=c.options.typeOptions;a=this.chart.pointer.getCoordinates(a);var l=a.xAxis[0].value,e=a.yAxis[0].value;d.points.forEach(function(a,c){c>=b&&(a.x=l,a.y=e)});c.update({typeOptions:{points:d.points}})}};q(c.prototype,{getYAxisPositions:function(b,a,c){function d(b){return r(b)&&!e(b)&&b.match("%")}var l=0;b=b.map(function(b){var f=d(b.options.height)?
parseFloat(b.options.height)/100:b.height/a;b=d(b.options.top)?parseFloat(b.options.top)/100:h(b.top-b.chart.plotTop)/a;e(f)||(f=c/100);l=h(l+f);return{height:100*f,top:100*b}});b.allAxesHeight=l;return b},getYAxisResizers:function(b){var a=[];b.forEach(function(c,d){c=b[d+1];a[d]=c?{enabled:!0,controlledAxis:{next:[m(c.options.id,c.options.index)]}}:{enabled:!1}});return a},resizeYAxes:function(b){b=b||20;var a=this.chart,c=a.yAxis.filter(g.isNotNavigatorYAxis),d=c.length;a=this.getYAxisPositions(c,
a.plotHeight,b);var e=this.getYAxisResizers(c),f=a.allAxesHeight,k=b;1<f?(6>d?(a[0].height=h(a[0].height-k),a=this.recalculateYAxisPositions(a,k)):(b=100/d,a=this.recalculateYAxisPositions(a,b/(d-1),!0,-1)),a[d-1]={top:h(100-b),height:b}):(k=100*h(1-f),5>d?(a[0].height=h(a[0].height+k),a=this.recalculateYAxisPositions(a,k)):a=this.recalculateYAxisPositions(a,k/d,!0,1));a.forEach(function(b,a){c[a].update({height:b.height+"%",top:b.top+"%",resize:e[a]},!1)})},recalculateYAxisPositions:function(b,a,
c,d){b.forEach(function(e,l){l=b[l-1];e.top=l?h(l.height+l.top):0;c&&(e.height=h(e.height+d*a))});return b}});k={segment:{className:"highcharts-segment",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"segment",type:"crookedLine",typeOptions:{points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.segment.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1)]},
arrowSegment:{className:"highcharts-arrow-segment",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"arrowSegment",type:"crookedLine",typeOptions:{line:{markerEnd:"arrow"},points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.arrowSegment.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1)]},ray:{className:"highcharts-ray",start:function(b){b=
this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"ray",type:"crookedLine",typeOptions:{type:"ray",points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.ray.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1)]},arrowRay:{className:"highcharts-arrow-ray",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"arrowRay",
type:"infinityLine",typeOptions:{type:"ray",line:{markerEnd:"arrow"},points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.arrowRay.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1)]},infinityLine:{className:"highcharts-infinity-line",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"infinityLine",type:"infinityLine",typeOptions:{type:"line",
points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.infinityLine.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1)]},arrowInfinityLine:{className:"highcharts-arrow-infinity-line",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"arrowInfinityLine",type:"infinityLine",typeOptions:{type:"line",line:{markerEnd:"arrow"},points:[{x:b.xAxis[0].value,
y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.arrowInfinityLine.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1)]},horizontalLine:{className:"highcharts-horizontal-line",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"horizontalLine",type:"infinityLine",draggable:"y",typeOptions:{type:"horizontalLine",points:[{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,
a.bindings.horizontalLine.annotationsOptions);this.chart.addAnnotation(b)}},verticalLine:{className:"highcharts-vertical-line",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"verticalLine",type:"infinityLine",draggable:"x",typeOptions:{type:"verticalLine",points:[{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.verticalLine.annotationsOptions);this.chart.addAnnotation(b)}},crooked3:{className:"highcharts-crooked3",
start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"crooked3",type:"crookedLine",typeOptions:{points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.crooked3.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1),g.updateNthPoint(2)]},crooked5:{className:"highcharts-crooked5",start:function(b){b=this.chart.pointer.getCoordinates(b);
var a=this.chart.options.navigation;b=n({langKey:"crookedLine",type:"crookedLine",typeOptions:{points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.crooked5.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1),g.updateNthPoint(2),g.updateNthPoint(3),g.updateNthPoint(4)]},elliott3:{className:"highcharts-elliott3",
start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"elliott3",type:"elliottWave",typeOptions:{points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.elliott3.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1),g.updateNthPoint(2),
g.updateNthPoint(3)]},elliott5:{className:"highcharts-elliott5",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"elliott5",type:"elliottWave",typeOptions:{points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]},labelOptions:{style:{color:"#666666"}}},
a.annotationsOptions,a.bindings.elliott5.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1),g.updateNthPoint(2),g.updateNthPoint(3),g.updateNthPoint(4),g.updateNthPoint(5)]},measureX:{className:"highcharts-measure-x",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"measure",type:"measure",typeOptions:{selectType:"x",point:{x:b.xAxis[0].value,y:b.yAxis[0].value,xAxis:0,yAxis:0},crosshairX:{strokeWidth:1,stroke:"#000000"},
crosshairY:{enabled:!1,strokeWidth:0,stroke:"#000000"},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"}},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.measureX.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateRectSize]},measureY:{className:"highcharts-measure-y",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"measure",type:"measure",typeOptions:{selectType:"y",point:{x:b.xAxis[0].value,
y:b.yAxis[0].value,xAxis:0,yAxis:0},crosshairX:{enabled:!1,strokeWidth:0,stroke:"#000000"},crosshairY:{strokeWidth:1,stroke:"#000000"},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"}},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.measureY.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateRectSize]},measureXY:{className:"highcharts-measure-xy",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"measure",
type:"measure",typeOptions:{selectType:"xy",point:{x:b.xAxis[0].value,y:b.yAxis[0].value,xAxis:0,yAxis:0},background:{width:0,height:0,strokeWidth:10},crosshairX:{strokeWidth:1,stroke:"#000000"},crosshairY:{strokeWidth:1,stroke:"#000000"}},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.measureXY.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateRectSize]},fibonacci:{className:"highcharts-fibonacci",start:function(b){b=this.chart.pointer.getCoordinates(b);
var a=this.chart.options.navigation;b=n({langKey:"fibonacci",type:"fibonacci",typeOptions:{points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.fibonacci.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1),g.updateHeight]},parallelChannel:{className:"highcharts-parallel-channel",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;
b=n({langKey:"parallelChannel",type:"tunnel",typeOptions:{points:[{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}]}},a.annotationsOptions,a.bindings.parallelChannel.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1),g.updateHeight]},pitchfork:{className:"highcharts-pitchfork",start:function(b){b=this.chart.pointer.getCoordinates(b);var a=this.chart.options.navigation;b=n({langKey:"pitchfork",type:"pitchfork",typeOptions:{points:[{x:b.xAxis[0].value,
y:b.yAxis[0].value,controlPoint:{style:{fill:"red"}}},{x:b.xAxis[0].value,y:b.yAxis[0].value},{x:b.xAxis[0].value,y:b.yAxis[0].value}],innerBackground:{fill:"rgba(100, 170, 255, 0.8)"}},shapeOptions:{strokeWidth:2}},a.annotationsOptions,a.bindings.pitchfork.annotationsOptions);return this.chart.addAnnotation(b)},steps:[g.updateNthPoint(1),g.updateNthPoint(2)]},verticalCounter:{className:"highcharts-vertical-counter",start:function(b){b=g.attractToPoint(b,this.chart);var a=this.chart.options.navigation,
c=r(this.verticalCounter)?this.verticalCounter:0;b=n({langKey:"verticalCounter",type:"verticalLine",typeOptions:{point:{x:b.x,y:b.y,xAxis:b.xAxis,yAxis:b.yAxis},label:{offset:b.below?40:-40,text:c.toString()}},labelOptions:{style:{color:"#666666",fontSize:"11px"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},a.annotationsOptions,a.bindings.verticalCounter.annotationsOptions);b=this.chart.addAnnotation(b);b.options.events.click.call(b,{})}},verticalLabel:{className:"highcharts-vertical-label",
start:function(b){b=g.attractToPoint(b,this.chart);var a=this.chart.options.navigation;b=n({langKey:"verticalLabel",type:"verticalLine",typeOptions:{point:{x:b.x,y:b.y,xAxis:b.xAxis,yAxis:b.yAxis},label:{offset:b.below?40:-40}},labelOptions:{style:{color:"#666666",fontSize:"11px"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},a.annotationsOptions,a.bindings.verticalLabel.annotationsOptions);b=this.chart.addAnnotation(b);b.options.events.click.call(b,{})}},verticalArrow:{className:"highcharts-vertical-arrow",
start:function(b){b=g.attractToPoint(b,this.chart);var a=this.chart.options.navigation;b=n({langKey:"verticalArrow",type:"verticalLine",typeOptions:{point:{x:b.x,y:b.y,xAxis:b.xAxis,yAxis:b.yAxis},label:{offset:b.below?40:-40,format:" "},connector:{fill:"none",stroke:b.below?"red":"green"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},a.annotationsOptions,a.bindings.verticalArrow.annotationsOptions);b=this.chart.addAnnotation(b);b.options.events.click.call(b,{})}},flagCirclepin:{className:"highcharts-flag-circlepin",
start:g.addFlagFromForm("circlepin")},flagDiamondpin:{className:"highcharts-flag-diamondpin",start:g.addFlagFromForm("flag")},flagSquarepin:{className:"highcharts-flag-squarepin",start:g.addFlagFromForm("squarepin")},flagSimplepin:{className:"highcharts-flag-simplepin",start:g.addFlagFromForm("nopin")},zoomX:{className:"highcharts-zoom-x",init:function(b){this.chart.update({chart:{zoomType:"x"}});d(this,"deselectButton",{button:b})}},zoomY:{className:"highcharts-zoom-y",init:function(b){this.chart.update({chart:{zoomType:"y"}});
d(this,"deselectButton",{button:b})}},zoomXY:{className:"highcharts-zoom-xy",init:function(b){this.chart.update({chart:{zoomType:"xy"}});d(this,"deselectButton",{button:b})}},seriesTypeLine:{className:"highcharts-series-type-line",init:function(b){this.chart.series[0].update({type:"line",useOhlcData:!0});d(this,"deselectButton",{button:b})}},seriesTypeOhlc:{className:"highcharts-series-type-ohlc",init:function(b){this.chart.series[0].update({type:"ohlc"});d(this,"deselectButton",{button:b})}},seriesTypeCandlestick:{className:"highcharts-series-type-candlestick",
init:function(b){this.chart.series[0].update({type:"candlestick"});d(this,"deselectButton",{button:b})}},fullScreen:{className:"highcharts-full-screen",init:function(b){this.chart.fullscreen.toggle();d(this,"deselectButton",{button:b})}},currentPriceIndicator:{className:"highcharts-current-price-indicator",init:function(b){var a=this.chart,c=a.series[0],e=c.options,f=e.lastVisiblePrice&&e.lastVisiblePrice.enabled;e=e.lastPrice&&e.lastPrice.enabled;a=a.stockTools;var g=a.getIconsURL();a&&a.guiEnabled&&
(b.firstChild.style["background-image"]=e?'url("'+g+'current-price-show.svg")':'url("'+g+'current-price-hide.svg")');c.update({lastPrice:{enabled:!e,color:"red"},lastVisiblePrice:{enabled:!f,label:{enabled:!0}}});d(this,"deselectButton",{button:b})}},indicators:{className:"highcharts-indicators",init:function(){var b=this;d(b,"showPopup",{formType:"indicators",options:{},onSubmit:function(a){b.utils.manageIndicators.call(b,a)}})}},toggleAnnotations:{className:"highcharts-toggle-annotations",init:function(b){var a=
this.chart,c=a.stockTools,e=c.getIconsURL();this.toggledAnnotations=!this.toggledAnnotations;(a.annotations||[]).forEach(function(b){b.setVisibility(!this.toggledAnnotations)},this);c&&c.guiEnabled&&(b.firstChild.style["background-image"]=this.toggledAnnotations?'url("'+e+'annotations-hidden.svg")':'url("'+e+'annotations-visible.svg")');d(this,"deselectButton",{button:b})}},saveChart:{className:"highcharts-save-chart",init:function(b){var c=this.chart,e=[],f=[],h=[],k=[];c.annotations.forEach(function(b,
a){e[a]=b.userOptions});c.series.forEach(function(b){b.is("sma")?f.push(b.userOptions):"flags"===b.type&&h.push(b.userOptions)});c.yAxis.forEach(function(b){g.isNotNavigatorYAxis(b)&&k.push(b.options)});a.win.localStorage.setItem("highcharts-chart",JSON.stringify({annotations:e,indicators:f,flags:h,yAxes:k}));d(this,"deselectButton",{button:b})}}};u({navigation:{bindings:k}});c.prototype.utils=n(g,c.prototype.utils)});r(c,"Stock/StockToolsGui.js",[c["Core/Chart/Chart.js"],c["Core/Globals.js"],c["Extensions/Annotations/NavigationBindings.js"],
c["Core/Utilities.js"]],function(a,c,p,k){var h=k.addEvent,r=k.createElement,q=k.css,d=k.extend,f=k.fireEvent,e=k.getStyle,n=k.isArray,m=k.merge,u=k.pick;k=k.setOptions;k({lang:{stockTools:{gui:{simpleShapes:"Simple shapes",lines:"Lines",crookedLines:"Crooked lines",measure:"Measure",advanced:"Advanced",toggleAnnotations:"Toggle annotations",verticalLabels:"Vertical labels",flags:"Flags",zoomChange:"Zoom change",typeChange:"Type change",saveChart:"Save chart",indicators:"Indicators",currentPriceIndicator:"Current Price Indicators",
zoomX:"Zoom X",zoomY:"Zoom Y",zoomXY:"Zooom XY",fullScreen:"Fullscreen",typeOHLC:"OHLC",typeLine:"Line",typeCandlestick:"Candlestick",circle:"Circle",label:"Label",rectangle:"Rectangle",flagCirclepin:"Flag circle",flagDiamondpin:"Flag diamond",flagSquarepin:"Flag square",flagSimplepin:"Flag simple",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",arrowLine:"Arrow line",horizontalLine:"Horizontal line",
verticalLine:"Vertical line",infinityLine:"Infinity line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",pitchfork:"Pitchfork",parallelChannel:"Parallel channel"}},navigation:{popup:{circle:"Circle",rectangle:"Rectangle",label:"Label",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",
arrowLine:"Arrow line",horizontalLine:"Horizontal line",verticalLine:"Vertical line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",pitchfork:"Pitchfork",parallelChannel:"Parallel channel",infinityLine:"Infinity line",measure:"Measure",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",flags:"Flags",addButton:"add",
saveButton:"save",editButton:"edit",removeButton:"remove",series:"Series",volume:"Volume",connector:"Connector",innerBackground:"Inner background",outerBackground:"Outer background",crosshairX:"Crosshair X",crosshairY:"Crosshair Y",tunnel:"Tunnel",background:"Background"}}},stockTools:{gui:{enabled:!0,className:"highcharts-bindings-wrapper",toolbarClassName:"stocktools-toolbar",buttons:"indicators separator simpleShapes lines crookedLines measure advanced toggleAnnotations separator verticalLabels flags separator zoomChange fullScreen typeChange separator currentPriceIndicator saveChart".split(" "),
definitions:{separator:{symbol:"separator.svg"},simpleShapes:{items:["label","circle","rectangle"],circle:{symbol:"circle.svg"},rectangle:{symbol:"rectangle.svg"},label:{symbol:"label.svg"}},flags:{items:["flagCirclepin","flagDiamondpin","flagSquarepin","flagSimplepin"],flagSimplepin:{symbol:"flag-basic.svg"},flagDiamondpin:{symbol:"flag-diamond.svg"},flagSquarepin:{symbol:"flag-trapeze.svg"},flagCirclepin:{symbol:"flag-elipse.svg"}},lines:{items:"segment arrowSegment ray arrowRay line arrowLine horizontalLine verticalLine".split(" "),
segment:{symbol:"segment.svg"},arrowSegment:{symbol:"arrow-segment.svg"},ray:{symbol:"ray.svg"},arrowRay:{symbol:"arrow-ray.svg"},line:{symbol:"line.svg"},arrowLine:{symbol:"arrow-line.svg"},verticalLine:{symbol:"vertical-line.svg"},horizontalLine:{symbol:"horizontal-line.svg"}},crookedLines:{items:["elliott3","elliott5","crooked3","crooked5"],crooked3:{symbol:"crooked-3.svg"},crooked5:{symbol:"crooked-5.svg"},elliott3:{symbol:"elliott-3.svg"},elliott5:{symbol:"elliott-5.svg"}},verticalLabels:{items:["verticalCounter",
"verticalLabel","verticalArrow"],verticalCounter:{symbol:"vertical-counter.svg"},verticalLabel:{symbol:"vertical-label.svg"},verticalArrow:{symbol:"vertical-arrow.svg"}},advanced:{items:["fibonacci","pitchfork","parallelChannel"],pitchfork:{symbol:"pitchfork.svg"},fibonacci:{symbol:"fibonacci.svg"},parallelChannel:{symbol:"parallel-channel.svg"}},measure:{items:["measureXY","measureX","measureY"],measureX:{symbol:"measure-x.svg"},measureY:{symbol:"measure-y.svg"},measureXY:{symbol:"measure-xy.svg"}},
toggleAnnotations:{symbol:"annotations-visible.svg"},currentPriceIndicator:{symbol:"current-price-show.svg"},indicators:{symbol:"indicators.svg"},zoomChange:{items:["zoomX","zoomY","zoomXY"],zoomX:{symbol:"zoom-x.svg"},zoomY:{symbol:"zoom-y.svg"},zoomXY:{symbol:"zoom-xy.svg"}},typeChange:{items:["typeOHLC","typeLine","typeCandlestick"],typeOHLC:{symbol:"series-ohlc.svg"},typeLine:{symbol:"series-line.svg"},typeCandlestick:{symbol:"series-candlestick.svg"}},fullScreen:{symbol:"fullscreen.svg"},saveChart:{symbol:"save-chart.svg"}}}}});
h(a,"afterGetContainer",function(){this.setStockTools()});h(a,"getMargins",function(){var a=this.stockTools&&this.stockTools.listWrapper;(a=a&&(a.startWidth+e(a,"padding-left")+e(a,"padding-right")||a.offsetWidth))&&a<this.plotWidth&&(this.plotLeft+=a,this.spacing[3]+=a)});["beforeRender","beforeRedraw"].forEach(function(c){h(a,c,function(){if(this.stockTools){var b=this.stockTools.listWrapper;b=b&&(b.startWidth+e(b,"padding-left")+e(b,"padding-right")||b.offsetWidth);var a=!1;b&&b<this.plotWidth?
(this.spacingBox.x+=b,a=!0):0===b&&(a=!0);b!==this.stockTools.prevOffsetWidth&&(this.stockTools.prevOffsetWidth=b,a&&(this.isDirtyLegend=!0))}})});h(a,"destroy",function(){this.stockTools&&this.stockTools.destroy()});h(a,"redraw",function(){this.stockTools&&this.stockTools.guiEnabled&&this.stockTools.redraw()});var t=function(){function a(a,c,d){this.wrapper=this.toolbar=this.submenu=this.showhideBtn=this.listWrapper=this.arrowWrapper=this.arrowUp=this.arrowDown=void 0;this.chart=d;this.options=a;
this.lang=c;this.iconsURL=this.getIconsURL();this.guiEnabled=a.enabled;this.visible=u(a.visible,!0);this.placed=u(a.placed,!1);this.eventsToUnbind=[];this.guiEnabled&&(this.createHTML(),this.init(),this.showHideNavigatorion());f(this,"afterInit")}a.prototype.init=function(){var a=this,c=this.lang,d=this.options,e=this.toolbar,f=a.addSubmenu,g=d.definitions,k=e.childNodes,m;d.buttons.forEach(function(b){m=a.addButton(e,g,b,c);a.eventsToUnbind.push(h(m.buttonWrapper,"click",function(){a.eraseActiveButtons(k,
m.buttonWrapper)}));n(g[b].items)&&f.call(a,m,g[b])})};a.prototype.addSubmenu=function(a,c){var b=this,d=a.submenuArrow,f=a.buttonWrapper,g=e(f,"width"),l=this.wrapper,k=this.listWrapper,m=this.toolbar.childNodes,n=0,p;this.submenu=p=r("ul",{className:"highcharts-submenu-wrapper"},null,f);this.addSubmenuItems(f,c);b.eventsToUnbind.push(h(d,"click",function(a){a.stopPropagation();b.eraseActiveButtons(m,f);0<=f.className.indexOf("highcharts-current")?(k.style.width=k.startWidth+"px",f.classList.remove("highcharts-current"),
p.style.display="none"):(p.style.display="block",n=p.offsetHeight-f.offsetHeight-3,p.offsetHeight+f.offsetTop>l.offsetHeight&&f.offsetTop>n||(n=0),q(p,{top:-n+"px",left:g+3+"px"}),f.className+=" highcharts-current",k.startWidth=l.offsetWidth,k.style.width=k.startWidth+e(k,"padding-left")+p.offsetWidth+3+"px")}))};a.prototype.addSubmenuItems=function(a,c){var b=this,d=this.submenu,e=this.lang,f=this.listWrapper,g;c.items.forEach(function(l){g=b.addButton(d,c,l,e);b.eventsToUnbind.push(h(g.mainButton,
"click",function(){b.switchSymbol(this,a,!0);f.style.width=f.startWidth+"px";d.style.display="none"}))});var l=d.querySelectorAll("li > .highcharts-menu-item-btn")[0];b.switchSymbol(l,!1)};a.prototype.eraseActiveButtons=function(a,c,d){[].forEach.call(a,function(a){a!==c&&(a.classList.remove("highcharts-current"),a.classList.remove("highcharts-active"),d=a.querySelectorAll(".highcharts-submenu-wrapper"),0<d.length&&(d[0].style.display="none"))})};a.prototype.addButton=function(b,c,d,e){void 0===e&&
(e={});c=c[d];var f=c.items,g=c.className||"";d=r("li",{className:u(a.prototype.classMapping[d],"")+" "+g,title:e[d]||d},null,b);b=r("span",{className:"highcharts-menu-item-btn"},null,d);if(f&&f.length){var l=r("span",{className:"highcharts-submenu-item-arrow highcharts-arrow-right"},null,d);l.style["background-image"]="url("+this.iconsURL+"arrow-bottom.svg)"}else b.style["background-image"]="url("+this.iconsURL+c.symbol+")";return{buttonWrapper:d,mainButton:b,submenuArrow:l}};a.prototype.addNavigation=
function(){var a=this.wrapper;this.arrowWrapper=r("div",{className:"highcharts-arrow-wrapper"});this.arrowUp=r("div",{className:"highcharts-arrow-up"},null,this.arrowWrapper);this.arrowUp.style["background-image"]="url("+this.iconsURL+"arrow-right.svg)";this.arrowDown=r("div",{className:"highcharts-arrow-down"},null,this.arrowWrapper);this.arrowDown.style["background-image"]="url("+this.iconsURL+"arrow-right.svg)";a.insertBefore(this.arrowWrapper,a.childNodes[0]);this.scrollButtons()};a.prototype.scrollButtons=
function(){var a=0,c=this.wrapper,d=this.toolbar,e=.1*c.offsetHeight;this.eventsToUnbind.push(h(this.arrowUp,"click",function(){0<a&&(a-=e,d.style["margin-top"]=-a+"px")}));this.eventsToUnbind.push(h(this.arrowDown,"click",function(){c.offsetHeight+a<=d.offsetHeight+e&&(a+=e,d.style["margin-top"]=-a+"px")}))};a.prototype.createHTML=function(){var a=this.chart,c=this.options,d=a.container;a=a.options.navigation;this.wrapper=a=r("div",{className:"highcharts-stocktools-wrapper "+c.className+" "+(a&&
a.bindingsClassName)});d.parentNode.insertBefore(a,d);this.toolbar=d=r("ul",{className:"highcharts-stocktools-toolbar "+c.toolbarClassName});this.listWrapper=c=r("div",{className:"highcharts-menu-wrapper"});a.insertBefore(c,a.childNodes[0]);c.insertBefore(d,c.childNodes[0]);this.showHideToolbar();this.addNavigation()};a.prototype.showHideNavigatorion=function(){this.visible&&this.toolbar.offsetHeight>this.wrapper.offsetHeight-50?this.arrowWrapper.style.display="block":(this.toolbar.style.marginTop=
"0px",this.arrowWrapper.style.display="none")};a.prototype.showHideToolbar=function(){var a=this.chart,c=this.wrapper,d=this.listWrapper,f=this.submenu,g=this.visible,k;this.showhideBtn=k=r("div",{className:"highcharts-toggle-toolbar highcharts-arrow-left"},null,c);k.style["background-image"]="url("+this.iconsURL+"arrow-right.svg)";g?(c.style.height="100%",k.style.top=e(d,"padding-top")+"px",k.style.left=c.offsetWidth+e(d,"padding-left")+"px"):(f&&(f.style.display="none"),k.style.left="0px",this.visible=
g=!1,d.classList.add("highcharts-hide"),k.classList.toggle("highcharts-arrow-right"),c.style.height=k.offsetHeight+"px");this.eventsToUnbind.push(h(k,"click",function(){a.update({stockTools:{gui:{visible:!g,placed:!0}}})}))};a.prototype.switchSymbol=function(a,c){var b=a.parentNode,d=b.classList.value;b=b.parentNode.parentNode;b.className="";d&&b.classList.add(d.trim());b.querySelectorAll(".highcharts-menu-item-btn")[0].style["background-image"]=a.style["background-image"];c&&this.selectButton(b)};
a.prototype.selectButton=function(a){0<=a.className.indexOf("highcharts-active")?a.classList.remove("highcharts-active"):a.classList.add("highcharts-active")};a.prototype.unselectAllButtons=function(a){var b=a.parentNode.querySelectorAll(".highcharts-active");[].forEach.call(b,function(b){b!==a&&b.classList.remove("highcharts-active")})};a.prototype.update=function(a){m(!0,this.chart.options.stockTools,a);this.destroy();this.chart.setStockTools(a);this.chart.navigationBindings&&this.chart.navigationBindings.update()};
a.prototype.destroy=function(){var a=this.wrapper,c=a&&a.parentNode;this.eventsToUnbind.forEach(function(a){a()});c&&c.removeChild(a);this.chart.isDirtyBox=!0;this.chart.redraw()};a.prototype.redraw=function(){this.showHideNavigatorion()};a.prototype.getIconsURL=function(){return this.chart.options.navigation.iconsURL||this.options.iconsURL||"https://code.highcharts.com/9.0.1/gfx/stock-icons/"};return a}();t.prototype.classMapping={circle:"highcharts-circle-annotation",rectangle:"highcharts-rectangle-annotation",
label:"highcharts-label-annotation",segment:"highcharts-segment",arrowSegment:"highcharts-arrow-segment",ray:"highcharts-ray",arrowRay:"highcharts-arrow-ray",line:"highcharts-infinity-line",arrowLine:"highcharts-arrow-infinity-line",verticalLine:"highcharts-vertical-line",horizontalLine:"highcharts-horizontal-line",crooked3:"highcharts-crooked3",crooked5:"highcharts-crooked5",elliott3:"highcharts-elliott3",elliott5:"highcharts-elliott5",pitchfork:"highcharts-pitchfork",fibonacci:"highcharts-fibonacci",
parallelChannel:"highcharts-parallel-channel",measureX:"highcharts-measure-x",measureY:"highcharts-measure-y",measureXY:"highcharts-measure-xy",verticalCounter:"highcharts-vertical-counter",verticalLabel:"highcharts-vertical-label",verticalArrow:"highcharts-vertical-arrow",currentPriceIndicator:"highcharts-current-price-indicator",indicators:"highcharts-indicators",flagCirclepin:"highcharts-flag-circlepin",flagDiamondpin:"highcharts-flag-diamondpin",flagSquarepin:"highcharts-flag-squarepin",flagSimplepin:"highcharts-flag-simplepin",
zoomX:"highcharts-zoom-x",zoomY:"highcharts-zoom-y",zoomXY:"highcharts-zoom-xy",typeLine:"highcharts-series-type-line",typeOHLC:"highcharts-series-type-ohlc",typeCandlestick:"highcharts-series-type-candlestick",fullScreen:"highcharts-full-screen",toggleAnnotations:"highcharts-toggle-annotations",saveChart:"highcharts-save-chart",separator:"highcharts-separator"};d(a.prototype,{setStockTools:function(a){var b=this.options,c=b.lang;a=m(b.stockTools&&b.stockTools.gui,a&&a.gui);this.stockTools=new t(a,
c.stockTools&&c.stockTools.gui,this);this.stockTools.guiEnabled&&(this.isDirtyBox=!0)}});h(p,"selectButton",function(a){var b=a.button,c=this.chart.stockTools;c&&c.guiEnabled&&(c.unselectAllButtons(a.button),0<=b.parentNode.className.indexOf("highcharts-submenu-wrapper")&&(b=b.parentNode.parentNode),c.selectButton(b))});h(p,"deselectButton",function(a){a=a.button;var b=this.chart.stockTools;b&&b.guiEnabled&&(0<=a.parentNode.className.indexOf("highcharts-submenu-wrapper")&&(a=a.parentNode.parentNode),
b.selectButton(a))});c.Toolbar=t;return c.Toolbar});r(c,"masters/modules/stock-tools.src.js",[],function(){})});
//# sourceMappingURL=stock-tools.js.map