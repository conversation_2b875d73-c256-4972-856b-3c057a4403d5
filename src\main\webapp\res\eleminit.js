/**
 * 
 */
 //-----------------------------------------------父界面传递的数据----------------------------------------------------------
var loc =encodeURI( location.href);//获取整个跳转地址内容，其实就是你传过来的整个地址字符串
	//var loc = "https://iwork.zx.zte.com.cn/iWork2ChartHandler2/index.jsp?taskid=1908&tem=1&jobname=0";
	console.log("我的地址"+loc);
	var n1 = loc.length;//地址的总长
	var n2 = loc.indexOf("?");//取得=号的位置
	var parameter = decodeURIComponent(decodeURI(loc.substr(n2+1, n1-n2)));//截取从?号后面的内容,也就是参数列表，因为传过来的路径是加了码的，所以要解码
	var parameters  = parameter.split("&");//从&处拆分，返回字符串数组
	var padata = new Array();//创建一个用于保存具体值得数组
	for (var i = 0; i < parameters.length; i++) {
		var m1 = parameters[i].length;//获得每个键值对的长度
		var m2 = parameters[i].indexOf("=");//获得每个键值对=号的位置
		var value = parameters[i].substr(m2+1, m1-m2).split(":")[0];//获取每个键值对=号后面具体的值
		var key=parameters[i].split("=")[0];
		padata[key] = value;
		console.log("参数值"+i+":"+value);
	}
	var kkkk = padata.tem;
	 document.getElementById("temchange").value =  padata.tem;
 //padata ={taskid: "1811", tem: "1", jobname: "中移TNR站内comp JR+JT测试"};
 
 var selcount = ""//当前构建的指标选择框
var selbeforenum = ""//历史构建版本选择框
  	   $.ajax({
	type: "post",
				   //url:'http://10.57.84.90:8080/iWorkDataHandlerAll/Iwork2TaskHandler',
//				   url:'http://10.57.84.90:8080/iWorkDataHandlerAll/Iwork2TaskHandler',
				   url:'indexzz.json',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					selcount = data[0];//指标
					selbeforenum = data[1];//历史构建
				}
});
const hisnum1 = [];
	for(let ihn=0;ihn<selbeforenum.length;ihn++){
		for (let value in selbeforenum[ihn]) {
			 console.log(selbeforenum[ihn][value]); // 输出：0, 1, 2
			 var hisnum2 = {name:"构建号_"+selbeforenum[ihn][value],value:selbeforenum[ihn][value]};
			 hisnum1.push(hisnum2);
			 
	}	
	 console.log(selbeforenum[ihn]);
	} 
//-------------------------------------------各组件初始化渲染-------------------------------------------------------------	
   layui.use('table', function(){
	var table = layui.table; 
	var dropdown = layui.dropdown;
//定义模板选择:	
	 dropdown.render({
    elem: '#taskmodel', 
    data: [{
      title: '中移模板',
      id: 100
    },{
      title: '电联模板',
      id: 101
    },{
      title: 'LTD_FDD',
      id: 103
    },
    {
      title: 'LTD_TDD',
      id: 104
    },{
      title: '自定义模板',
      id: 102
    }],
    click: function(obj){
	console.log(obj);
	this.elem.find('span').text(obj.title);
	if(obj.title=="中移模板"){
		kkkk = 1;
	}else if(obj.title=="电联模板"){
			kkkk = 2;
	}else if(obj.title=="LTE_FDD"){
			kkkk = 3;
	}else if(obj.title=="LTE_TDD"){
			kkkk = 4;
	}else if(obj.title=="自定义模板"){
			kkkk = 5;
	}		
	 document.getElementById("temchange").value =  kkkk
	 console.log(document.getElementById("temchange").value);
	$.ajax({
				   type: "get",
				   url:'selkpitemplate',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:kkkk},
				   dataType:'json',
				   success: function(data){
					xmSelect.get("#zztbnum",true).update({data:data});//基础指标
					xmSelect.get("#zztbnum2",true).update({data:data});//图表
					xmSelect.get("#helnum",true).update({data:data});//历史构建
					xmSelect.get("#zzbnum",true).update({data:data});//历史构建
					
					
				}
			});	
	}
});
//-------------------------------------------【TAB2】-------------------------------------------------------------	 
	xmSelect.render({
                 	el: '#helnum', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	on: function(data){
						var arr = data.arr;
						var arrnum = "";
						for(let ihn=0;ihn<arr.length;ihn++){
							console.log(arr[ihn].value);
							arrnum = arrnum + '-'+arr[ihn].value;
						}
						console.log(arrnum);
						$.ajax({
				   type: "get",
				    url:'hisgetcase',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:kkkk,index:arrnum,flag:"h"},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					htestcase.update({		//用例
				           		data:  data,	
				           	});					
				}
				   })
						},						
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},               	
                 	data: hisnum1
                 }) 
//-------测试用例[健康度]：
var htestcase=xmSelect.render({
                 	el: '#helcase', 
                 	language: 'zn',
                 	direction: 'down',
                 	max: 2,
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: []
                 });	
//-------健康度打分[表]：
table.render({
    elem: '#helscore'
    ,cols: [[
        {field:'version1',title: '版本[用例一]',width: '10%',align:'center'},
       {field:'version2',title: '版本[用例二]',width: '10%',align:'center'},
       {field:'usecasename1',title: '用例一',width: '13%',align:'center'},
       {field:'usecasename2',title: '用例二',width: '13%',align:'center'},
       {field:'task',title: '关联任务',width: '8%',align:'center'},
       {field:'imp',title: '提升项',width: '8%',align:'center'},
       {field:'flat',title: '持平项',width: '8%',align:'center'},
       {field:'worse',title: '恶化项',width: '8%',align:'center'},
       {field:'score',title: '性能KPI得分',width: '10%',align:'center'},
       {field:'con',title: '测试结论',align:'center'},
    ]],
    data:[],
    text:{
                                none:'请选择对比用例！'},
  });		
//-------健康度指标：	
	table.render({
    elem: '#helkpidata'
    ,toolbar: '#bar'
    ,cols: [[
       {field:'level',title: '类别',align:'center',width: '10%',sort:true}
      ,{field:'title', title: '指标',align:'center',width: '30%',}
      ,{field:'old', title: '用例一',align:'center',width: '10%',}
      ,{field:'new',title: '用例二',align:'center',width: '10%',}
      ,{field:'tempchazhi', title: '差值',align:'center',width: '10%',} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'tempfudu', title: '幅度',align:'center',width: '10%',}
      ,{field:'comparison', title: '描述',align:'center',width:'10%',sort:true,templet: function(d) {
																            if (d.comparison === '提升项') {
																              return '<span style="color: green;">' + d.comparison + '</span>';
																            } else if (d.comparison === '恶化项') {
																              return '<span style="color: red;">' + d.comparison + '</span>';
																            } else {
																              return d.comparison;
																            }
																          }}
       ,{field:'deduction', title: '扣分',align:'center',sort:true}
    ]],
   data:[],
   maxHeight : '400px',
   
   text:{
                                none:'请选择对比用例！'},
   }); 
//----图表渲染：
Highcharts.chart('healthhcharts', {
		chart: {
				zoomType: 'xy',
		},
		title: {
				text: '请选择对应构建号和相关用例',
				style:{
						fontSize:'18px'
					}
		},
		xAxis: [{
				categories: ["关键一级(A类)","关键一级(B类)","关键一级(C类)","关键一级","关键二级","关键三级"],
				crosshair: true,
				labels: {
					style:{
						fontSize:'20px'
					}
				}				
		}],
		yAxis: [
		{
				gridLineWidth: 0,
				title: {
						text: '',
				},
				labels: {
						format: '{value} ',
						style:{
						fontSize:'20px'
					}
				}		
		}
		],
		tooltip: {
				shared: true
		},
		legend: {
				layout: 'vertical',
				align: 'left',
				x: 80,
				verticalAlign: 'top',
				y: 55,
				floating: true,
				backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || '#FFFFFF'
		},
		plotOptions: {
        column: {
			maxPointWidth: 80,
            //stacking: 'normal',
            dataLabels: {
                enabled: true,
                color: (Highcharts.theme && Highcharts.theme.dataLabelsColor) || 'white',
                style: {
                    // 如果不需要数据标签阴影，可以尿 textOutline 设置丿 'none'
                    textOutline: '1px 1px black'
                }
            }
        }
    },
		series: [ 
		{
				name: '改善项',
				type: 'column',
				yAxis: 0,
				data:[],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#A6FFA6'
		},
		{
				name: '持平项',
				type: 'column',
				yAxis: 0,
				data:[],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: '#ACD6FF'
		},
		{
				name: '恶化项',
				type: 'column',
				yAxis: 0,
				data:[],
				marker: {
						enabled: false
				},
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: ' '
				},
				color: 'red'
		}],
});   
//-------------------------------------------【TAB3】-------------------------------------------------------------	
if((padata.tem=="0")||(padata.tem=="1")){//-------------中移模板
  console.log("aaa");
      table.render({
    elem: '#zztbasic'
    ,cols: [[
       {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',sort:true}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',sort:true,}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',sort:true,templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',sort:true}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',sort:true}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',sort:true}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',sort:true}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',sort:true}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',sort:true}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',sort:true}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',sort:true}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',sort:true}
        ,{field:'1594820269733', title: '小区上行平均MCS(Kpi)_1594820269733',width: '10%',sort:true}
        ,{field:'1594820269734', title: '小区下行平均MCS(Kpi)_1594820269734',width: '10%',sort:true}
     
    ]],
    text:{
                                none:'当前任务暂无指标数据！'},
    data:[],
     }); 
  }else if(padata.tem=="2"){//-------------电联
  console.log("bbb");
	    table.render({
    elem: '#zztbasic'
    ,cols: [[
      {field:'id',title: '用例别名',width: '8%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '12%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '12%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'667000', title: 'RRC连接建立成功率(%)_667000',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667000]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667000] + '</span>';
																            } else if (comparePercentages(''+d[667000]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667000] + '</span>';
																            } else {
																              return d[667000];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'667184', title: '无线接通率(%)_667184',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667184]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667184] + '</span>';
																            } else if (comparePercentages(''+d[667184]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667184] + '</span>';
																            } else {
																              return d[667184];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'1647503157118', title: 'Flow掉线率(%)_1647503157118',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[1647503157118] + '</span>';
																            } else if (comparePercentages(''+d[1647503157118]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[1647503157118] + '</span>';
																            } else {
																              return d[1647503157118];
																            }
																          }}
      ,{field:'1597370984303',title: 'RRC连接重建比率(%)_1597370984303',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'1597370984308', title: '小区上行平均MCS(Kpi)_1597370984308',width: '10%',}
        ,{field:'1597370984309', title: '小区下行平均MCS(Kpi)_1597370984309',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}else if(padata.tem=="5"){//自定义模板
		console.log("ccc");
		table.render({
    elem: '#zztbasic'
    ,cols: [[
      {field:'id',title: '用例别名',width: '5%',fixed:'left'}
       ,{field:'useCaseName',title: '用例名称',width: '20%',fixed:'left'}
       ,{field:'starttime',title: '起始时间',width: '10%',fixed:'left'}
        ,{field:'endtime',title: '结束时间',width: '10%',fixed:'left'}
     /*  ,{field:'gnbid', title: '基站id',width: '10%',}
       ,{field:'localcellid', title: '小区物理标识',width: '10%',}*/
      ,{field:'C600000007', title: 'RRC连接最大连接用户数_C600000007',width: '10%',colspan: 2}
      ,{field:'C600000008',title: 'RRC连接平均连接用户数_C600000008',width: '10%',}   
      ,{field:'664216', title: 'RRC连接建立成功率(%)_664216',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664216]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664216] + '</span>';
																            } else if (comparePercentages(''+d[664216]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664216] + '</span>';
																            } else {
																              return d[664216];
																            }
																          }}
      ,{field:'667014', title: 'QoS Flow建立成功率(%)_667014',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667014]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667014] + '</span>';
																            } else if (comparePercentages(''+d[667014]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667014] + '</span>';
																            } else {
																              return d[667014];
																            }
																          }} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'664249', title: '无线接通率(%)_664249',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[664249]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[664249] + '</span>';
																            } else if (comparePercentages(''+d[664249]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[664249] + '</span>';
																            } else {
																              return d[664249];
																            }
																          }}
      ,{field:'667188', title: 'gNB间切换成功率(%)_667188',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667188]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667188] + '</span>';
																            } else if (comparePercentages(''+d[667188]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667188] + '</span>';
																            } else {
																              return d[667188];
																            }
																          }}
      ,{field:'667189', title: 'gNB内切换成功率(%)_667189',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667189]+'','99.50%') === 'ok') {
																              return '<span style="color: green;">' + d[667189] + '</span>';
																            } else if (comparePercentages(''+d[667189]+'','99.50%') === 'no') {
																              return '<span style="color: red;">' +d[667189] + '</span>';
																            } else {
																              return d[667189];
																            }
																          }}
        ,{field:'667185', title: '无线掉线率(%)_667185',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667185]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667185] + '</span>';
																            } else if (comparePercentages(''+d[667185]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667185] + '</span>';
																            } else {
																              return d[667185];
																            }
																          }}
      ,{field:'667186', title: 'Flow掉线率(%)_667186',width: '10%',templet: function(d) {
																            if (comparePercentages(''+d[667186]+'','1.00%') === 'ok') {
																              return '<span style="color: red;">' + d[667186] + '</span>';
																            } else if (comparePercentages(''+d[667186]+'','1.00%') === 'no') {
																              return '<span style="color: green;">' +d[667186] + '</span>';
																            } else {
																              return d[667186];
																            }
																          }}
      ,{field:'669002',title: 'RRC连接重建比率(%)_669002',width: '10%',}
      ,{field:'669798', title: '小区上行UE Throughput(kbps)_669798',width: '10%',}
        ,{field:'669501', title: '小区下行UE Throughput(kbps)_669501',width: '10%',}
      ,{field:'670049', title: '上行每PRB平均吞吐量(Kb/PRB)_670049',width: '10%',}
      ,{field:'670050',title: '下行每PRB平均吞吐量(Kb/PRB)_670050',width: '10%',}
      ,{field:'669505', title: '小区上行RLC SDU平均时延(ms)_669505',width: '10%',}//minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'669503', title: '小区下行RLC SDU平均时延(ms)_669503',width: '10%',}
       ,{field:'670067', title: '小区上行PRB平均占用率(%)_670067',width: '10%',}
      ,{field:'670068',title: '小区下行PRB平均占用率(%)_670068',width: '10%',}
        ,{field:'4001511', title: '小区上行平均MCS(Kpi)_4001511',width: '10%',}
        ,{field:'4001512', title: '小区下行平均MCS(Kpi)_4001512',width: '10%',}
     
    ]],
    data:[],
   text:{
                                none:'当前任务暂无指标数据！'},
     }); 
	}
	//
//-------自主分析表基础分析的【构建号】：
xmSelect.render({
                 	el: '#zztbnum', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
					
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: hisnum1
                 });		
//-------自主分析表的【构建号】：
xmSelect.render({
                 	el: '#zztbnum2', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
					on: function(data){
						var arr = data.arr;
						var arrnum = "";
						for(let ihn=0;ihn<arr.length;ihn++){
							console.log(arr[ihn].value);
							arrnum = arrnum + '-'+arr[ihn].value;
						}
						console.log(arrnum);
						$.ajax({
				   type: "get",
				    url:'hisgetcase',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:kkkk,index:arrnum,flag:"h"},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					zztcase.update({		//用例
				           		data:  data,	
				           	});					
				}
				   })
						},	
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: hisnum1
                 });	
//-------自主分析表的【用例】：
 var zztcase = xmSelect.render({
                 	el: '#zztcase', 
                 	language: 'zn',
                 	direction: 'down',
                 	max: 2,
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: []
                 });	
//-------自主分析添加对比组的表格：
var width2 =Math.round(document.documentElement.clientWidth*0.9*0.618) ;
console.log(width2);
  table.render({
    elem: '#zztaddcase'
    ,maxHeight :'300px'
    ,width:''+width2+''
    ,cols: [[
       {field:'name',title: '组别',width: '10%',align:'center',type:'numbers'}
      ,{field:'case1',title: '用例一',width: '45%',align:'center'}
      ,{field:'case2',title: '用例二',width: '45%',align:'center'}
      ,{ field: 'wealth', width: 150, title: '操作', align: 'center',templet: function (item) {
                 return '<a style="margin:0px 5px; color:red; cursor: pointer;" lay-event="delete" id="DeleteTr" >删除</a>';
             }
         }
    ]],
    data:[],
    text:{
                                none:'请选择需要对比的组别'},
  }); 

//-------自主分析多健康度打分[表]：
table.render({
    elem: '#zzmtscore'
    ,cols: [[
        {field:'version1',title: '版本[用例一]',width: '10%',align:'center'},
       {field:'version2',title: '版本[用例二]',width: '10%',align:'center'},
       {field:'usecasename1',title: '用例一',width: '13%',align:'center'},
       {field:'usecasename2',title: '用例二',width: '13%',align:'center'},
       {field:'task',title: '关联任务',width: '8%',align:'center'},
       {field:'imp',title: '提升项',width: '8%',align:'center'},
       {field:'flat',title: '持平项',width: '8%',align:'center'},
       {field:'worse',title: '恶化项',width: '8%',align:'center'},
       {field:'score',title: '性能KPI得分',width: '10%',align:'center'},
       {field:'con',title: '测试结论',align:'center'},
    ]],
    data:[],
    text:{
                                none:'请选择对比用例！'},
  });		
//-------自主分析多健康度指标：	
	table.render({
    elem: '#zzmtdata'
    ,toolbar: '#bar2'
    ,cols: [[
       {field:'level',title: '类别',align:'center',width: '10%',sort:true}
      ,{field:'title', title: '指标',align:'center',width: '30%',}
      ,{field:'old', title: '用例一',align:'center',width: '10%',}
      ,{field:'new',title: '用例二',align:'center',width: '10%',}
      ,{field:'tempchazhi', title: '差值',align:'center',width: '10%',} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'tempfudu', title: '幅度',align:'center',width: '10%',}
      ,{field:'comparison', title: '描述',align:'center',width:'10%',sort:true,templet: function(d) {
																            if (d.comparison === '提升项') {
																              return '<span style="color: green;">' + d.comparison + '</span>';
																            } else if (d.comparison === '恶化项') {
																              return '<span style="color: red;">' + d.comparison + '</span>';
																            } else {
																              return d.comparison;
																            }
																          }}
       ,{field:'deduction', title: '扣分',align:'center',sort:true}
    ]],
   data:[],
   maxHeight : '400px',
   
   text:{
                                none:'请选择对比用例！'},
   });                                      
//-------------------------------------------【TAB4】-------------------------------------------------------------	
 	//-------自主分析【构建号】：
var zzcnum = xmSelect.render({
                 	el: '#zzbnum', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
					on: function(data){
						var arr = data.arr;
						var arrnum = "";
						for(let ihn=0;ihn<arr.length;ihn++){
							console.log(arr[ihn].value);
							arrnum = arrnum + '-'+arr[ihn].value;
						}
						console.log(arrnum);
						$.ajax({
				   type: "get",
				    url:'hisgetcase',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:kkkk,index:arrnum,flag:"h"},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					zhtestcase.update({		//构建号1	
				           		data:  data,	
				           	});
					
				}
				   })
						},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: hisnum1
                 });
  console.log(hisnum1[0].value);
  zzcnum.setValue([hisnum1[0].value]);
                
 	//-------自主分析【测试用例】：
var zhtestcase=xmSelect.render({
                 	el: '#zzcase', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
					on:function(data){
						var arr = data.arr;
						var arrnum = "";
						for(let ihn=0;ihn<arr.length;ihn++){
							console.log(arr[ihn].value);
							arrnum = arrnum + ','+arr[ihn].value;
						}
						console.log(arrnum);
						$.ajax({
				   type: "get",
				    url:'hisgetcell',
				   async:false,
				   data: {jobid:padata.taskid,kpitemplate:kkkk,tcase:encodeURI(arrnum)},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					htestcell.update({		//构建号1	
				           		data:  data,	
				           	});
					
				}
				   })
						},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: []
                 });
 	//-------自主分析【测试小区】：
var htestcell=xmSelect.render({
                 	el: '#zzcell', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: []
                 });                                
//自主分析图指标的渲染：
var zzcindex=xmSelect.render({
                 	el: '#zzbindex', 
                 	language: 'zn',
                 	direction: 'down',
                 	toolbar: {
						show: true,
					},
                 	size:'small',
                 	autoRow: true,
                	filterable: true,
                	theme: {
						color: '#008ed3',
					},
                	model: {
                		label: {
                			type: 'block',
                			block: {
                				//最大显示数量, 0:不限制
                				showCount: 2,
                				//是否显示删除图标
                				showIcon: true,
                			}
                		}
                	},
                	tree: {
                		show: true,
                		showFolderIcon: true,
                		showLine: true,
                		indent: 20,
                	},
                	
                 data: selcount
                 });   
  if(padata.tem=="1"){
	zzcindex.setValue(["C600000008","664216","670067","670068"])          
}else if(padata.tem=="2"){
	zzcindex.setValue(["C600000008","667000","670067","670068"])       
}               
               
//--------------------------------
  $.ajax({
				   type: "get",
				   //url:'https://iwork.zx.zte.com.cn/iWorkDataHandlerAll/QueryAutoDataAnalysisServlet',//20表+4个图
				   url:'http://10.57.84.90:8080/iWorkDataHandlerAll/QueryNewAutoDataAnalysisServlet',
				   //url:'chartsdata.json',
				   //async:false,
				   data: {jobid:padata.taskid,kpitemplate:padata.tem},
				    dataType:'json',
				   success: function(data){
					console.log(data);
					for(let icf=0;icf<data[0].length;icf++){
						console.log(data[0][icf].y[0].max);
							const nullPositions = data[0][icf].case.reduce((acc, curr, index) => {
							  if (curr === null) {
							    acc.push(index);
							  }
							  return acc;
							}, []);
							nullPositions.unshift(0)	;
							var listband = [];
							for(let icb=0;icb<nullPositions.length-1;icb++){
									var cfband={from:nullPositions[icb],to:nullPositions[icb+1],color:'white',borderWidth:1,borderColor:'rgba(68, 170, 213, 0.1)',label:{text:''+data[0][icf].case[nullPositions[icb+1]-1]+''}}
									listband.push(cfband);
							}		  
						console.log(nullPositions);				
						//动态增加图	
						//渲染增加的图：
						Highcharts.chart('chart'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+data[0][icf].title+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [{
								categories: data[0][icf].x,
								labels: {
									  formatter: function() {
										if(Number.isInteger(this.value)){
												this.value = ""; 
										}else{
											
										}
				           return this.value
				        },
								rotation:90,
					         style :{
						fontSize: 8
						}
								},
						 plotLines:listband,
						 lineColor:'black'		
						}],
						yAxis: [{ // Primary yAxis
								max:data[0][icf].y[0].max,
								min:data[0][icf].y[0].min,
								tickInterval:data[0][icf].y[0].tickInterval,
								labels: {
										format: '{value}',
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								},
								title: {
										text: data[0][icf].y[0].label,
										style: {
												color: Highcharts.getOptions().colors[0]
										}
								}
								
						}
							],
						tooltip: {
						},
						series:data[0][icf].series,
				
				});	
					}
				}
				}) 

//////--------
	})