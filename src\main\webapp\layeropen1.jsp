<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
<title>指标选择</title>
 <link href="res/css/layui.css" type="text/css" rel="stylesheet">
<style>
        body {
            background-color: rgba(255, 255, 255, 0.5); 
        },
        xm-select > .xm-body .scroll-body {
    max-width: 100px;
};
    </style>
</head>

<body >
	<form class="layui-form " style="margin-top:1%">
        					<div class="layui-form-item " style="width:80%">
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">指标选择</label>
                                    <div class="layui-input-inline layui-col-md12">
				                      <div id="countnew"></div>
				                  </div>
				                  <button   lay-submit lay-filter="indexnew" style="margin-left:1%" class="layui-btn layui-bg-blue">对比</button>  
		                       </div>
		                        
		                       </form>  
 <script type="text/javascript" src="res/jquery.min.js"></script>
<script type="text/javascript" src="res/layui.js"></script>
<script type="text/javascript" src="res/xm-select/xm-select.js"></script>

<script > 
$.ajax({
	type: "post",
				   //url:'https://iwork.zx.zte.com.cn/iWorkDataHandlerAll/Iwork2TaskHandler',
				   url:'indexzz.json',
				   async:false,
				   data: {},
				    dataType:'json',
				   success: function(data){
					   selcount = data[0];
					   xmSelect.render({
		                 	el: '#countnew', 
		                 	language: 'zn',
		                 	direction: 'down',
		                 	toolbar: {
								show: true,
							},
		                 	theme: {
								color: '#008ed3',
							},
		                 	autoRow: true,
		                	filterable: true,
		                	model: {
		                		label: {
		                			type: 'block',
		                			block: {
		                				//最大显示数量, 0:不限制
		                				showCount: 2,
		                				//是否显示删除图标
		                				showIcon: true,
		                			}
		                		}
		                	},
		                	tree: {
		                		show: true,
		                		showFolderIcon: true,
		                		showLine: true,
		                		indent: 20,
		                	},
		                	
		                 data: selcount
		                 }); 
				}
}); 
	</script>
</body>
</html>