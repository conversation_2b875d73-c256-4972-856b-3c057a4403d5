// Colors for buttons.
$button-background-color: #f7f7f7;
$button-hover-color: #e6ebf5;


.highcharts-popup.highcharts-annotation-toolbar {
  right: 10%;
  left: auto;
  height: 40px;
  overflow: hidden;
  padding-right: 40px;
  width: auto;
  min-width: 0;
}

.highcharts-popup.highcharts-annotation-toolbar button {
  margin-top:0px;
}

.highcharts-popup.highcharts-annotation-toolbar > span {
  display:block;
  float:left;
  padding: 12px;
}


.highcharts-popup {
  background-color: #fff;
  color: #666;
  display: none;
  font-size: 0.876em;
  max-height: 90%;
  top: 5%;
  left: 15%;
  overflow-x: hidden;
  overflow-y: auto;
  width: 75%;
  min-width: 300px;
  max-width: 600px;
  position: absolute;
  z-index: 100;
  -webkit-box-shadow: 0px 0px 8px 0px rgba(61,61,61,0.3);
  -moz-box-shadow: 0px 0px 8px 0px rgba(61,61,61,0.3);
  box-shadow: 0px 0px 8px 0px rgba(61,61,61,0.3);
}

.highcharts-popup div, .highcharts-popup span {
  box-sizing: content-box;
}

.highcharts-popup input, .highcharts-popup label, .highcharts-popup select {
  clear: both;
  float: left;
  width: 100%;
  margin-bottom: 10px;
}

.highcharts-popup input {
  border: 1px solid #e6e6e6;
  padding: 5px;
  width: calc(100% - 12px);
}

.highcharts-popup-lhs-col, .highcharts-popup-rhs-col {
  padding: 20px;
  height: calc(100% - 84px); /* 44px - padding, 40px - tabs*/
  float: left;
}

.highcharts-popup-lhs-col.highcharts-popup-lhs-full { 
  width: calc(100% - 52px);
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100% - 140px);
  border: none;
  padding: 0px 20px 20px 20px;
}

.highcharts-popup-lhs-col.highcharts-popup-lhs-full + .highcharts-popup-bottom-row {
  width: calc(100% - 32px);
}

.highcharts-popup-lhs-col {
  clear: both;
  width: calc(30% - 44px);
  border-right: 1px solid #e6e6e6;
}

.highcharts-popup-bottom-row {
  float: left;
  padding: 0px 20px;
  width: calc(100% - 40px);
}

.highcharts-popup-rhs-col {
  width: calc(70% - 40px);
}

.highcharts-popup-rhs-col-wrapper {
  float: left;
  width: calc(100% - 20px);
  padding-right: 20px;
  height: calc(100% - 40px);
  overflow-y: auto;
  overflow-x: hidden;
}

.highcharts-popup-rhs-col-wrapper h3 {
  margin-top:0px;
  padding-bottom:0px;
}

.highcharts-bindings-wrapper ul.highcharts-indicator-list,
.highcharts-indicator-list {
  float: left;
  color: #666;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 0px;
  padding: 0px;
}

.highcharts-indicator-list li {
  cursor: pointer;
  padding: 0px 0px 5px 0px;
  margin: 0px;
  width: 100%;
  height: auto;
  overflow: hidden;
  word-break: break-all;
}

.highcharts-indicator-list li:hover {
  background-color: $button-hover-color;
}

.highcharts-tab-item {
  background-color: $button-background-color;
  cursor: pointer;
  display: block;
  float:left;
  padding: 10px;
  height: 20px;
}

.highcharts-tab-item.highcharts-tab-item-active {
  background-color: $button-hover-color;
}

.highcharts-tab-item-content {
  display: none; 
  float: left;
  height: 100%;
  overflow: hidden;
  width: 100%;
  border-top: 1px solid #e6e6e6;
}

.highcharts-tab-item-show {
  display: block;
}

.highcharts-popup-close {
  background-repeat: no-repeat;
  background-position: 50% 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  position: absolute;
  padding: 10px;
  top: 0%;
  right: 0%;
  color: #333333;
}

.highcharts-popup-close:hover,
.highcharts-popup button:hover,
.highcharts-popup button.highcharts-annotation-edit-button:hover,
.highcharts-popup button.highcharts-annotation-remove-button:hover {
  background-color: $button-hover-color;
}

.highcharts-popup button {
  float: right;
  border: none;
  background: $button-background-color;
  color: #666;
  margin-left:5px;
  margin-top:12px;
}
.highcharts-popup button:first-child {
  margin-left: 0;
}

.highcharts-tab-disabled {
  color: #ccc;
}

/* annotation edit small popup */
.highcharts-popup button.highcharts-annotation-edit-button,
.highcharts-popup button.highcharts-annotation-remove-button {
  width: 20px;
  height: 40px;
  padding: 20px;
}

.highcharts-popup button.highcharts-annotation-edit-button {
  background-repeat: no-repeat;
  background-position: 50% 50%;
  text-indent: -9999px;
}

.highcharts-popup button.highcharts-annotation-remove-button {
  background-repeat: no-repeat;
  background-position: 50% 50%;
  text-indent: -9999px;
}

.highcharts-popup .highcharts-annotation-title {
  display: block;
  float: left;
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 15px;
  width: 100%;
}

.highcharts-popup .highcharts-popup-main-title {
  border-bottom: 1px solid #e6e6e6;
  margin: 0px 0px 20px 0px;
  padding: 8px 0px 6px 20px;
}

.highcharts-indicator-title {
  float: left;
  padding-bottom: 15px;
}