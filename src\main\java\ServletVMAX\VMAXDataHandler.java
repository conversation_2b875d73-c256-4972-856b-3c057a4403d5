package ServletVMAX;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import Dao.DaoCMCC;
import utilvmax.vmax_function;



/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/VMAXDataHandler")
public class VMAXDataHandler extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public VMAXDataHandler() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		String usecase = request.getParameter("usecase");
		usecase = java.net.URLDecoder.decode(usecase,"utf-8");
		String jobid = request.getParameter("jobid");
		
//	usecase = "3#10658#1#1#1#COMP-内场化测试用例-Test,3#10658#1#1#2#COMP-内场化测试用例-Test,3#10658#1#1#2#COMP-内场化测试用例-Test,"
//			+ "3#10658#2#1#2#COMP-内场化测试用例-Test";
//		usecase="74#310938#42#1#3CI冒烟-310938-42-C800,72#310938#42#1#3CI冒烟-310938-42-C800";
		String[] param1 = usecase.split(",");
		
//		System.out.println("usecse:"+usecase);
//		System.out.println("jobid:"+jobid);
//		jobid = "1778";
		DaoCMCC dao=new DaoCMCC();
//		kpitemplate = "5";
//		String jobid = "1778";
		
//		String[] param1 = "8#10658#1#1#1#【FRT深圳宏站】【内场】中移TNR站内CoMP JR测试,8#10658#1#1#1#【FRT深圳宏站】【内场】中移TNR站内CoMP JR测试".split(",");
						
//		String jobid = "1818";
//		String[] param1 = "4#310937#61#1#1#310937_VBPd01_A9631E S35_1级CI冒烟,4#310937#31#1#2#310937_VBPd21_A9631A S35_1级CI冒烟".split(",");				
		
		vmax_function fun=new vmax_function();

		JSONArray usecasesArray = new JSONArray();
		for(int i=0;i<param1.length;i++) {
			JSONArray tempusecseArray = new JSONArray();
			String[] tempStrings = param1[i].split("#");
			String 	build1 = tempStrings[0];
			String	gnbid1 = tempStrings[1];
			String	celllocalid1 = tempStrings[2];
			String	usecaseid1 = tempStrings[3]; 
			String  useCaseName1 = "";
			if(tempStrings.length>4) {
				useCaseName1 = tempStrings[4];
			}
			tempusecseArray.put(build1).put(gnbid1).put(celllocalid1).put(usecaseid1).put(useCaseName1);
			usecasesArray.put(tempusecseArray);
		}       
		
		JSONArray dataArray= new JSONArray();
		JSONArray versionArray = new JSONArray();
		JSONArray tempusecaseArray = new JSONArray();

		for(int i=0;i<usecasesArray.length();i++) {
			JSONArray tempusecseArray = usecasesArray.getJSONArray(i);
			String buildno = tempusecseArray.getString(0);
			String gnbid = tempusecseArray.getString(1);
			String celllocalid = tempusecseArray.getString(2);
			String usecaseid = tempusecseArray.getString(3);
			String useCaseName = tempusecseArray.getString(4);

			String datatable = "vmaxdata";
			String sql = "select * from "+datatable+" where jobid ='"+jobid+"' and usecaseid ='"+usecaseid+"' and"
					+ " BUILD_NUMBER='"+buildno+"'  and"
					+ "	gnbid = '"+gnbid+"' and celllocalid = '"+celllocalid+"';";
			JSONArray dou1= fun.vmaxset(sql);
			ResultSet rs = dao.executeQuery(sql);
			String version = "";
			try {
				if(rs.next()) {
					version = rs.getString(66);
				}
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			versionArray.put(version);
			tempusecaseArray.put(useCaseName);
			dataArray.put(dou1);
		}
	
		
		JSONArray listkpi = new JSONArray();
		JSONArray listscore = new JSONArray();
		if(dataArray.length()>=2) {
			listkpi =  fun.show(dataArray.getJSONArray(0), dataArray.getJSONArray(1));
			listscore =  fun.score(dataArray.getJSONArray(0), dataArray.getJSONArray(1),versionArray.getString(0),versionArray.getString(1),tempusecaseArray.getString(0),tempusecaseArray.getString(1),jobid);

		}

		JSONArray resultArray = new JSONArray();
		
		resultArray.put(listkpi).put(listscore);
		
		dao.close();	    

		PrintWriter out = response.getWriter(); 
		out.print(resultArray);
		out.flush();
		out.close();
	

	}




	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
	
	private int Lookcolumn(String tablename) {
		// TODO Auto-generated method stub
		DaoCMCC dao = new DaoCMCC();
		int countcol=0;
		String sql ="select count(*) \r\n"
				+ "from information_schema.COLUMNS \r\n"
				+ "where TABLE_SCHEMA='iwork2' and table_name='"+tablename+"'";
		ResultSet rs = dao.executeQuery(sql);
		try {
			while(rs.next()) {
				countcol = rs.getInt(1);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dao.close();
		return countcol;
	}
	
	private JSONArray Lookcolumnname(String tablename) {
		// TODO Auto-generated method stub
		DaoCMCC dao = new DaoCMCC();
		JSONArray colnameArray = new JSONArray();
		String sql = "SELECT COLUMN_NAME FROM information_schema.COLUMNS\r\n"
		+ "WHERE TABLE_SCHEMA = 'iwork2' AND TABLE_NAME = '"+tablename+"' ORDER BY ordinal_position;";
		ResultSet rs = dao.executeQuery(sql);
		try {
			while(rs.next()) {
				colnameArray.put(rs.getString(1));
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dao.close();
		return colnameArray;
	}
	
    private static void zipDirectory(File directory, String basePath, ZipOutputStream zos) throws Exception {
        File[] files = directory.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                zipDirectory(file,  file.getName(), zos);
            } else {
                byte[] buffer = new byte[1024];
                FileInputStream fis = new FileInputStream(file);
                zos.putNextEntry(new ZipEntry( file.getName()));
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, length);
                }
                zos.closeEntry();
                fis.close();
            }
        }
    }
}




