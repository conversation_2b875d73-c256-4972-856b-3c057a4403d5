 body { width: 90%; margin: 8px auto 0 auto; font-family: Arial, Helvetica; font-size: small; background-color: #F8F8FF;  }

  /* ------------------------------------------------- */

  #tabs {
    overflow: hidden;
    width: 100%;
    margin: -5px 0 0 0;
    padding: 0;
    list-style: none;
    margin-top: 20px;
  }

  #tabs li {
    float: left;
    margin: 0 -15px 0 0;
  }

  #tabs a {
    float: left;
    position: relative;
    padding: 0 40px;
    height: 0;
    line-height: 32px;
    text-transform: uppercase;
    text-decoration: none;
    font-size: 20px;
    color: #fff;      
    border-right: 32px solid transparent;
    border-bottom: 32px solid #008ED3;
  /*   border-bottom-color: #7779; */
    opacity:0.3;
    filter:alpha(opacity=30);      
  }

  #tabs a:hover,
  #tabs a:focus {
    border-bottom-color: #2ac7e1;
    opacity: 1;
    filter: alpha(opacity=100);
  }

  #tabs a:focus {
    outline: 0;
  }

  #tabs #current {
    z-index: 3;
    border-bottom-color: #008ED3;
    opacity: 1;
    filter: alpha(opacity=100);      
  }

  /* ----------- */
  #content {
      background: #F8F8FF;
      border-top:2px solid #008ED3;
     /*  padding: 2em; */
      /*height: 220px;*/
  }

  #content h2,
    #content h3,
    #content p {
      margin: 0 0 15px 0;
  }  

  /* Demo page only */
  #about {
      color: #999;
      text-align: center;
      font: 0.9em Arial, Helvetica;
  }

  #about a {
      color: #777;
  }   