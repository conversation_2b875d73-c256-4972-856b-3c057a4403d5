package Servlet;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;

import org.json.JSONObject;

@ServerEndpoint("/websocket")
public class webso {
    private static final Set<Session> sessions = new HashSet<>();

    @OnOpen
    public void onOpen(Session session) {
    	System.out.println("123");
    	System.out.println(session);
        sessions.add(session);
    }

    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
    	System.out.println(message);
    	System.out.println(session);
    	   for(int iold=0;iold<100;iold++) {
    		if(iold%2==0){
    			 JSONObject objold1 = new JSONObject();
    			 objold1.put("status",true);
    				objold1.put("msg","Processing completed:61");
    				objold1.put("file","Result_20240309022938086439.zip");
    				objold1.put("progress","10%");
    			session.getBasicRemote().sendText(objold1.toString());
    		}
    	}
		/*
		 * for (Session s : sessions) { s.getBasicRemote().sendText("aaa"); }
		 */
    }

    @OnClose
    public void onClose(Session session) {
        sessions.remove(session);
    }
}