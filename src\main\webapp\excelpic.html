<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Insert title here</title>
 	<meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <link href="res/layui-v2.9.2/layui/css/layui.css" rel="stylesheet" />
   <style type="text/css">
   .container ul{
   width: 90%;
   padding: 15px;
   min-height:300px;
   max-height:300px;
   overflow:auto;
   background-color:#FFFFF0;
   display: inline-block;
   border-radius: 5px;
   border: 1px solid #C6C8CA;
 }
 .container ul li{
  display: block;
  float: left;
  width: 100%;
  height: 35px;
  line-height: 35px;
  border-radius: 4px;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color:yellow;
  margin-bottom:10px;
  -moz-user-select: none;
  user-select: none;
  text-indent: 10px;
  color: #555;
}
.layui-col-md-custom {
    width: 24% !important; /* 设置你想要的宽度 */
  }

   
   </style>
</head>
<body>
		<div class="layui-row "  style="margin-left:1%;margin-top:1%;padding:1%" >
				<div class=" layui-panel layui-col-md12 layui-col-md-custom3" >
       				<form class="layui-form" style="margin-top:1%;padding:0.2%">
        					<div class="layui-form-item " >
		        				<label class="layui-form-label" style="margin-left:-1.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline " style="width:20%">
				                      <div id="helnum"></div>
				                  </div> 
				                  <button   lay-submit lay-filter="comp" style="margin-left:1%" class="layui-btn layui-btn-sm layui-bg-blue">对比</button>
				                  <button   lay-submit lay-filter="openfile" style="margin-left:50%" class="layui-btn layui-btn-sm layui-bg-blue">打开配置</button>  
				                  <button   lay-submit lay-filter="savefile" style="margin-left:5%" class="layui-btn layui-btn-sm layui-bg-blue">保存配置</button>    
		                       </div>
		                       </form> 
       			</div>
		</div>
       			
 <div class="layui-row container"  style="margin-top:-1.5%;padding:1%" id="aaab">
    <div class=" layui-panel layui-col-md3 layui-col-md-custom"  style="margin-left:1%">
      <div class="layui-card">
        <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">字段列表</div>
        <div class="layui-card-body">
        	<ul id="filed1" >
	</ul>
        </div>
      </div>
    </div>
    <div class=" layui-panel layui-col-md3 layui-col-md-custom"  style="margin-left:1%">
      <div class="layui-card">
        <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">筛选器</div>
        <div class="layui-card-body">
        	<ul id="filed2">
        	
	</ul>
        </div>
      </div>
    </div>
    <div class=" layui-panel layui-col-md3 layui-col-md-custom"  style="margin-left:1%">
      <div class="layui-card">
        <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">系列(图例)</div>
        <div class="layui-card-body">
        	<ul id="filed3">
	</ul>
        </div>
      </div>
    </div>
    <div class=" layui-panel layui-col-md3 layui-col-md-custom"  style="margin-left:1%">
      <div class="layui-card">
        <div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">X轴</div>
        <div class="layui-card-body">
        	<ul id="filed4">
	</ul>
        </div>
      </div>
    </div>
    </div>
<div class="layui-row "  style="margin-left:1%;margin-top:-1.5%;padding:1%" >
				<div class=" layui-panel layui-col-md12 layui-col-md-custom3" >
       				<form class="layui-form" style="margin-top:1%;padding:0.2%">
        					<div class="layui-form-item " >
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">值(Y主轴)</label>
                                    <div class="layui-input-inline " style="width:15%">
				                      <select>
										      <option value="">汇总方式</option>
										      <option value="count">求和</option>
										      <option value="number">计数</option>
										      <option value="avg">平均值</option>
										       <option value="max">最大值</option>
										        <option value="min">最小值</option>
										    </select>
				                  </div> 
				                  <div class="layui-input-inline " style="width:15%">
				                      <select>
										      <option value="">图表类型</option>
										      <option value="1">簇状柱状图</option>
										      <option value="2">折线图</option>
										      <option value="3">带标记折线图</option>
										       <option value="4">散点图</option>
										        <option value="5">带拟合散点图</option>
										    </select>
				                  </div> 
				                  <label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">值(Y次轴)</label>
                                    <div class="layui-input-inline " style="width:15%">
				                      <select>
										      <option value="">汇总方式</option>
										      <option value="count">求和</option>
										      <option value="number">计数</option>
										      <option value="avg">平均值</option>
										       <option value="max">最大值</option>
										        <option value="min">最小值</option>
										    </select>
				                  </div> 
				                  <div class="layui-input-inline " style="width:15%">
				                      <select>
										      <option value="">图表类型</option>
										      <option value="1">簇状柱状图</option>
										      <option value="2">折线图</option>
										      <option value="3">带标记折线图</option>
										       <option value="4">散点图</option>
										        <option value="5">带拟合散点图</option>
										    </select>
				                  </div> 
				                  <button   lay-submit lay-filter="runrun" style="margin-left:1%" class="layui-btn layui-btn-sm layui-bg-blue">运行</button>
		                       </div>
		                       </form> 
       			</div>
		</div>
<script type="text/javascript" src="res/jquery.min.js"></script>
 <script type="text/javascript" src="res/layui.js"></script> 
 <script type="text/javascript" src="res/xm-select/treeTable.js"></script>
<script type="text/javascript" src="res/xm-select/xm-select.js"></script>
<script type="text/javascript" src="res/excelpic.js"></script>
<script type="text/javascript">
//假设我们有一个HTML元素
		var htmlElement = document.getElementById("aaab").innerHTML;
		console.log(htmlElement); 
		// 将HTML元素转换为字符串
		var htmlString = htmlElement.toString();
		
		// 创建一个JSON对象
		var jsonData = {
		    html: htmlString
		};
		
		// 使用JSON.stringify()方法将JSON对象转换为字符串
		var jsonString = JSON.stringify(jsonData);
		
		console.log(jsonString); 
		
	    function addtk2() {
	    	var htmlElement = document.getElementById("aaab").innerHTML;
			console.log(htmlElement); 
	    	
	    }
</script>

</body>
</html>