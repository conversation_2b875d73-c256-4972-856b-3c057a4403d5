package Dao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;


/**
 Title:Dao
 Description:TODO
 Auther:钖涢敓鏂ゆ嫹10170365
 Date:2017-10-17閿熸枻鎷烽敓鏂ゆ嫹10:50:40
 */
public class DaoDateSet {
	
	protected static String Username="5gtest4CI";
	protected static String Password="iWork4test!@#";
	protected static String url="jdbc:mysql://*************:3306/5520dataset?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=UTC&autoReconnect=true&failOverReadOnly=false&autoReconnect=true";
	
	protected static String className="com.mysql.cj.jdbc.Driver";
	public static  Connection conn=null;
	/**
	  閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷�
	 Description:閿熸磥鏋勯敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷疯彉閿燂拷
	 Auther:钖涢敓鏂ゆ嫹10170365
	 Date:2017-10-17閿熸枻鎷烽敓鏂ゆ嫹10:41:30
	 */
	public DaoDateSet() {
		try {
			Class.forName(className);
			//System.out.println(new Date()+" mysql閿熸枻鎷稪DBC閿熸枻鎷烽敓鏂ゆ嫹閿熸埅鎴愮櫢鎷烽敓鏂ゆ嫹");
		} catch (ClassNotFoundException ex) {
			System.out.println("SQLException: " + ex.getMessage());
		}
		try {
			conn=DriverManager.getConnection(url,Username,Password);
			//System.out.println(new Date()+" 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷疯彉閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓缂寸櫢鎷烽敓鏂ゆ嫹");
		} catch (SQLException ex) {
			System.out.println("SQLException: " + ex.getMessage());
		}
	}
	/**
	  閿熸磥鏂归敓鏂ゆ嫹
	 Description:閿熸埅鎲嬫嫹閿熸枻鎷疯彉閿熸枻鎷烽敓鏂ゆ嫹鎷ュ閿熸枻鎷烽敓锟�
	 Auther:钖涢敓鏂ゆ嫹10170365
	 Date:2017-10-17閿熸枻鎷烽敓鏂ゆ嫹11:13:21
	 */
	public  void close(){
		if(conn!=null){
			try {
				conn.close();
				//System.out.println(new Date()+" 閿熺即鐧告嫹閿熻緝鍖℃嫹閿熸枻鎷疯彉閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸帴锝忔嫹");
			} catch (SQLException e) {
				System.out.println("SQLException: " + e.getMessage());
			}finally{
				conn = null;
			}
		}
	}
	/**
	 * 
	  閿熸磥鏂归敓鏂ゆ嫹
	 Description:鎵ч敓鏂ゆ嫹Sql閿熸枻鎷烽敓渚ュ嚖鎷烽敓鏂ゆ嫹
	 Auther:钖涢敓鏂ゆ嫹10170365
	 Date:2017-10-17閿熸枻鎷烽敓鏂ゆ嫹12:28:38
	 */
	public  int execute(String sql){
		
		if(conn==null) 
			new DaoDateSet();	//閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷蜂剑閿熸枻鎷烽敓杞款亷鎷风湪閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷风閿熸枻鎷烽湁閿熸枻鎷锋梿鏂ゆ嫹閿燂拷
		try {
			return conn.createStatement().executeUpdate(sql);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			System.out.print("SQLException: " + e.getMessage());System.out.println("  閿熸枻鎷烽敓鏂ゆ嫹閿熺獤杈炬嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷峰�奸敓鏂ゆ嫹");
			return -1;
		}
	}
	/**
	 * 
	  閿熸磥鏂归敓鏂ゆ嫹
	 Description:閿熸枻鎷烽敓浠嬶級閿熸枻鎷疯閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸埅璇ф嫹璇㈤敓鏂ゆ嫹閿燂拷
	 Auther:钖涢敓鏂ゆ嫹10170365
	 Date:2017-10-17閿熸枻鎷烽敓鏂ゆ嫹02:35:58
	 */
	public static  ResultSet executeQuery(String sql){
		try {
			if(conn==null)  new DaoDateSet();  
			return conn.createStatement(ResultSet.TYPE_SCROLL_SENSITIVE,
					ResultSet.CONCUR_UPDATABLE).executeQuery(sql);//鎵ч敓鍙鎷疯
		} catch (SQLException e) {
			System.out.print("SQLException: " + e.getMessage());
			return null;
		} finally {
		}
	}
	
	
	/**
	  閿熸磥鏂归敓鏂ゆ嫹
	 Description:閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷疯嵓閿熺惮ser閿熸枻鎷烽敓鍙鎷烽敓璇崟閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓渚ュ尅鎷峰閿熸枻鎷烽敓锟�
	 Auther:钖涢敓鏂ゆ嫹10170365
	 Date:2017-10-17閿熸枻鎷烽敓鏂ゆ嫹11:30:24
	 */

	
	

}

