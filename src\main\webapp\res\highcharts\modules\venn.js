/*
 Highcharts JS v9.0.1 (2021-02-15)

 (c) 2017-2021 Highsoft AS
 Authors: <AUTHORS>

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/modules/venn",["highcharts"],function(t){a(t);a.Highcharts=t;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){function t(a,f,b,n){a.hasOwnProperty(f)||(a[f]=n.apply(null,b))}a=a?a._modules:{};t(a,"Mixins/Geometry.js",[],function(){return{getAngleBetweenPoints:function(a,f){return Math.atan2(f.x-a.x,f.y-a.y)},getCenterOfPoints:function(a){var f=
a.reduce(function(b,a){b.x+=a.x;b.y+=a.y;return b},{x:0,y:0});return{x:f.x/a.length,y:f.y/a.length}},getDistanceBetweenPoints:function(a,f){return Math.sqrt(Math.pow(f.x-a.x,2)+Math.pow(f.y-a.y,2))}}});t(a,"Mixins/GeometryCircles.js",[a["Mixins/Geometry.js"]],function(a){function f(e,l){l=Math.pow(10,l);return Math.round(e*l)/l}function b(e){if(0>=e)throw Error("radius of circle must be a positive number.");return Math.PI*e*e}function n(e,l){return e*e*Math.acos(1-l/e)-(e-l)*Math.sqrt(l*(2*e-l))}
function q(e,l){var a=r(e,l),b=e.r,g=l.r,p=[];if(a<b+g&&a>Math.abs(b-g)){b*=b;var c=(b-g*g+a*a)/(2*a);g=Math.sqrt(b-c*c);b=e.x;p=l.x;e=e.y;var k=l.y;l=b+c*(p-b)/a;c=e+c*(k-e)/a;e=g/a*-(k-e);a=g/a*-(p-b);p=[{x:f(l+e,14),y:f(c-a,14)},{x:f(l-e,14),y:f(c+a,14)}]}return p}function c(e){return e.reduce(function(e,a,b,g){g=g.slice(b+1).reduce(function(e,l,g){var f=[b,g+b+1];return e.concat(q(a,l).map(function(e){e.indexes=f;return e}))},[]);return e.concat(g)},[])}function k(e,a){return r(e,a)<=a.r+1e-10}
function v(e,a){return!a.some(function(a){return!k(e,a)})}function u(e){return c(e).filter(function(a){return v(a,e)})}var g=a.getAngleBetweenPoints,m=a.getCenterOfPoints,r=a.getDistanceBetweenPoints;return{getAreaOfCircle:b,getAreaOfIntersectionBetweenCircles:function(e){var a=u(e);if(1<a.length){var b=m(a);a=a.map(function(a){a.angle=g(b,a);return a}).sort(function(a,e){return e.angle-a.angle});var f=a[a.length-1];a=a.reduce(function(a,b){var l=a.startPoint,f=m([l,b]),c=b.indexes.filter(function(a){return-1<
l.indexes.indexOf(a)}).reduce(function(a,c){c=e[c];var k=g(c,b),E=g(c,l);k=E-(E-k+(E<k?2*Math.PI:0))/2;k=r(f,{x:c.x+c.r*Math.sin(k),y:c.y+c.r*Math.cos(k)});c=c.r;k>2*c&&(k=2*c);if(!a||a.width>k)a={r:c,largeArc:k>c?1:0,width:k,x:b.x,y:b.y};return a},null);if(c){var k=c.r;a.arcs.push(["A",k,k,0,c.largeArc,1,c.x,c.y]);a.startPoint=b}return a},{startPoint:f,arcs:[]}).arcs;if(0!==a.length&&1!==a.length){a.unshift(["M",f.x,f.y]);var c={center:b,d:a}}}return c},getCircleCircleIntersection:q,getCirclesIntersectionPoints:c,
getCirclesIntersectionPolygon:u,getCircularSegmentArea:n,getOverlapBetweenCircles:function(a,c,g){var e=0;g<a+c&&(g<=Math.abs(c-a)?e=b(a<c?a:c):(e=(a*a-c*c+g*g)/(2*g),g-=e,e=n(a,a-e)+n(c,c-g)),e=f(e,14));return e},isCircle1CompletelyOverlappingCircle2:function(a,b){return r(a,b)+b.r<a.r+1e-10},isPointInsideCircle:k,isPointInsideAllCircles:v,isPointOutsideAllCircles:function(a,b){return!b.some(function(b){return k(a,b)})},round:f}});t(a,"Mixins/NelderMead.js",[],function(){var a=function(a){a=a.slice(0,
-1);for(var b=a.length,f=[],q=function(a,b){a.sum+=b[a.i];return a},c=0;c<b;c++)f[c]=a.reduce(q,{sum:0,i:c}).sum/b;return f};return{getCentroid:a,nelderMead:function(f,b){var n=function(a,b){return a.fx-b.fx},q=function(a,b,c,g){return b.map(function(b,e){return a*b+c*g[e]})},c=function(a,b){b.fx=f(b);a[a.length-1]=b;return a},k=function(a){var b=a[0];return a.map(function(a){a=q(.5,b,.5,a);a.fx=f(a);return a})},v=function(a,b,c,g){a=q(c,a,g,b);a.fx=f(a);return a};b=function(a){var b=a.length,c=Array(b+
1);c[0]=a;c[0].fx=f(a);for(var g=0;g<b;++g){var e=a.slice();e[g]=e[g]?1.05*e[g]:.001;e.fx=f(e);c[g+1]=e}return c}(b);for(var u=0;100>u;u++){b.sort(n);var g=b[b.length-1],m=a(b),r=v(m,g,2,-1);r.fx<b[0].fx?(g=v(m,g,3,-2),b=c(b,g.fx<r.fx?g:r)):r.fx>=b[b.length-2].fx?r.fx>g.fx?(m=v(m,g,.5,.5),b=m.fx<g.fx?c(b,m):k(b)):(m=v(m,g,1.5,-.5),b=m.fx<r.fx?c(b,m):k(b)):b=c(b,r)}return b[0]}}});t(a,"Mixins/DrawPoint.js",[],function(){var a=function(a){return"function"===typeof a},f=function(b){var f,q=this,c=q.graphic,
k=b.animatableAttribs,v=b.onComplete,u=b.css,g=b.renderer,m=null===(f=q.series)||void 0===f?void 0:f.options.animation;if(q.shouldDraw())c||(q.graphic=c=g[b.shapeType](b.shapeArgs).add(b.group)),c.css(u).attr(b.attribs).animate(k,b.isNew?!1:m,v);else if(c){var r=function(){q.graphic=c=c.destroy();a(v)&&v()};Object.keys(k).length?c.animate(k,void 0,function(){r()}):r()}};return{draw:f,drawPoint:function(a){(a.attribs=a.attribs||{})["class"]=this.getClassName();f.call(this,a)},isFn:a}});t(a,"Series/Venn/VennPoint.js",
[a["Mixins/DrawPoint.js"],a["Core/Series/SeriesRegistry.js"],a["Core/Utilities.js"]],function(a,f,b){var q=this&&this.__extends||function(){var a=function(b,c){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])};return a(b,c)};return function(b,c){function g(){this.constructor=b}a(b,c);b.prototype=null===c?Object.create(c):(g.prototype=c.prototype,new g)}}(),t=b.extend,c=b.isNumber;f=function(a){function b(){var b=
null!==a&&a.apply(this,arguments)||this;b.options=void 0;b.series=void 0;return b}q(b,a);b.prototype.isValid=function(){return c(this.value)};b.prototype.shouldDraw=function(){return!!this.shapeArgs};return b}(f.seriesTypes.scatter.prototype.pointClass);t(f.prototype,{draw:a.drawPoint});return f});t(a,"Series/Venn/VennUtils.js",[a["Mixins/GeometryCircles.js"],a["Mixins/Geometry.js"],a["Mixins/NelderMead.js"],a["Core/Utilities.js"]],function(a,f,b,n){var q=a.getAreaOfCircle,c=a.getCircleCircleIntersection,
k=a.getOverlapBetweenCircles,v=a.isPointInsideAllCircles,u=a.isPointInsideCircle,g=a.isPointOutsideAllCircles,m=f.getDistanceBetweenPoints,r=n.extend,e=n.isArray,l=n.isNumber,t=n.isObject,I=n.isString,A;(function(p){function n(a){var b=a.filter(function(a){return 2===a.sets.length}).reduce(function(a,b){b.sets.forEach(function(d,h,c){t(a[d])||(a[d]={overlapping:{},totalOverlap:0});a[d].totalOverlap+=b.value;a[d].overlapping[c[1-h]]=b.value});return a},{});a.filter(C).forEach(function(a){r(a,b[a.sets[0]])});
return a}function A(a,b,d,h,z){var c=a(b),F=a(d);z=z||100;h=h||1e-10;var e=d-b,g=1;if(b>=d)throw Error("a must be smaller than b.");if(0<c*F)throw Error("f(a) and f(b) must have opposite signs.");if(0===c)var w=b;else if(0===F)w=d;else for(;g++<=z&&0!==E&&e>h;){e=(d-b)/2;w=b+e;var E=a(w);0<c*E?b=w:d=w}return w}function B(a,b,d){var h=a+b;return 0>=d?h:q(a<b?a:b)<=d?0:A(function(h){h=k(a,b,h);return d-h},0,h)}function G(a){var b=0;2===a.length&&(b=a[0],a=a[1],b=k(b.r,a.r,m(b,a)));return b}function C(a){return e(a.sets)&&
1===a.sets.length}function y(a){var b={};return t(a)&&l(a.value)&&-1<a.value&&e(a.sets)&&0<a.sets.length&&!a.sets.some(function(a){var d=!1;!b[a]&&I(a)?b[a]=!0:d=!0;return d})}function H(a,b){return b.reduce(function(b,h){var d=0;1<h.sets.length&&(d=h.value,h=G(h.sets.map(function(b){return a[b]})),h=d-h,d=Math.round(h*h*1E11)/1E11);return b+d},0)}function D(a,b){return b.totalOverlap-a.totalOverlap}p.geometry=f;p.geometryCircles=a;p.nelderMead=b;p.addOverlapToSets=n;p.getDistanceBetweenCirclesByOverlap=
B;p.getLabelWidth=function(a,b,d){var h=b.reduce(function(a,b){return Math.min(b.r,a)},Infinity),c=d.filter(function(b){return!u(a,b)});d=function(d,h){return A(function(z){var e={x:a.x+h*z,y:a.y};e=v(e,b)&&g(e,c);return-(d-z)+(e?0:Number.MAX_VALUE)},0,d)};return 2*Math.min(d(h,-1),d(h,1))};p.getMarginFromCircles=function(a,b,d){b=b.reduce(function(b,d){d=d.r-m(a,d);return d<=b?d:b},Number.MAX_VALUE);return b=d.reduce(function(b,d){d=m(a,d)-d.r;return d<=b?d:b},b)};p.isSet=C;p.layoutGreedyVenn=function(a){var b=
[],d={};a.filter(function(a){return 1===a.sets.length}).forEach(function(a){d[a.sets[0]]=a.circle={x:Number.MAX_VALUE,y:Number.MAX_VALUE,r:Math.sqrt(a.value/Math.PI)}});var h=function(a,d){var c=a.circle;c.x=d.x;c.y=d.y;b.push(a)};n(a);var e=a.filter(C).sort(D);h(e.shift(),{x:0,y:0});var g=a.filter(function(a){return 2===a.sets.length});e.forEach(function(a){var e=a.circle,z=e.r,F=a.overlapping,f=b.reduce(function(a,h,f){var x=h.circle,w=B(z,x.r,F[h.sets[0]]),k=[{x:x.x+w,y:x.y},{x:x.x-w,y:x.y},{x:x.x,
y:x.y+w},{x:x.x,y:x.y-w}];b.slice(f+1).forEach(function(a){var b=a.circle;a=B(z,b.r,F[a.sets[0]]);k=k.concat(c({x:x.x,y:x.y,r:w},{x:b.x,y:b.y,r:a}))});k.forEach(function(b){e.x=b.x;e.y=b.y;var c=H(d,g);c<a.loss&&(a.loss=c,a.coordinates=b)});return a},{loss:Number.MAX_VALUE,coordinates:void 0});h(a,f.coordinates)});return d};p.loss=H;p.processVennData=function(a){a=e(a)?a:[];var b=a.reduce(function(a,b){y(b)&&C(b)&&0<b.value&&-1===a.indexOf(b.sets[0])&&a.push(b.sets[0]);return a},[]).sort(),d=a.reduce(function(a,
d){y(d)&&!d.sets.some(function(a){return-1===b.indexOf(a)})&&(a[d.sets.sort().join()]=d);return a},{});b.reduce(function(a,b,d,c){c.slice(d+1).forEach(function(d){a.push(b+","+d)});return a},[]).forEach(function(a){if(!d[a]){var b={sets:a.split(","),value:0};d[a]=b}});return Object.keys(d).map(function(a){return d[a]})};p.sortByTotalOverlap=D})(A||(A={}));return A});t(a,"Series/Venn/VennSeries.js",[a["Core/Animation/AnimationUtilities.js"],a["Core/Color/Color.js"],a["Mixins/Geometry.js"],a["Mixins/GeometryCircles.js"],
a["Mixins/NelderMead.js"],a["Core/Color/Palette.js"],a["Core/Series/SeriesRegistry.js"],a["Series/Venn/VennPoint.js"],a["Series/Venn/VennUtils.js"],a["Core/Utilities.js"]],function(a,f,b,n,t,c,k,v,u,g){var m=this&&this.__extends||function(){var a=function(b,d){a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var d in b)b.hasOwnProperty(d)&&(a[d]=b[d])};return a(b,d)};return function(b,d){function c(){this.constructor=b}a(b,d);b.prototype=null===
d?Object.create(d):(c.prototype=d.prototype,new c)}}(),r=a.animObject,e=f.parse,l=b.getCenterOfPoints,q=n.getAreaOfIntersectionBetweenCircles,I=n.getCirclesIntersectionPolygon,A=n.isCircle1CompletelyOverlappingCircle2,p=n.isPointInsideAllCircles,J=n.isPointOutsideAllCircles,K=t.nelderMead,B=k.seriesTypes.scatter;a=g.addEvent;var G=g.extend,C=g.isArray,y=g.isNumber,H=g.isObject,D=g.merge;g=function(a){function b(){var b=null!==a&&a.apply(this,arguments)||this;b.data=void 0;b.mapOfIdToRelation=void 0;
b.options=void 0;b.points=void 0;return b}m(b,a);b.getLabelPosition=function(a,b){var d=a.reduce(function(d,c){var e=c.r/2;return[{x:c.x,y:c.y},{x:c.x+e,y:c.y},{x:c.x-e,y:c.y},{x:c.x,y:c.y+e},{x:c.x,y:c.y-e}].reduce(function(d,c){var e=u.getMarginFromCircles(c,a,b);d.margin<e&&(d.point=c,d.margin=e);return d},d)},{point:void 0,margin:-Number.MAX_VALUE}).point;d=K(function(d){return-u.getMarginFromCircles({x:d[0],y:d[1]},a,b)},[d.x,d.y]);d={x:d[0],y:d[1]};p(d,a)&&J(d,b)||(d=1<a.length?l(I(a)):{x:a[0].x,
y:a[0].y});return d};b.getLabelValues=function(a,c){var d=a.sets,e=c.reduce(function(a,b){var c=-1<d.indexOf(b.sets[0]);a[c?"internal":"external"].push(b.circle);return a},{internal:[],external:[]});e.external=e.external.filter(function(a){return e.internal.some(function(b){return!A(a,b)})});a=b.getLabelPosition(e.internal,e.external);c=u.getLabelWidth(a,e.internal,e.external);return{position:a,width:c}};b.layout=function(a){var d={},c={};if(0<a.length){var e=u.layoutGreedyVenn(a),g=a.filter(u.isSet);
a.forEach(function(a){var h=a.sets,f=h.join();if(h=u.isSet(a)?e[f]:q(h.map(function(a){return e[a]})))d[f]=h,c[f]=b.getLabelValues(a,g)})}return{mapOfIdToShape:d,mapOfIdToLabelValues:c}};b.getScale=function(a,b,c){var d=c.bottom-c.top,e=c.right-c.left;d=Math.min(0<e?1/e*a:1,0<d?1/d*b:1);return{scale:d,centerX:a/2-(c.right+c.left)/2*d,centerY:b/2-(c.top+c.bottom)/2*d}};b.updateFieldBoundaries=function(a,b){var c=b.x-b.r,d=b.x+b.r,e=b.y+b.r;b=b.y-b.r;if(!y(a.left)||a.left>c)a.left=c;if(!y(a.right)||
a.right<d)a.right=d;if(!y(a.top)||a.top>b)a.top=b;if(!y(a.bottom)||a.bottom<e)a.bottom=e;return a};b.prototype.animate=function(a){if(!a){var b=r(this.options.animation);this.points.forEach(function(a){var c=a.shapeArgs;if(a.graphic&&c){var d={},e={};c.d?d.opacity=.001:(d.r=0,e.r=c.r);a.graphic.attr(d).animate(e,b);c.d&&setTimeout(function(){a&&a.graphic&&a.graphic.animate({opacity:1})},b.duration)}},this)}};b.prototype.drawPoints=function(){var a=this,b=a.chart,c=a.group,e=b.renderer;(a.points||
[]).forEach(function(d){var g={zIndex:C(d.sets)?d.sets.length:0},f=d.shapeArgs;b.styledMode||G(g,a.pointAttribs(d,d.state));d.draw({isNew:!d.graphic,animatableAttribs:f,attribs:g,group:c,renderer:e,shapeType:f&&f.d?"path":"circle"})})};b.prototype.init=function(){B.prototype.init.apply(this,arguments);delete this.opacity};b.prototype.pointAttribs=function(a,b){var c=this.options||{};a=D(c,{color:a&&a.color},a&&a.options||{},b&&c.states[b]||{});return{fill:e(a.color).brighten(a.brightness).get(),opacity:a.opacity,
stroke:a.borderColor,"stroke-width":a.borderWidth,dashstyle:a.borderDashStyle}};b.prototype.translate=function(){var a=this.chart;this.processedXData=this.xData;this.generatePoints();var c=u.processVennData(this.options.data);c=b.layout(c);var e=c.mapOfIdToShape,g=c.mapOfIdToLabelValues;c=Object.keys(e).filter(function(a){return(a=e[a])&&y(a.r)}).reduce(function(a,c){return b.updateFieldBoundaries(a,e[c])},{top:0,bottom:0,left:0,right:0});a=b.getScale(a.plotWidth,a.plotHeight,c);var f=a.scale,k=a.centerX,
l=a.centerY;this.points.forEach(function(a){var b=C(a.sets)?a.sets:[],c=b.join(),d=e[c],h=g[c]||{};c=h.width;h=h.position;var n=a.options&&a.options.dataLabels;if(d){if(d.r)var m={x:k+d.x*f,y:l+d.y*f,r:d.r*f};else d.d&&(d=d.d,d.forEach(function(a){"M"===a[0]?(a[1]=k+a[1]*f,a[2]=l+a[2]*f):"A"===a[0]&&(a[1]*=f,a[2]*=f,a[6]=k+a[6]*f,a[7]=l+a[7]*f)}),m={d:d});h?(h.x=k+h.x*f,h.y=l+h.y*f):h={};y(c)&&(c=Math.round(c*f))}a.shapeArgs=m;h&&m&&(a.plotX=h.x,a.plotY=h.y);c&&m&&(a.dlOptions=D(!0,{style:{width:c}},
H(n)&&n));a.name=a.options.name||b.join("\u2229")})};b.defaultOptions=D(B.defaultOptions,{borderColor:c.neutralColor20,borderDashStyle:"solid",borderWidth:1,brighten:0,clip:!1,colorByPoint:!0,dataLabels:{enabled:!0,verticalAlign:"middle",formatter:function(){return this.point.name}},inactiveOtherPoints:!0,marker:!1,opacity:.75,showInLegend:!1,states:{hover:{opacity:1,borderColor:c.neutralColor80},select:{color:c.neutralColor20,borderColor:c.neutralColor100,animation:!1},inactive:{opacity:.075}},tooltip:{pointFormat:"{point.name}: {point.value}"}});
return b}(B);G(g.prototype,{axisTypes:[],directTouch:!0,isCartesian:!1,pointArrayMap:["value"],pointClass:v,utils:u});k.registerSeriesType("venn",g);"";a(g,"afterSetOptions",function(a){var b=a.options.states;this.is("venn")&&Object.keys(b).forEach(function(a){b[a].halo=!1})});return g});t(a,"masters/modules/venn.src.js",[],function(){})});
//# sourceMappingURL=venn.js.map