<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
    pageEncoding="ISO-8859-1"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="ISO-8859-1">
<title>Insert title here</title>
<style>
.box {
  position: relative;
  width: 200px;
  height: 100px;
  background-color: #f2f2f2;
  border-radius: 10px;
}

.box::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  height: 10px;
  background-color: #fff;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  transform: skewY(-2deg); /* 调整这个值可以改变弧线的角度 */
}

</style>
</head>
<body>
<div class="box">Tab 1</div>
<div class="box">Tab 2</div>
<div class="box">Tab 3</div>
</body>
</html>