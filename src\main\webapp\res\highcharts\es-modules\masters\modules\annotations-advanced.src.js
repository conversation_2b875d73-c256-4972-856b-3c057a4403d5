/**
 * @license Highcharts JS v9.0.1 (2021-02-15)
 * @module highcharts/modules/annotations-advanced
 * @requires highcharts
 *
 * Annotations module
 *
 * (c) 2009-2021 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */
'use strict';
import '../../Extensions/Annotations/Annotations.js';
import '../../Extensions/Annotations/Types/BasicAnnotation.js';
import '../../Extensions/Annotations/Types/CrookedLine.js';
import '../../Extensions/Annotations/Types/ElliottWave.js';
import '../../Extensions/Annotations/Types/Tunnel.js';
import '../../Extensions/Annotations/Types/InfinityLine.js';
import '../../Extensions/Annotations/Types/Fibonacci.js';
import '../../Extensions/Annotations/Types/Pitchfork.js';
import '../../Extensions/Annotations/Types/VerticalLine.js';
import '../../Extensions/Annotations/Types/Measure.js';
import '../../Extensions/Annotations/NavigationBindings.js';
import '../../Extensions/Annotations/Popup.js';
