/*
 Highcharts JS v9.0.1 (2021-02-15)

 Annotations module

 (c) 2009-2021 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/modules/annotations",["highcharts"],function(t){a(t);a.Highcharts=t;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){function t(c,a,m,r){c.hasOwnProperty(a)||(c[a]=r.apply(null,m))}a=a?a._modules:{};t(a,"Extensions/Annotations/Mixins/EventEmitterMixin.js",[a["Core/Globals.js"],a["Core/Utilities.js"]],function(c,a){var g=a.addEvent,
r=a.fireEvent,k=a.objectEach,x=a.pick,b=a.removeEvent;return{addEvents:function(){var b=this,d=function(d){g(d,c.isTouchDevice?"touchstart":"mousedown",function(d){b.onMouseDown(d)},{passive:!1})};d(this.graphic.element);(b.labels||[]).forEach(function(b){b.options.useHTML&&b.graphic.text&&d(b.graphic.text.element)});k(b.options.events,function(d,h){var e=function(e){"click"===h&&b.cancelClick||d.call(b,b.chart.pointer.normalize(e),b.target)};if(-1===(b.nonDOMEvents||[]).indexOf(h))b.graphic.on(h,
e);else g(b,h,e,{passive:!1})});if(b.options.draggable&&(g(b,"drag",b.onDrag),!b.graphic.renderer.styledMode)){var e={cursor:{x:"ew-resize",y:"ns-resize",xy:"move"}[b.options.draggable]};b.graphic.css(e);(b.labels||[]).forEach(function(d){d.options.useHTML&&d.graphic.text&&d.graphic.text.css(e)})}b.isUpdating||r(b,"add")},removeDocEvents:function(){this.removeDrag&&(this.removeDrag=this.removeDrag());this.removeMouseUp&&(this.removeMouseUp=this.removeMouseUp())},onMouseDown:function(b){var d=this,
e=d.chart.pointer;b.preventDefault&&b.preventDefault();if(2!==b.button){b=e.normalize(b);var u=b.chartX;var h=b.chartY;d.cancelClick=!1;d.chart.hasDraggedAnnotation=!0;d.removeDrag=g(c.doc,c.isTouchDevice?"touchmove":"mousemove",function(b){d.hasDragged=!0;b=e.normalize(b);b.prevChartX=u;b.prevChartY=h;r(d,"drag",b);u=b.chartX;h=b.chartY},c.isTouchDevice?{passive:!1}:void 0);d.removeMouseUp=g(c.doc,c.isTouchDevice?"touchend":"mouseup",function(b){d.cancelClick=d.hasDragged;d.hasDragged=!1;d.chart.hasDraggedAnnotation=
!1;r(x(d.target,d),"afterUpdate");d.onMouseUp(b)},c.isTouchDevice?{passive:!1}:void 0)}},onMouseUp:function(b){var d=this.chart;b=this.target||this;var e=d.options.annotations;d=d.annotations.indexOf(b);this.removeDocEvents();e[d]=b.options},onDrag:function(b){if(this.chart.isInsidePlot(b.chartX-this.chart.plotLeft,b.chartY-this.chart.plotTop)){var d=this.mouseMoveToTranslation(b);"x"===this.options.draggable&&(d.y=0);"y"===this.options.draggable&&(d.x=0);this.points.length?this.translate(d.x,d.y):
(this.shapes.forEach(function(b){b.translate(d.x,d.y)}),this.labels.forEach(function(b){b.translate(d.x,d.y)}));this.redraw(!1)}},mouseMoveToRadians:function(b,d,e){var c=b.prevChartY-e,h=b.prevChartX-d;e=b.chartY-e;b=b.chartX-d;this.chart.inverted&&(d=h,h=c,c=d,d=b,b=e,e=d);return Math.atan2(e,b)-Math.atan2(c,h)},mouseMoveToTranslation:function(b){var d=b.chartX-b.prevChartX;b=b.chartY-b.prevChartY;if(this.chart.inverted){var e=b;b=d;d=e}return{x:d,y:b}},mouseMoveToScale:function(b,d,e){d=(b.chartX-
d||1)/(b.prevChartX-d||1);b=(b.chartY-e||1)/(b.prevChartY-e||1);this.chart.inverted&&(e=b,b=d,d=e);return{x:d,y:b}},destroy:function(){this.removeDocEvents();b(this);this.hcEvents=null}}});t(a,"Extensions/Annotations/ControlPoint.js",[a["Core/Utilities.js"],a["Extensions/Annotations/Mixins/EventEmitterMixin.js"]],function(c,a){var g=c.merge,r=c.pick;return function(){function c(c,b,g,d){this.addEvents=a.addEvents;this.graphic=void 0;this.mouseMoveToRadians=a.mouseMoveToRadians;this.mouseMoveToScale=
a.mouseMoveToScale;this.mouseMoveToTranslation=a.mouseMoveToTranslation;this.onDrag=a.onDrag;this.onMouseDown=a.onMouseDown;this.onMouseUp=a.onMouseUp;this.removeDocEvents=a.removeDocEvents;this.nonDOMEvents=["drag"];this.chart=c;this.target=b;this.options=g;this.index=r(g.index,d)}c.prototype.setVisibility=function(c){this.graphic.attr("visibility",c?"visible":"hidden");this.options.visible=c};c.prototype.render=function(){var c=this.chart,b=this.options;this.graphic=c.renderer.symbol(b.symbol,0,
0,b.width,b.height).add(c.controlPointsGroup).css(b.style);this.setVisibility(b.visible);this.addEvents()};c.prototype.redraw=function(c){this.graphic[c?"animate":"attr"](this.options.positioner.call(this,this.target))};c.prototype.destroy=function(){a.destroy.call(this);this.graphic&&(this.graphic=this.graphic.destroy());this.options=this.target=this.chart=null};c.prototype.update=function(c){var b=this.chart,a=this.target,d=this.index;c=g(!0,this.options,c);this.destroy();this.constructor(b,a,c,
d);this.render(b.controlPointsGroup);this.redraw()};return c}()});t(a,"Extensions/Annotations/MockPoint.js",[a["Core/Series/Series.js"],a["Core/Utilities.js"],a["Core/Axis/Axis.js"]],function(c,a,m){var g=a.defined,k=a.fireEvent;return function(){function a(b,a,d){this.y=this.x=this.plotY=this.plotX=this.isInside=void 0;this.mock=!0;this.series={visible:!0,chart:b,getPlotBox:c.prototype.getPlotBox};this.target=a||null;this.options=d;this.applyOptions(this.getOptions())}a.fromPoint=function(b){return new a(b.series.chart,
null,{x:b.x,y:b.y,xAxis:b.series.xAxis,yAxis:b.series.yAxis})};a.pointToPixels=function(b,c){var d=b.series,e=d.chart,a=b.plotX,h=b.plotY;e.inverted&&(b.mock?(a=b.plotY,h=b.plotX):(a=e.plotWidth-b.plotY,h=e.plotHeight-b.plotX));d&&!c&&(b=d.getPlotBox(),a+=b.translateX,h+=b.translateY);return{x:a,y:h}};a.pointToOptions=function(b){return{x:b.x,y:b.y,xAxis:b.series.xAxis,yAxis:b.series.yAxis}};a.prototype.hasDynamicOptions=function(){return"function"===typeof this.options};a.prototype.getOptions=function(){return this.hasDynamicOptions()?
this.options(this.target):this.options};a.prototype.applyOptions=function(b){this.command=b.command;this.setAxis(b,"x");this.setAxis(b,"y");this.refresh()};a.prototype.setAxis=function(b,c){c+="Axis";b=b[c];var d=this.series.chart;this.series[c]=b instanceof m?b:g(b)?d[c][b]||d.get(b):null};a.prototype.toAnchor=function(){var b=[this.plotX,this.plotY,0,0];this.series.chart.inverted&&(b[0]=this.plotY,b[1]=this.plotX);return b};a.prototype.getLabelConfig=function(){return{x:this.x,y:this.y,point:this}};
a.prototype.isInsidePlot=function(){var b=this.plotX,c=this.plotY,d=this.series.xAxis,e=this.series.yAxis,a={x:b,y:c,isInsidePlot:!0};d&&(a.isInsidePlot=g(b)&&0<=b&&b<=d.len);e&&(a.isInsidePlot=a.isInsidePlot&&g(c)&&0<=c&&c<=e.len);k(this.series.chart,"afterIsInsidePlot",a);return a.isInsidePlot};a.prototype.refresh=function(){var b=this.series,c=b.xAxis;b=b.yAxis;var d=this.getOptions();c?(this.x=d.x,this.plotX=c.toPixels(d.x,!0)):(this.x=null,this.plotX=d.x);b?(this.y=d.y,this.plotY=b.toPixels(d.y,
!0)):(this.y=null,this.plotY=d.y);this.isInside=this.isInsidePlot()};a.prototype.translate=function(b,c,d,e){this.hasDynamicOptions()||(this.plotX+=d,this.plotY+=e,this.refreshOptions())};a.prototype.scale=function(b,c,d,e){if(!this.hasDynamicOptions()){var a=this.plotY*e;this.plotX=(1-d)*b+this.plotX*d;this.plotY=(1-e)*c+a;this.refreshOptions()}};a.prototype.rotate=function(b,c,d){if(!this.hasDynamicOptions()){var e=Math.cos(d);d=Math.sin(d);var a=this.plotX,h=this.plotY;a-=b;h-=c;this.plotX=a*e-
h*d+b;this.plotY=a*d+h*e+c;this.refreshOptions()}};a.prototype.refreshOptions=function(){var b=this.series,c=b.xAxis;b=b.yAxis;this.x=this.options.x=c?this.options.x=c.toValue(this.plotX,!0):this.plotX;this.y=this.options.y=b?b.toValue(this.plotY,!0):this.plotY};return a}()});t(a,"Extensions/Annotations/Mixins/ControllableMixin.js",[a["Extensions/Annotations/ControlPoint.js"],a["Extensions/Annotations/MockPoint.js"],a["Core/Tooltip.js"],a["Core/Utilities.js"]],function(c,a,m,r){var g=r.isObject,x=
r.isString,b=r.merge,B=r.splat;return{init:function(b,c,a){this.annotation=b;this.chart=b.chart;this.options=c;this.points=[];this.controlPoints=[];this.index=a;this.linkPoints();this.addControlPoints()},attr:function(){this.graphic.attr.apply(this.graphic,arguments)},getPointsOptions:function(){var b=this.options;return b.points||b.point&&B(b.point)},attrsFromOptions:function(b){var c=this.constructor.attrsMap,d={},h,a=this.chart.styledMode;for(h in b){var p=c[h];!p||a&&-1!==["fill","stroke","stroke-width"].indexOf(p)||
(d[p]=b[h])}return d},anchor:function(c){var d=c.series.getPlotBox(),a=c.series.chart,h=c.mock?c.toAnchor():m.prototype.getAnchor.call({chart:c.series.chart},c);h={x:h[0]+(this.options.x||0),y:h[1]+(this.options.y||0),height:h[2]||0,width:h[3]||0};return{relativePosition:h,absolutePosition:b(h,{x:h.x+(c.mock?d.translateX:a.plotLeft),y:h.y+(c.mock?d.translateY:a.plotTop)})}},point:function(b,c){if(b&&b.series)return b;c&&null!==c.series||(g(b)?c=new a(this.chart,this,b):x(b)?c=this.chart.get(b)||null:
"function"===typeof b&&(c=b.call(c,this),c=c.series?c:new a(this.chart,this,b)));return c},linkPoints:function(){var b=this.getPointsOptions(),c=this.points,a=b&&b.length||0,h;for(h=0;h<a;h++){var z=this.point(b[h],c[h]);if(!z){c.length=0;return}z.mock&&z.refresh();c[h]=z}return c},addControlPoints:function(){var a=this.options.controlPoints;(a||[]).forEach(function(d,u){d=b(this.options.controlPointOptions,d);d.index||(d.index=u);a[u]=d;this.controlPoints.push(new c(this.chart,this,d))},this)},shouldBeDrawn:function(){return!!this.points.length},
render:function(b){this.controlPoints.forEach(function(b){b.render()})},redraw:function(b){this.controlPoints.forEach(function(c){c.redraw(b)})},transform:function(b,c,a,h,z){if(this.chart.inverted){var d=c;c=a;a=d}this.points.forEach(function(d,e){this.transformPoint(b,c,a,h,z,e)},this)},transformPoint:function(b,c,u,h,z,p){var d=this.points[p];d.mock||(d=this.points[p]=a.fromPoint(d));d[b](c,u,h,z)},translate:function(b,c){this.transform("translate",null,null,b,c)},translatePoint:function(b,c,a){this.transformPoint("translate",
null,null,b,c,a)},translateShape:function(b,c){var a=this.annotation.chart,h=this.annotation.userOptions,d=a.annotations.indexOf(this.annotation);a=a.options.annotations[d];this.translatePoint(b,c,0);a[this.collection][this.index].point=this.options.point;h[this.collection][this.index].point=this.options.point},rotate:function(b,c,a){this.transform("rotate",b,c,a)},scale:function(b,c,a,h){this.transform("scale",b,c,a,h)},setControlPointsVisibility:function(b){this.controlPoints.forEach(function(c){c.setVisibility(b)})},
destroy:function(){this.graphic&&(this.graphic=this.graphic.destroy());this.tracker&&(this.tracker=this.tracker.destroy());this.controlPoints.forEach(function(b){b.destroy()});this.options=this.controlPoints=this.points=this.chart=null;this.annotation&&(this.annotation=null)},update:function(c){var a=this.annotation;c=b(!0,this.options,c);var d=this.graphic.parentGroup;this.destroy();this.constructor(a,c);this.render(d);this.redraw()}}});t(a,"Extensions/Annotations/Mixins/MarkerMixin.js",[a["Core/Chart/Chart.js"],
a["Core/Renderer/SVG/SVGRenderer.js"],a["Core/Utilities.js"]],function(c,a,m){function g(b){return function(c){this.attr(b,"url(#"+c+")")}}var k=m.addEvent,x=m.defined,b=m.merge,B=m.objectEach,d=m.uniqueKey,e={arrow:{tagName:"marker",attributes:{display:"none",id:"arrow",refY:5,refX:9,markerWidth:10,markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 0 L 10 5 L 0 10 Z","stroke-width":0}}]},"reverse-arrow":{tagName:"marker",attributes:{display:"none",id:"reverse-arrow",refY:5,refX:1,markerWidth:10,
markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 5 L 10 0 L 10 10 Z","stroke-width":0}}]}};a.prototype.addMarker=function(c,a){var h={attributes:{id:c}},d={stroke:a.color||"none",fill:a.color||"rgba(0, 0, 0, 0.75)"};h.children=a.children.map(function(c){return b(d,c)});a=b(!0,{attributes:{markerWidth:20,markerHeight:20,refX:0,refY:0,orient:"auto"}},a,h);a=this.definition(a);a.id=c;return a};a={markerEndSetter:g("marker-end"),markerStartSetter:g("marker-start"),setItemMarkers:function(c){var a=
c.options,e=c.chart,p=e.options.defs,u=a.fill,g=x(u)&&"none"!==u?u:a.stroke;["markerStart","markerEnd"].forEach(function(h){var f,l=a[h],w;if(l){for(w in p){var v=p[w];if((l===(null===(f=v.attributes)||void 0===f?void 0:f.id)||l===v.id)&&"marker"===v.tagName){var n=v;break}}n&&(f=c[h]=e.renderer.addMarker((a.id||d())+"-"+l,b(n,{color:g})),c.attr(h,f.getAttribute("id")))}})}};k(c,"afterGetContainer",function(){this.options.defs=b(e,this.options.defs||{});B(this.options.defs,function(b){var c=b.attributes;
"marker"===b.tagName&&c&&"none"!==c.display&&this.renderer.addMarker(c.id,b)},this)});return a});t(a,"Extensions/Annotations/Controllables/ControllablePath.js",[a["Extensions/Annotations/Mixins/ControllableMixin.js"],a["Core/Globals.js"],a["Extensions/Annotations/Mixins/MarkerMixin.js"],a["Core/Utilities.js"]],function(c,a,m,r){var g=r.extend,x="rgba(192,192,192,"+(a.svg?.0001:.002)+")";return function(){function b(b,a,e){this.addControlPoints=c.addControlPoints;this.anchor=c.anchor;this.attr=c.attr;
this.attrsFromOptions=c.attrsFromOptions;this.destroy=c.destroy;this.getPointsOptions=c.getPointsOptions;this.init=c.init;this.linkPoints=c.linkPoints;this.point=c.point;this.rotate=c.rotate;this.scale=c.scale;this.setControlPointsVisibility=c.setControlPointsVisibility;this.setMarkers=m.setItemMarkers;this.transform=c.transform;this.transformPoint=c.transformPoint;this.translate=c.translate;this.translatePoint=c.translatePoint;this.translateShape=c.translateShape;this.update=c.update;this.type="path";
this.init(b,a,e);this.collection="shapes"}b.prototype.toD=function(){var b=this.options.d;if(b)return"function"===typeof b?b.call(this):b;b=this.points;var c=b.length,a=c,u=b[0],h=a&&this.anchor(u).absolutePosition,g=0,p=[];if(h)for(p.push(["M",h.x,h.y]);++g<c&&a;)u=b[g],a=u.command||"L",h=this.anchor(u).absolutePosition,"M"===a?p.push([a,h.x,h.y]):"L"===a?p.push([a,h.x,h.y]):"Z"===a&&p.push([a]),a=u.series.visible;return a?this.chart.renderer.crispLine(p,this.graphic.strokeWidth()):null};b.prototype.shouldBeDrawn=
function(){return c.shouldBeDrawn.call(this)||!!this.options.d};b.prototype.render=function(b){var a=this.options,e=this.attrsFromOptions(a);this.graphic=this.annotation.chart.renderer.path([["M",0,0]]).attr(e).add(b);a.className&&this.graphic.addClass(a.className);this.tracker=this.annotation.chart.renderer.path([["M",0,0]]).addClass("highcharts-tracker-line").attr({zIndex:2}).add(b);this.annotation.chart.styledMode||this.tracker.attr({"stroke-linejoin":"round",stroke:x,fill:x,"stroke-width":this.graphic.strokeWidth()+
2*a.snap});c.render.call(this);g(this.graphic,{markerStartSetter:m.markerStartSetter,markerEndSetter:m.markerEndSetter});this.setMarkers(this)};b.prototype.redraw=function(b){var a=this.toD(),e=b?"animate":"attr";a?(this.graphic[e]({d:a}),this.tracker[e]({d:a})):(this.graphic.attr({d:"M 0 -9000000000"}),this.tracker.attr({d:"M 0 -9000000000"}));this.graphic.placed=this.tracker.placed=!!a;c.redraw.call(this,b)};b.attrsMap={dashStyle:"dashstyle",strokeWidth:"stroke-width",stroke:"stroke",fill:"fill",
zIndex:"zIndex"};return b}()});t(a,"Extensions/Annotations/Controllables/ControllableRect.js",[a["Extensions/Annotations/Mixins/ControllableMixin.js"],a["Extensions/Annotations/Controllables/ControllablePath.js"],a["Core/Utilities.js"]],function(c,a,m){var g=m.merge;return function(){function k(a,b,g){this.addControlPoints=c.addControlPoints;this.anchor=c.anchor;this.attr=c.attr;this.attrsFromOptions=c.attrsFromOptions;this.destroy=c.destroy;this.getPointsOptions=c.getPointsOptions;this.init=c.init;
this.linkPoints=c.linkPoints;this.point=c.point;this.rotate=c.rotate;this.scale=c.scale;this.setControlPointsVisibility=c.setControlPointsVisibility;this.shouldBeDrawn=c.shouldBeDrawn;this.transform=c.transform;this.transformPoint=c.transformPoint;this.translatePoint=c.translatePoint;this.translateShape=c.translateShape;this.update=c.update;this.type="rect";this.translate=c.translateShape;this.init(a,b,g);this.collection="shapes"}k.prototype.render=function(a){var b=this.attrsFromOptions(this.options);
this.graphic=this.annotation.chart.renderer.rect(0,-9E9,0,0).attr(b).add(a);c.render.call(this)};k.prototype.redraw=function(a){var b=this.anchor(this.points[0]).absolutePosition;if(b)this.graphic[a?"animate":"attr"]({x:b.x,y:b.y,width:this.options.width,height:this.options.height});else this.attr({x:0,y:-9E9});this.graphic.placed=!!b;c.redraw.call(this,a)};k.attrsMap=g(a.attrsMap,{width:"width",height:"height"});return k}()});t(a,"Extensions/Annotations/Controllables/ControllableCircle.js",[a["Extensions/Annotations/Mixins/ControllableMixin.js"],
a["Extensions/Annotations/Controllables/ControllablePath.js"],a["Core/Utilities.js"]],function(c,a,m){var g=m.merge;return function(){function k(a,b,g){this.addControlPoints=c.addControlPoints;this.anchor=c.anchor;this.attr=c.attr;this.attrsFromOptions=c.attrsFromOptions;this.destroy=c.destroy;this.getPointsOptions=c.getPointsOptions;this.init=c.init;this.linkPoints=c.linkPoints;this.point=c.point;this.rotate=c.rotate;this.scale=c.scale;this.setControlPointsVisibility=c.setControlPointsVisibility;
this.shouldBeDrawn=c.shouldBeDrawn;this.transform=c.transform;this.transformPoint=c.transformPoint;this.translatePoint=c.translatePoint;this.translateShape=c.translateShape;this.update=c.update;this.type="circle";this.translate=c.translateShape;this.init(a,b,g);this.collection="shapes"}k.prototype.render=function(a){var b=this.attrsFromOptions(this.options);this.graphic=this.annotation.chart.renderer.circle(0,-9E9,0).attr(b).add(a);c.render.call(this)};k.prototype.redraw=function(a){var b=this.anchor(this.points[0]).absolutePosition;
if(b)this.graphic[a?"animate":"attr"]({x:b.x,y:b.y,r:this.options.r});else this.graphic.attr({x:0,y:-9E9});this.graphic.placed=!!b;c.redraw.call(this,a)};k.prototype.setRadius=function(c){this.options.r=c};k.attrsMap=g(a.attrsMap,{r:"r"});return k}()});t(a,"Extensions/Annotations/Controllables/ControllableLabel.js",[a["Extensions/Annotations/Mixins/ControllableMixin.js"],a["Extensions/Annotations/MockPoint.js"],a["Core/Renderer/SVG/SVGRenderer.js"],a["Core/Tooltip.js"],a["Core/Utilities.js"]],function(c,
a,m,r,k){var g=k.extend,b=k.format,t=k.isNumber,d=k.pick;k=function(){function e(b,a,d){this.addControlPoints=c.addControlPoints;this.attr=c.attr;this.attrsFromOptions=c.attrsFromOptions;this.destroy=c.destroy;this.getPointsOptions=c.getPointsOptions;this.init=c.init;this.linkPoints=c.linkPoints;this.point=c.point;this.rotate=c.rotate;this.scale=c.scale;this.setControlPointsVisibility=c.setControlPointsVisibility;this.shouldBeDrawn=c.shouldBeDrawn;this.transform=c.transform;this.transformPoint=c.transformPoint;
this.translateShape=c.translateShape;this.update=c.update;this.init(b,a,d);this.collection="labels"}e.alignedPosition=function(b,c){var a=b.align,h=b.verticalAlign,d=(c.x||0)+(b.x||0),e=(c.y||0)+(b.y||0),g,f;"right"===a?g=1:"center"===a&&(g=2);g&&(d+=(c.width-(b.width||0))/g);"bottom"===h?f=1:"middle"===h&&(f=2);f&&(e+=(c.height-(b.height||0))/f);return{x:Math.round(d),y:Math.round(e)}};e.justifiedOptions=function(b,c,a,d){var h=a.align,e=a.verticalAlign,g=c.box?0:c.padding||0,f=c.getBBox();c={align:h,
verticalAlign:e,x:a.x,y:a.y,width:c.width,height:c.height};a=d.x-b.plotLeft;var l=d.y-b.plotTop;d=a+g;0>d&&("right"===h?c.align="left":c.x=-d);d=a+f.width-g;d>b.plotWidth&&("left"===h?c.align="right":c.x=b.plotWidth-d);d=l+g;0>d&&("bottom"===e?c.verticalAlign="top":c.y=-d);d=l+f.height-g;d>b.plotHeight&&("top"===e?c.verticalAlign="bottom":c.y=b.plotHeight-d);return c};e.prototype.translatePoint=function(b,a){c.translatePoint.call(this,b,a,0)};e.prototype.translate=function(b,c){var a=this.annotation.chart,
d=this.annotation.userOptions,h=a.annotations.indexOf(this.annotation);h=a.options.annotations[h];a.inverted&&(a=b,b=c,c=a);this.options.x+=b;this.options.y+=c;h[this.collection][this.index].x=this.options.x;h[this.collection][this.index].y=this.options.y;d[this.collection][this.index].x=this.options.x;d[this.collection][this.index].y=this.options.y};e.prototype.render=function(b){var a=this.options,d=this.attrsFromOptions(a),g=a.style;this.graphic=this.annotation.chart.renderer.label("",0,-9999,
a.shape,null,null,a.useHTML,null,"annotation-label").attr(d).add(b);this.annotation.chart.styledMode||("contrast"===g.color&&(g.color=this.annotation.chart.renderer.getContrast(-1<e.shapesWithoutBackground.indexOf(a.shape)?"#FFFFFF":a.backgroundColor)),this.graphic.css(a.style).shadow(a.shadow));a.className&&this.graphic.addClass(a.className);this.graphic.labelrank=a.labelrank;c.render.call(this)};e.prototype.redraw=function(a){var d=this.options,e=this.text||d.format||d.text,g=this.graphic,k=this.points[0];
g.attr({text:e?b(e,k.getLabelConfig(),this.annotation.chart):d.formatter.call(k,this)});d=this.anchor(k);(e=this.position(d))?(g.alignAttr=e,e.anchorX=d.absolutePosition.x,e.anchorY=d.absolutePosition.y,g[a?"animate":"attr"](e)):g.attr({x:0,y:-9999});g.placed=!!e;c.redraw.call(this,a)};e.prototype.anchor=function(b){var a=c.anchor.apply(this,arguments),d=this.options.x||0,e=this.options.y||0;a.absolutePosition.x-=d;a.absolutePosition.y-=e;a.relativePosition.x-=d;a.relativePosition.y-=e;return a};
e.prototype.position=function(b){var c=this.graphic,k=this.annotation.chart,p=this.points[0],m=this.options,u=b.absolutePosition,x=b.relativePosition;if(b=p.series.visible&&a.prototype.isInsidePlot.call(p)){if(m.distance)var f=r.prototype.getPosition.call({chart:k,distance:d(m.distance,16)},c.width,c.height,{plotX:x.x,plotY:x.y,negative:p.negative,ttBelow:p.ttBelow,h:x.height||x.width});else m.positioner?f=m.positioner.call(this):(p={x:u.x,y:u.y,width:0,height:0},f=e.alignedPosition(g(m,{width:c.width,
height:c.height}),p),"justify"===this.options.overflow&&(f=e.alignedPosition(e.justifiedOptions(k,c,m,f),p)));m.crop&&(m=f.x-k.plotLeft,p=f.y-k.plotTop,b=k.isInsidePlot(m,p)&&k.isInsidePlot(m+c.width,p+c.height))}return b?f:null};e.attrsMap={backgroundColor:"fill",borderColor:"stroke",borderWidth:"stroke-width",zIndex:"zIndex",borderRadius:"r",padding:"padding"};e.shapesWithoutBackground=["connector"];return e}();m.prototype.symbols.connector=function(b,c,a,d,g){var e=g&&g.anchorX;g=g&&g.anchorY;
var h=a/2;if(t(e)&&t(g)){var k=[["M",e,g]];var f=c-g;0>f&&(f=-d-f);f<a&&(h=e<b+a/2?f:a-f);g>c+d?k.push(["L",b+h,c+d]):g<c?k.push(["L",b+h,c]):e<b?k.push(["L",b,c+d/2]):e>b+a&&k.push(["L",b+a,c+d/2])}return k||[]};return k});t(a,"Extensions/Annotations/Controllables/ControllableImage.js",[a["Extensions/Annotations/Controllables/ControllableLabel.js"],a["Extensions/Annotations/Mixins/ControllableMixin.js"]],function(c,a){return function(){function g(c,g,m){this.addControlPoints=a.addControlPoints;this.anchor=
a.anchor;this.attr=a.attr;this.attrsFromOptions=a.attrsFromOptions;this.destroy=a.destroy;this.getPointsOptions=a.getPointsOptions;this.init=a.init;this.linkPoints=a.linkPoints;this.point=a.point;this.rotate=a.rotate;this.scale=a.scale;this.setControlPointsVisibility=a.setControlPointsVisibility;this.shouldBeDrawn=a.shouldBeDrawn;this.transform=a.transform;this.transformPoint=a.transformPoint;this.translatePoint=a.translatePoint;this.translateShape=a.translateShape;this.update=a.update;this.type=
"image";this.translate=a.translateShape;this.init(c,g,m);this.collection="shapes"}g.prototype.render=function(c){var g=this.attrsFromOptions(this.options),m=this.options;this.graphic=this.annotation.chart.renderer.image(m.src,0,-9E9,m.width,m.height).attr(g).add(c);this.graphic.width=m.width;this.graphic.height=m.height;a.render.call(this)};g.prototype.redraw=function(g){var k=this.anchor(this.points[0]);if(k=c.prototype.position.call(this,k))this.graphic[g?"animate":"attr"]({x:k.x,y:k.y});else this.graphic.attr({x:0,
y:-9E9});this.graphic.placed=!!k;a.redraw.call(this,g)};g.attrsMap={width:"width",height:"height",zIndex:"zIndex"};return g}()});t(a,"Extensions/Annotations/Annotations.js",[a["Core/Animation/AnimationUtilities.js"],a["Core/Chart/Chart.js"],a["Extensions/Annotations/Mixins/ControllableMixin.js"],a["Extensions/Annotations/Controllables/ControllableRect.js"],a["Extensions/Annotations/Controllables/ControllableCircle.js"],a["Extensions/Annotations/Controllables/ControllablePath.js"],a["Extensions/Annotations/Controllables/ControllableImage.js"],
a["Extensions/Annotations/Controllables/ControllableLabel.js"],a["Extensions/Annotations/ControlPoint.js"],a["Extensions/Annotations/Mixins/EventEmitterMixin.js"],a["Core/Globals.js"],a["Extensions/Annotations/MockPoint.js"],a["Core/Pointer.js"],a["Core/Utilities.js"]],function(a,g,m,r,k,x,b,t,d,e,u,h,z,p){var c=a.getDeferredAnimation;a=g.prototype;var y=p.addEvent,B=p.defined,f=p.destroyObjectProperties,l=p.erase,w=p.extend,v=p.find,n=p.fireEvent,q=p.merge,D=p.pick,F=p.splat;p=p.wrap;var A=function(){function a(a,
b){this.annotation=void 0;this.coll="annotations";this.shapesGroup=this.labelsGroup=this.labelCollector=this.group=this.graphic=this.animationConfig=this.collection=void 0;this.chart=a;this.points=[];this.controlPoints=[];this.coll="annotations";this.labels=[];this.shapes=[];this.options=q(this.defaultOptions,b);this.userOptions=b;b=this.getLabelsAndShapesOptions(this.options,b);this.options.labels=b.labels;this.options.shapes=b.shapes;this.init(a,this.options)}a.prototype.init=function(){var a=this.chart,
b=this.options.animation;this.linkPoints();this.addControlPoints();this.addShapes();this.addLabels();this.setLabelCollector();this.animationConfig=c(a,b)};a.prototype.getLabelsAndShapesOptions=function(a,b){var c={};["labels","shapes"].forEach(function(d){a[d]&&(c[d]=F(b[d]).map(function(b,c){return q(a[d][c],b)}))});return c};a.prototype.addShapes=function(){(this.options.shapes||[]).forEach(function(a,b){a=this.initShape(a,b);q(!0,this.options.shapes[b],a.options)},this)};a.prototype.addLabels=
function(){(this.options.labels||[]).forEach(function(a,b){a=this.initLabel(a,b);q(!0,this.options.labels[b],a.options)},this)};a.prototype.addClipPaths=function(){this.setClipAxes();this.clipXAxis&&this.clipYAxis&&(this.clipRect=this.chart.renderer.clipRect(this.getClipBox()))};a.prototype.setClipAxes=function(){var a=this.chart.xAxis,b=this.chart.yAxis,c=(this.options.labels||[]).concat(this.options.shapes||[]).reduce(function(c,d){return[a[d&&d.point&&d.point.xAxis]||c[0],b[d&&d.point&&d.point.yAxis]||
c[1]]},[]);this.clipXAxis=c[0];this.clipYAxis=c[1]};a.prototype.getClipBox=function(){if(this.clipXAxis&&this.clipYAxis)return{x:this.clipXAxis.left,y:this.clipYAxis.top,width:this.clipXAxis.width,height:this.clipYAxis.height}};a.prototype.setLabelCollector=function(){var a=this;a.labelCollector=function(){return a.labels.reduce(function(a,b){b.options.allowOverlap||a.push(b.graphic);return a},[])};a.chart.labelCollectors.push(a.labelCollector)};a.prototype.setOptions=function(a){this.options=q(this.defaultOptions,
a)};a.prototype.redraw=function(a){this.linkPoints();this.graphic||this.render();this.clipRect&&this.clipRect.animate(this.getClipBox());this.redrawItems(this.shapes,a);this.redrawItems(this.labels,a);m.redraw.call(this,a)};a.prototype.redrawItems=function(a,b){for(var c=a.length;c--;)this.redrawItem(a[c],b)};a.prototype.renderItems=function(a){for(var b=a.length;b--;)this.renderItem(a[b])};a.prototype.render=function(){var a=this.chart.renderer;this.graphic=a.g("annotation").attr({opacity:0,zIndex:this.options.zIndex,
visibility:this.options.visible?"visible":"hidden"}).add();this.shapesGroup=a.g("annotation-shapes").add(this.graphic).clip(this.chart.plotBoxClip);this.labelsGroup=a.g("annotation-labels").attr({translateX:0,translateY:0}).add(this.graphic);this.addClipPaths();this.clipRect&&this.graphic.clip(this.clipRect);this.renderItems(this.shapes);this.renderItems(this.labels);this.addEvents();m.render.call(this)};a.prototype.setVisibility=function(a){var b=this.options;a=D(a,!b.visible);this.graphic.attr("visibility",
a?"visible":"hidden");a||this.setControlPointsVisibility(!1);b.visible=a};a.prototype.setControlPointsVisibility=function(a){var b=function(b){b.setControlPointsVisibility(a)};m.setControlPointsVisibility.call(this,a);this.shapes.forEach(b);this.labels.forEach(b)};a.prototype.destroy=function(){var a=this.chart,b=function(a){a.destroy()};this.labels.forEach(b);this.shapes.forEach(b);this.clipYAxis=this.clipXAxis=null;l(a.labelCollectors,this.labelCollector);e.destroy.call(this);m.destroy.call(this);
f(this,a)};a.prototype.remove=function(){return this.chart.removeAnnotation(this)};a.prototype.update=function(a,b){var c=this.chart,d=this.getLabelsAndShapesOptions(this.userOptions,a),f=c.annotations.indexOf(this);a=q(!0,this.userOptions,a);a.labels=d.labels;a.shapes=d.shapes;this.destroy();this.constructor(c,a);c.options.annotations[f]=a;this.isUpdating=!0;D(b,!0)&&c.redraw();n(this,"afterUpdate");this.isUpdating=!1};a.prototype.initShape=function(b,c){b=q(this.options.shapeOptions,{controlPointOptions:this.options.controlPointOptions},
b);c=new a.shapesMap[b.type](this,b,c);c.itemType="shape";this.shapes.push(c);return c};a.prototype.initLabel=function(a,b){a=q(this.options.labelOptions,{controlPointOptions:this.options.controlPointOptions},a);b=new t(this,a,b);b.itemType="label";this.labels.push(b);return b};a.prototype.redrawItem=function(a,b){a.linkPoints();a.shouldBeDrawn()?(a.graphic||this.renderItem(a),a.redraw(D(b,!0)&&a.graphic.placed),a.points.length&&this.adjustVisibility(a)):this.destroyItem(a)};a.prototype.adjustVisibility=
function(a){var b=!1,c=a.graphic;a.points.forEach(function(a){!1!==a.series.visible&&!1!==a.visible&&(b=!0)});b?"hidden"===c.visibility&&c.show():c.hide()};a.prototype.destroyItem=function(a){l(this[a.itemType+"s"],a);a.destroy()};a.prototype.renderItem=function(a){a.render("label"===a.itemType?this.labelsGroup:this.shapesGroup)};a.ControlPoint=d;a.MockPoint=h;a.shapesMap={rect:r,circle:k,path:x,image:b};a.types={};return a}();q(!0,A.prototype,m,e,q(A.prototype,{nonDOMEvents:["add","afterUpdate",
"drag","remove"],defaultOptions:{visible:!0,animation:{},draggable:"xy",labelOptions:{align:"center",allowOverlap:!1,backgroundColor:"rgba(0, 0, 0, 0.75)",borderColor:"black",borderRadius:3,borderWidth:1,className:"",crop:!1,formatter:function(){return B(this.y)?this.y:"Annotation label"},includeInDataExport:!0,overflow:"justify",padding:5,shadow:!1,shape:"callout",style:{fontSize:"11px",fontWeight:"normal",color:"contrast"},useHTML:!1,verticalAlign:"bottom",x:0,y:-16},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",
strokeWidth:1,fill:"rgba(0, 0, 0, 0.75)",r:0,snap:2},controlPointOptions:{symbol:"circle",width:10,height:10,style:{stroke:"black","stroke-width":2,fill:"white"},visible:!1,events:{}},events:{},zIndex:6}}));u.extendAnnotation=function(a,b,c,d){b=b||A;q(!0,a.prototype,b.prototype,c);a.prototype.defaultOptions=q(a.prototype.defaultOptions,d||{})};w(a,{initAnnotation:function(a){a=new (A.types[a.type]||A)(this,a);this.annotations.push(a);return a},addAnnotation:function(a,b){a=this.initAnnotation(a);
this.options.annotations.push(a.options);D(b,!0)&&(a.redraw(),a.graphic.attr({opacity:1}));return a},removeAnnotation:function(a){var b=this.annotations,c="annotations"===a.coll?a:v(b,function(b){return b.options.id===a});c&&(n(c,"remove"),l(this.options.annotations,c.options),l(b,c),c.destroy())},drawAnnotations:function(){this.plotBoxClip.attr(this.plotBox);this.annotations.forEach(function(a){a.redraw();a.graphic.animate({opacity:1},a.animationConfig)})}});a.collectionsWithUpdate.push("annotations");
a.collectionsWithInit.annotations=[a.addAnnotation];y(g,"afterInit",function(){this.annotations=[];this.options.annotations||(this.options.annotations=[])});a.callbacks.push(function(a){a.plotBoxClip=this.renderer.clipRect(this.plotBox);a.controlPointsGroup=a.renderer.g("control-points").attr({zIndex:99}).clip(a.plotBoxClip).add();a.options.annotations.forEach(function(b,c){if(!a.annotations.some(function(a){return a.options===b})){var d=a.initAnnotation(b);a.options.annotations[c]=d.options}});a.drawAnnotations();
y(a,"redraw",a.drawAnnotations);y(a,"destroy",function(){a.plotBoxClip.destroy();a.controlPointsGroup.destroy()});y(a,"exportData",function(b){var c,d,f,q,n,v,l,e,D=a.annotations,g=(this.options.exporting&&this.options.exporting.csv||{}).columnHeaderFormatter,h=!b.dataRows[1].xValues,w=null===(d=null===(c=a.options.lang)||void 0===c?void 0:c.exportData)||void 0===d?void 0:d.annotationHeader;c=function(a){if(g){var b=g(a);if(!1!==b)return b}b=w+" "+a;return h?{columnTitle:b,topLevelColumnTitle:b}:
b};var F=b.dataRows[0].length,k=null===(n=null===(q=null===(f=a.options.exporting)||void 0===f?void 0:f.csv)||void 0===q?void 0:q.annotations)||void 0===n?void 0:n.itemDelimiter,A=null===(e=null===(l=null===(v=a.options.exporting)||void 0===v?void 0:v.csv)||void 0===l?void 0:l.annotations)||void 0===e?void 0:e.join;D.forEach(function(a){a.options.labelOptions.includeInDataExport&&a.labels.forEach(function(a){if(a.options.text){var c=a.options.text;a.points.forEach(function(a){var d=a.x,q=a.series.xAxis?
a.series.xAxis.options.index:-1,f=!1;if(-1===q){a=b.dataRows[0].length;for(var n=Array(a),v=0;v<a;++v)n[v]="";n.push(c);n.xValues=[];n.xValues[q]=d;b.dataRows.push(n);f=!0}f||b.dataRows.forEach(function(a,b){!f&&a.xValues&&void 0!==q&&d===a.xValues[q]&&(A&&a.length>F?a[a.length-1]+=k+c:a.push(c),f=!0)});if(!f){a=b.dataRows[0].length;n=Array(a);for(v=0;v<a;++v)n[v]="";n[0]=d;n.push(c);n.xValues=[];void 0!==q&&(n.xValues[q]=d);b.dataRows.push(n)}})}})});var m=0;b.dataRows.forEach(function(a){m=Math.max(m,
a.length)});f=m-b.dataRows[0].length;for(q=0;q<f;q++)n=c(q+1),h?(b.dataRows[0].push(n.topLevelColumnTitle),b.dataRows[1].push(n.columnTitle)):b.dataRows[0].push(n)})});p(z.prototype,"onContainerMouseDown",function(a){this.chart.hasDraggedAnnotation||a.apply(this,Array.prototype.slice.call(arguments,1))});return u.Annotation=A});t(a,"Mixins/Navigation.js",[],function(){return{initUpdate:function(a){a.navigation||(a.navigation={updates:[],update:function(a,c){this.updates.forEach(function(g){g.update.call(g.context,
a,c)})}})},addUpdate:function(a,g){g.navigation||this.initUpdate(g);g.navigation.updates.push({update:a,context:g})}}});t(a,"Extensions/Annotations/NavigationBindings.js",[a["Extensions/Annotations/Annotations.js"],a["Core/Chart/Chart.js"],a["Mixins/Navigation.js"],a["Core/Globals.js"],a["Core/Utilities.js"]],function(a,g,m,r,k){function c(a){var b=a.prototype.defaultOptions.events&&a.prototype.defaultOptions.events.click;C(!0,a.prototype.defaultOptions.events,{click:function(a){var c=this,d=c.chart.navigationBindings,
f=d.activeAnnotation;b&&b.call(c,a);f!==c?(d.deselectAnnotation(),d.activeAnnotation=c,c.setControlPointsVisibility(!0),e(d,"showPopup",{annotation:c,formType:"annotation-toolbar",options:d.annotationToFields(c),onSubmit:function(a){var b={};"remove"===a.actionType?(d.activeAnnotation=!1,d.chart.removeAnnotation(c)):(d.fieldsToOptions(a.fields,b),d.deselectAnnotation(),a=b.typeOptions,"measure"===c.options.type&&(a.crosshairY.enabled=0!==a.crosshairY.strokeWidth,a.crosshairX.enabled=0!==a.crosshairX.strokeWidth),
c.update(b))}})):e(d,"closePopup");a.activeAnnotation=!0}})}var b=k.addEvent,t=k.attr,d=k.format,e=k.fireEvent,u=k.isArray,h=k.isFunction,z=k.isNumber,p=k.isObject,C=k.merge,y=k.objectEach,E=k.pick;k=k.setOptions;var f=r.doc,l=r.win,w=function(){function a(a,b){this.selectedButton=this.boundClassNames=void 0;this.chart=a;this.options=b;this.eventsToUnbind=[];this.container=f.getElementsByClassName(this.options.bindingsClassName||"")}a.prototype.initEvents=function(){var a=this,c=a.chart,d=a.container,
f=a.options;a.boundClassNames={};y(f.bindings||{},function(b){a.boundClassNames[b.className]=b});[].forEach.call(d,function(c){a.eventsToUnbind.push(b(c,"click",function(b){var d=a.getButtonEvents(c,b);d&&a.bindingsButtonClick(d.button,d.events,b)}))});y(f.events||{},function(c,d){h(c)&&a.eventsToUnbind.push(b(a,d,c,{passive:!1}))});a.eventsToUnbind.push(b(c.container,"click",function(b){!c.cancelClick&&c.isInsidePlot(b.chartX-c.plotLeft,b.chartY-c.plotTop)&&a.bindingsChartClick(this,b)}));a.eventsToUnbind.push(b(c.container,
r.isTouchDevice?"touchmove":"mousemove",function(b){a.bindingsContainerMouseMove(this,b)},r.isTouchDevice?{passive:!1}:void 0))};a.prototype.initUpdate=function(){var a=this;m.addUpdate(function(b){a.update(b)},this.chart)};a.prototype.bindingsButtonClick=function(a,b,c){var d=this.chart;this.selectedButtonElement&&(e(this,"deselectButton",{button:this.selectedButtonElement}),this.nextEvent&&(this.currentUserDetails&&"annotations"===this.currentUserDetails.coll&&d.removeAnnotation(this.currentUserDetails),
this.mouseMoveEvent=this.nextEvent=!1));this.selectedButton=b;this.selectedButtonElement=a;e(this,"selectButton",{button:a});b.init&&b.init.call(this,a,c);(b.start||b.steps)&&d.renderer.boxWrapper.addClass("highcharts-draw-mode")};a.prototype.bindingsChartClick=function(a,b){a=this.chart;var c=this.selectedButton;a=a.renderer.boxWrapper;var d;if(d=this.activeAnnotation&&!b.activeAnnotation&&b.target.parentNode){a:{d=b.target;var f=l.Element.prototype,q=f.matches||f.msMatchesSelector||f.webkitMatchesSelector,
n=null;if(f.closest)n=f.closest.call(d,".highcharts-popup");else{do{if(q.call(d,".highcharts-popup"))break a;d=d.parentElement||d.parentNode}while(null!==d&&1===d.nodeType)}d=n}d=!d}d&&e(this,"closePopup");c&&c.start&&(this.nextEvent?(this.nextEvent(b,this.currentUserDetails),this.steps&&(this.stepIndex++,c.steps[this.stepIndex]?this.mouseMoveEvent=this.nextEvent=c.steps[this.stepIndex]:(e(this,"deselectButton",{button:this.selectedButtonElement}),a.removeClass("highcharts-draw-mode"),c.end&&c.end.call(this,
b,this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1,this.selectedButton=null))):(this.currentUserDetails=c.start.call(this,b),c.steps?(this.stepIndex=0,this.steps=!0,this.mouseMoveEvent=this.nextEvent=c.steps[this.stepIndex]):(e(this,"deselectButton",{button:this.selectedButtonElement}),a.removeClass("highcharts-draw-mode"),this.steps=!1,this.selectedButton=null,c.end&&c.end.call(this,b,this.currentUserDetails))))};a.prototype.bindingsContainerMouseMove=function(a,b){this.mouseMoveEvent&&
this.mouseMoveEvent(b,this.currentUserDetails)};a.prototype.fieldsToOptions=function(a,b){y(a,function(a,c){var d=parseFloat(a),f=c.split("."),l=b,q=f.length-1;!z(d)||a.match(/px/g)||c.match(/format/g)||(a=d);""!==a&&"undefined"!==a&&f.forEach(function(b,c){var d=E(f[c+1],"");q===c?l[b]=a:(l[b]||(l[b]=d.match(/\d/g)?[]:{}),l=l[b])})});return b};a.prototype.deselectAnnotation=function(){this.activeAnnotation&&(this.activeAnnotation.setControlPointsVisibility(!1),this.activeAnnotation=!1)};a.prototype.annotationToFields=
function(b){function c(a,f,l,n){if(l&&a&&-1===g.indexOf(f)&&(0<=(l.indexOf&&l.indexOf(f))||l[f]||!0===l))if(u(a))n[f]=[],a.forEach(function(a,b){p(a)?(n[f][b]={},y(a,function(a,d){c(a,d,e[f],n[f][b])})):c(a,0,e[f],n[f])});else if(p(a)){var q={};u(n)?(n.push(q),q[f]={},q=q[f]):n[f]=q;y(a,function(a,b){c(a,b,0===f?l:e[f],q)})}else"format"===f?n[f]=[d(a,b.labels[0].points[0]).toString(),"text"]:u(n)?n.push([a,v(a)]):n[f]=[a,v(a)]}var f=b.options,l=a.annotationsEditable,e=l.nestedOptions,v=this.utils.getFieldType,
n=E(f.type,f.shapes&&f.shapes[0]&&f.shapes[0].type,f.labels&&f.labels[0]&&f.labels[0].itemType,"label"),g=a.annotationsNonEditable[f.langKey]||[],h={langKey:f.langKey,type:n};y(f,function(a,b){"typeOptions"===b?(h[b]={},y(f[b],function(a,d){c(a,d,e,h[b],!0)})):c(a,b,l[n],h)});return h};a.prototype.getClickedClassNames=function(a,b){var c=b.target;b=[];for(var d;c&&((d=t(c,"class"))&&(b=b.concat(d.split(" ").map(function(a){return[a,c]}))),c=c.parentNode,c!==a););return b};a.prototype.getButtonEvents=
function(a,b){var c=this,d;this.getClickedClassNames(a,b).forEach(function(a){c.boundClassNames[a[0]]&&!d&&(d={events:c.boundClassNames[a[0]],button:a[1]})});return d};a.prototype.update=function(a){this.options=C(!0,this.options,a);this.removeEvents();this.initEvents()};a.prototype.removeEvents=function(){this.eventsToUnbind.forEach(function(a){a()})};a.prototype.destroy=function(){this.removeEvents()};a.annotationsEditable={nestedOptions:{labelOptions:["style","format","backgroundColor"],labels:["style"],
label:["style"],style:["fontSize","color"],background:["fill","strokeWidth","stroke"],innerBackground:["fill","strokeWidth","stroke"],outerBackground:["fill","strokeWidth","stroke"],shapeOptions:["fill","strokeWidth","stroke"],shapes:["fill","strokeWidth","stroke"],line:["strokeWidth","stroke"],backgroundColors:[!0],connector:["fill","strokeWidth","stroke"],crosshairX:["strokeWidth","stroke"],crosshairY:["strokeWidth","stroke"]},circle:["shapes"],verticalLine:[],label:["labelOptions"],measure:["background",
"crosshairY","crosshairX"],fibonacci:[],tunnel:["background","line","height"],pitchfork:["innerBackground","outerBackground"],rect:["shapes"],crookedLine:[],basicAnnotation:["shapes","labelOptions"]};a.annotationsNonEditable={rectangle:["crosshairX","crosshairY","label"]};return a}();w.prototype.utils={updateRectSize:function(a,b){var c=b.chart,d=b.options.typeOptions,f=c.pointer.getCoordinates(a);a=f.xAxis[0].value-d.point.x;d=d.point.y-f.yAxis[0].value;b.update({typeOptions:{background:{width:c.inverted?
d:a,height:c.inverted?a:d}}})},getFieldType:function(a){return{string:"text",number:"number","boolean":"checkbox"}[typeof a]}};g.prototype.initNavigationBindings=function(){var a=this.options;a&&a.navigation&&a.navigation.bindings&&(this.navigationBindings=new w(this,a.navigation),this.navigationBindings.initEvents(),this.navigationBindings.initUpdate())};b(g,"load",function(){this.initNavigationBindings()});b(g,"destroy",function(){this.navigationBindings&&this.navigationBindings.destroy()});b(w,
"deselectButton",function(){this.selectedButtonElement=null});b(a,"remove",function(){this.chart.navigationBindings&&this.chart.navigationBindings.deselectAnnotation()});r.Annotation&&(c(a),y(a.types,function(a){c(a)}));k({lang:{navigation:{popup:{simpleShapes:"Simple shapes",lines:"Lines",circle:"Circle",rectangle:"Rectangle",label:"Label",shapeOptions:"Shape options",typeOptions:"Details",fill:"Fill",format:"Text",strokeWidth:"Line width",stroke:"Line color",title:"Title",name:"Name",labelOptions:"Label options",
labels:"Labels",backgroundColor:"Background color",backgroundColors:"Background colors",borderColor:"Border color",borderRadius:"Border radius",borderWidth:"Border width",style:"Style",padding:"Padding",fontSize:"Font size",color:"Color",height:"Height",shapes:"Shape options"}}},navigation:{bindingsClassName:"highcharts-bindings-container",bindings:{circleAnnotation:{className:"highcharts-circle-annotation",start:function(a){a=this.chart.pointer.getCoordinates(a);var b=this.chart.options.navigation;
return this.chart.addAnnotation(C({langKey:"circle",type:"basicAnnotation",shapes:[{type:"circle",point:{xAxis:0,yAxis:0,x:a.xAxis[0].value,y:a.yAxis[0].value},r:5}]},b.annotationsOptions,b.bindings.circleAnnotation.annotationsOptions))},steps:[function(a,b){var c=b.options.shapes[0].point,d=this.chart.xAxis[0].toPixels(c.x);c=this.chart.yAxis[0].toPixels(c.y);var f=this.chart.inverted;b.update({shapes:[{r:Math.max(Math.sqrt(Math.pow(f?c-a.chartX:d-a.chartX,2)+Math.pow(f?d-a.chartY:c-a.chartY,2)),
5)}]})}]},rectangleAnnotation:{className:"highcharts-rectangle-annotation",start:function(a){var b=this.chart.pointer.getCoordinates(a);a=this.chart.options.navigation;var c=b.xAxis[0].value;b=b.yAxis[0].value;return this.chart.addAnnotation(C({langKey:"rectangle",type:"basicAnnotation",shapes:[{type:"path",points:[{xAxis:0,yAxis:0,x:c,y:b},{xAxis:0,yAxis:0,x:c,y:b},{xAxis:0,yAxis:0,x:c,y:b},{xAxis:0,yAxis:0,x:c,y:b}]}]},a.annotationsOptions,a.bindings.rectangleAnnotation.annotationsOptions))},steps:[function(a,
b){var c=b.options.shapes[0].points,d=this.chart.pointer.getCoordinates(a);a=d.xAxis[0].value;d=d.yAxis[0].value;c[1].x=a;c[2].x=a;c[2].y=d;c[3].y=d;b.update({shapes:[{points:c}]})}]},labelAnnotation:{className:"highcharts-label-annotation",start:function(a){a=this.chart.pointer.getCoordinates(a);var b=this.chart.options.navigation;return this.chart.addAnnotation(C({langKey:"label",type:"basicAnnotation",labelOptions:{format:"{y:.2f}"},labels:[{point:{xAxis:0,yAxis:0,x:a.xAxis[0].value,y:a.yAxis[0].value},
overflow:"none",crop:!0}]},b.annotationsOptions,b.bindings.labelAnnotation.annotationsOptions))}}},events:{},annotationsOptions:{animation:{defer:0}}}});b(w,"closePopup",function(){this.deselectAnnotation()});return w});t(a,"Extensions/Annotations/Popup.js",[a["Core/Globals.js"],a["Extensions/Annotations/NavigationBindings.js"],a["Core/Pointer.js"],a["Core/Utilities.js"]],function(a,g,m,r){var c=a.isFirefox,t=r.addEvent,b=r.createElement,B=r.defined,d=r.fireEvent,e=r.getOptions,u=r.isArray,h=r.isObject,
z=r.isString,p=r.objectEach,C=r.pick,y=r.stableSort;r=r.wrap;var E=/\d/g;r(m.prototype,"onContainerMouseDown",function(a,b){var c=b.target&&b.target.className;z(c)&&0<=c.indexOf("highcharts-popup-field")||a.apply(this,Array.prototype.slice.call(arguments,1))});a.Popup=function(a,b,c){this.init(a,b,c)};a.Popup.prototype={init:function(a,c,d){this.chart=d;this.container=b("div",{className:"highcharts-popup"},null,a);this.lang=this.getLangpack();this.iconsURL=c;this.addCloseBtn()},addCloseBtn:function(){var a=
this;var c=b("div",{className:"highcharts-popup-close"},null,this.container);c.style["background-image"]="url("+this.iconsURL+"close.svg)";["click","touchstart"].forEach(function(b){t(c,b,function(){d(a.chart.navigationBindings,"closePopup")})})},addColsContainer:function(a){var c=b("div",{className:"highcharts-popup-lhs-col"},null,a);a=b("div",{className:"highcharts-popup-rhs-col"},null,a);b("div",{className:"highcharts-popup-rhs-col-wrapper"},null,a);return{lhsCol:c,rhsCol:a}},addInput:function(a,
c,d,e){var f=a.split(".");f=f[f.length-1];var l=this.lang;c="highcharts-"+c+"-"+f;c.match(E)||b("label",{innerHTML:l[f]||f,htmlFor:c},null,d);b("input",{name:c,value:e[0],type:e[1],className:"highcharts-popup-field"},null,d).setAttribute("highcharts-data-name",a)},addButton:function(a,c,d,e,g){var f=this,l=this.closePopup,h=this.getFields;var n=b("button",{innerHTML:c},null,a);["click","touchstart"].forEach(function(a){t(n,a,function(){l.call(f);return e(h(g,d))})});return n},getFields:function(a,
b){var c=a.querySelectorAll("input"),d=a.querySelectorAll("#highcharts-select-series > option:checked")[0];a=a.querySelectorAll("#highcharts-select-volume > option:checked")[0];var f,e;var l={actionType:b,linkedTo:d&&d.getAttribute("value"),fields:{}};[].forEach.call(c,function(a){e=a.getAttribute("highcharts-data-name");(f=a.getAttribute("highcharts-data-series-id"))?l.seriesId=a.value:e?l.fields[e]=a.value:l.type=a.value});a&&(l.fields["params.volumeSeriesID"]=a.getAttribute("value"));return l},
showPopup:function(){var a=this.container,b=a.querySelectorAll(".highcharts-popup-close")[0];a.innerHTML="";0<=a.className.indexOf("highcharts-annotation-toolbar")&&(a.classList.remove("highcharts-annotation-toolbar"),a.removeAttribute("style"));a.appendChild(b);a.style.display="block"},closePopup:function(){this.popup.container.style.display="none"},showForm:function(a,b,c,d){this.popup=b.navigationBindings.popup;this.showPopup();"indicators"===a&&this.indicators.addForm.call(this,b,c,d);"annotation-toolbar"===
a&&this.annotations.addToolbar.call(this,b,c,d);"annotation-edit"===a&&this.annotations.addForm.call(this,b,c,d);"flag"===a&&this.annotations.addForm.call(this,b,c,d,!0)},getLangpack:function(){return e().lang.navigation.popup},annotations:{addToolbar:function(a,c,d){var f=this,e=this.lang,l=this.popup.container,g=this.showForm;-1===l.className.indexOf("highcharts-annotation-toolbar")&&(l.className+=" highcharts-annotation-toolbar");l.style.top=a.plotTop+10+"px";b("span",{innerHTML:C(e[c.langKey]||
c.langKey,c.shapes&&c.shapes[0].type)},null,l);var h=this.addButton(l,e.removeButton||"remove","remove",d,l);h.className+=" highcharts-annotation-remove-button";h.style["background-image"]="url("+this.iconsURL+"destroy.svg)";h=this.addButton(l,e.editButton||"edit","edit",function(){g.call(f,"annotation-edit",a,c,d)},l);h.className+=" highcharts-annotation-edit-button";h.style["background-image"]="url("+this.iconsURL+"edit.svg)"},addForm:function(a,c,d,e){var f=this.popup.container,l=this.lang;b("h2",
{innerHTML:l[c.langKey]||c.langKey,className:"highcharts-popup-main-title"},null,f);var h=b("div",{className:"highcharts-popup-lhs-col highcharts-popup-lhs-full"},null,f);var g=b("div",{className:"highcharts-popup-bottom-row"},null,f);this.annotations.addFormFields.call(this,h,a,"",c,[],!0);this.addButton(g,e?l.addButton||"add":l.saveButton||"save",e?"add":"save",d,f)},addFormFields:function(a,d,e,g,n,q){var f=this,l=this.annotations.addFormFields,k=this.addInput,m=this.lang,v,w;p(g,function(b,c){v=
""!==e?e+"."+c:c;h(b)&&(!u(b)||u(b)&&h(b[0])?(w=m[c]||c,w.match(E)||n.push([!0,w,a]),l.call(f,a,d,v,b,n,!1)):n.push([f,v,"annotation",a,b]))});q&&(y(n,function(a){return a[1].match(/format/g)?-1:1}),c&&n.reverse(),n.forEach(function(a){!0===a[0]?b("span",{className:"highcharts-annotation-title",innerHTML:a[1]},null,a[2]):k.apply(a[0],a.splice(1))}))}},indicators:{addForm:function(a,b,c){var d=this.indicators,f=this.lang;this.tabs.init.call(this,a);b=this.popup.container.querySelectorAll(".highcharts-tab-item-content");
this.addColsContainer(b[0]);d.addIndicatorList.call(this,a,b[0],"add");var e=b[0].querySelectorAll(".highcharts-popup-rhs-col")[0];this.addButton(e,f.addButton||"add","add",c,e);this.addColsContainer(b[1]);d.addIndicatorList.call(this,a,b[1],"edit");e=b[1].querySelectorAll(".highcharts-popup-rhs-col")[0];this.addButton(e,f.saveButton||"save","edit",c,e);this.addButton(e,f.removeButton||"remove","remove",c,e)},addIndicatorList:function(a,c,d){var f=this,e=c.querySelectorAll(".highcharts-popup-lhs-col")[0];
c=c.querySelectorAll(".highcharts-popup-rhs-col")[0];var g="edit"===d,h=g?a.series:a.options.plotOptions,l=this.indicators.addFormFields,k;var m=b("ul",{className:"highcharts-indicator-list"},null,e);var w=c.querySelectorAll(".highcharts-popup-rhs-col-wrapper")[0];p(h,function(c,d){var e=c.options;if(c.params||e&&e.params){var n=f.indicators.getNameType(c,d),q=n.type;k=b("li",{className:"highcharts-indicator-list",innerHTML:n.name},null,m);["click","touchstart"].forEach(function(d){t(k,d,function(){l.call(f,
a,g?c:h[q],n.type,w);g&&c.options&&b("input",{type:"hidden",name:"highcharts-id-"+q,value:c.options.id},null,w).setAttribute("highcharts-data-series-id",c.options.id)})})}});0<m.childNodes.length&&m.childNodes[0].click()},getNameType:function(b,c){var d=b.options,f=a.seriesTypes;f=f[c]&&f[c].prototype.nameBase||c.toUpperCase();d&&d.type&&(c=b.options.type,f=b.name);return{name:f,type:c}},listAllSeries:function(a,c,d,e,g){a="highcharts-"+c+"-type-"+a;var f;b("label",{innerHTML:this.lang[c]||c,htmlFor:a},
null,e);var h=b("select",{name:a,className:"highcharts-popup-field"},null,e);h.setAttribute("id","highcharts-select-"+c);d.series.forEach(function(a){f=a.options;!f.params&&f.id&&"highcharts-navigator-series"!==f.id&&b("option",{innerHTML:f.name||f.id,value:f.id},null,h)});B(g)&&(h.value=g)},addFormFields:function(a,c,d,e){var f=c.params||c.options.params,g=this.indicators.getNameType;e.innerHTML="";b("h3",{className:"highcharts-indicator-title",innerHTML:g(c,d).name},null,e);b("input",{type:"hidden",
name:"highcharts-type-"+d,value:d},null,e);this.indicators.listAllSeries.call(this,d,"series",a,e,c.linkedParent&&f.volumeSeriesID);f.volumeSeriesID&&this.indicators.listAllSeries.call(this,d,"volume",a,e,c.linkedParent&&c.linkedParent.options.id);this.indicators.addParamInputs.call(this,a,"params",f,d,e)},addParamInputs:function(a,b,c,d,e){var f=this,g=this.indicators.addParamInputs,l=this.addInput,k;p(c,function(c,n){k=b+"."+n;h(c)?g.call(f,a,k,c,d,e):"params.volumeSeriesID"!==k&&l.call(f,k,d,e,
[c,"text"])})},getAmount:function(){var a=0;this.series.forEach(function(b){var c=b.options;(b.params||c&&c.params)&&a++});return a}},tabs:{init:function(a){var b=this.tabs;a=this.indicators.getAmount.call(a);var c=b.addMenuItem.call(this,"add");b.addMenuItem.call(this,"edit",a);b.addContentItem.call(this,"add");b.addContentItem.call(this,"edit");b.switchTabs.call(this,a);b.selectTab.call(this,c,0)},addMenuItem:function(a,c){var d=this.popup.container,e="highcharts-tab-item",f=this.lang;0===c&&(e+=
" highcharts-tab-disabled");c=b("span",{innerHTML:f[a+"Button"]||a,className:e},null,d);c.setAttribute("highcharts-data-tab-type",a);return c},addContentItem:function(){return b("div",{className:"highcharts-tab-item-content"},null,this.popup.container)},switchTabs:function(a){var b=this,c;this.popup.container.querySelectorAll(".highcharts-tab-item").forEach(function(d,e){c=d.getAttribute("highcharts-data-tab-type");"edit"===c&&0===a||["click","touchstart"].forEach(function(a){t(d,a,function(){b.tabs.deselectAll.call(b);
b.tabs.selectTab.call(b,this,e)})})})},selectTab:function(a,b){var c=this.popup.container.querySelectorAll(".highcharts-tab-item-content");a.className+=" highcharts-tab-item-active";c[b].className+=" highcharts-tab-item-show"},deselectAll:function(){var a=this.popup.container,b=a.querySelectorAll(".highcharts-tab-item");a=a.querySelectorAll(".highcharts-tab-item-content");var c;for(c=0;c<b.length;c++)b[c].classList.remove("highcharts-tab-item-active"),a[c].classList.remove("highcharts-tab-item-show")}}};
t(g,"showPopup",function(b){this.popup||(this.popup=new a.Popup(this.chart.container,this.chart.options.navigation.iconsURL||this.chart.options.stockTools&&this.chart.options.stockTools.gui.iconsURL||"https://code.highcharts.com/9.0.1/gfx/stock-icons/",this.chart));this.popup.showForm(b.formType,this.chart,b.options,b.onSubmit)});t(g,"closePopup",function(){this.popup&&this.popup.closePopup()})});t(a,"masters/modules/annotations.src.js",[],function(){})});
//# sourceMappingURL=annotations.js.map