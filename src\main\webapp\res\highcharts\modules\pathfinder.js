/*
 Highcharts Gantt JS v9.0.1 (2021-02-15)

 Pathfinder

 (c) 2016-2021 ystein Moseng

 License: www.highcharts.com/license
*/
(function(a){"object"===typeof module&&module.exports?(a["default"]=a,module.exports=a):"function"===typeof define&&define.amd?define("highcharts/modules/pathfinder",["highcharts"],function(z){a(z);a.Highcharts=z;return a}):a("undefined"!==typeof Highcharts?Highcharts:void 0)})(function(a){function z(a,q,m,r){a.hasOwnProperty(q)||(a[q]=r.apply(null,m))}a=a?a._modules:{};z(a,"Extensions/ArrowSymbols.js",[a["Core/Renderer/SVG/SVGRenderer.js"]],function(a){a.prototype.symbols.arrow=function(a,m,r,n){return[["M",
a,m+n/2],["L",a+r,m],["L",a,m+n/2],["L",a+r,m+n]]};a.prototype.symbols["arrow-half"]=function(q,m,r,n){return a.prototype.symbols.arrow(q,m,r/2,n)};a.prototype.symbols["triangle-left"]=function(a,m,r,n){return[["M",a+r,m],["L",a,m+n/2],["L",a+r,m+n],["Z"]]};a.prototype.symbols["arrow-filled"]=a.prototype.symbols["triangle-left"];a.prototype.symbols["triangle-left-half"]=function(q,m,r,n){return a.prototype.symbols["triangle-left"](q,m,r/2,n)};a.prototype.symbols["arrow-filled-half"]=a.prototype.symbols["triangle-left-half"]});
z(a,"Gantt/Connection.js",[a["Core/Globals.js"],a["Core/Options.js"],a["Core/Series/Point.js"],a["Core/Utilities.js"]],function(a,q,m,r){function n(b){var e=b.shapeArgs;return e?{xMin:e.x,xMax:e.x+e.width,yMin:e.y,yMax:e.y+e.height}:(e=b.graphic&&b.graphic.getBBox())?{xMin:b.plotX-e.width/2,xMax:b.plotX+e.width/2,yMin:b.plotY-e.height/2,yMax:b.plotY+e.height/2}:null}"";var y=r.defined,z=r.error,x=r.extend,A=r.merge,B=r.objectEach,h=a.deg2rad,c=Math.max,l=Math.min;x(q.defaultOptions,{connectors:{type:"straight",
lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}});q=function(){function b(e,t,g){this.toPoint=this.pathfinder=this.graphics=this.fromPoint=this.chart=void 0;this.init(e,t,g)}b.prototype.init=function(e,t,g){this.fromPoint=e;this.toPoint=t;this.options=g;this.chart=e.series.chart;this.pathfinder=this.chart.pathfinder};b.prototype.renderPath=function(e,t,g){var b=this.chart,h=b.styledMode,c=
b.pathfinder,d=!b.options.chart.forExport&&!1!==g,f=this.graphics&&this.graphics.path;c.group||(c.group=b.renderer.g().addClass("highcharts-pathfinder-group").attr({zIndex:-1}).add(b.seriesGroup));c.group.translate(b.plotLeft,b.plotTop);f&&f.renderer||(f=b.renderer.path().add(c.group),h||f.attr({opacity:0}));f.attr(t);e={d:e};h||(e.opacity=1);f[d?"animate":"attr"](e,g);this.graphics=this.graphics||{};this.graphics.path=f};b.prototype.addMarker=function(e,b,g){var c=this.fromPoint.series.chart,t=c.pathfinder;
c=c.renderer;var a="start"===e?this.fromPoint:this.toPoint,d=a.getPathfinderAnchorPoint(b);if(b.enabled&&((g="start"===e?g[1]:g[g.length-2])&&"M"===g[0]||"L"===g[0])){g={x:g[1],y:g[2]};g=a.getRadiansToVector(g,d);d=a.getMarkerVector(g,b.radius,d);g=-g/h;if(b.width&&b.height){var f=b.width;var w=b.height}else f=w=2*b.radius;this.graphics=this.graphics||{};d={x:d.x-f/2,y:d.y-w/2,width:f,height:w,rotation:g,rotationOriginX:d.x,rotationOriginY:d.y};this.graphics[e]?this.graphics[e].animate(d):(this.graphics[e]=
c.symbol(b.symbol).addClass("highcharts-point-connecting-path-"+e+"-marker").attr(d).add(t.group),c.styledMode||this.graphics[e].attr({fill:b.color||this.fromPoint.color,stroke:b.lineColor,"stroke-width":b.lineWidth,opacity:0}).animate({opacity:1},a.series.options.animation))}};b.prototype.getPath=function(b){var e=this.pathfinder,c=this.chart,h=e.algorithms[b.type],a=e.chartObstacles;if("function"!==typeof h)return z('"'+b.type+'" is not a Pathfinder algorithm.'),{path:[],obstacles:[]};h.requiresObstacles&&
!a&&(a=e.chartObstacles=e.getChartObstacles(b),c.options.connectors.algorithmMargin=b.algorithmMargin,e.chartObstacleMetrics=e.getObstacleMetrics(a));return h(this.fromPoint.getPathfinderAnchorPoint(b.startMarker),this.toPoint.getPathfinderAnchorPoint(b.endMarker),A({chartObstacles:a,lineObstacles:e.lineObstacles||[],obstacleMetrics:e.chartObstacleMetrics,hardBounds:{xMin:0,xMax:c.plotWidth,yMin:0,yMax:c.plotHeight},obstacleOptions:{margin:b.algorithmMargin},startDirectionX:e.getAlgorithmStartDirection(b.startMarker)},
b))};b.prototype.render=function(){var b=this.fromPoint,a=b.series,h=a.chart,F=h.pathfinder,p=A(h.options.connectors,a.options.connectors,b.options.connectors,this.options),m={};h.styledMode||(m.stroke=p.lineColor||b.color,m["stroke-width"]=p.lineWidth,p.dashStyle&&(m.dashstyle=p.dashStyle));m["class"]="highcharts-point-connecting-path highcharts-color-"+b.colorIndex;p=A(m,p);y(p.marker.radius)||(p.marker.radius=l(c(Math.ceil((p.algorithmMargin||8)/2)-1,1),5));b=this.getPath(p);h=b.path;b.obstacles&&
(F.lineObstacles=F.lineObstacles||[],F.lineObstacles=F.lineObstacles.concat(b.obstacles));this.renderPath(h,m,a.options.animation);this.addMarker("start",A(p.marker,p.startMarker),h);this.addMarker("end",A(p.marker,p.endMarker),h)};b.prototype.destroy=function(){this.graphics&&(B(this.graphics,function(b){b.destroy()}),delete this.graphics)};return b}();a.Connection=q;x(m.prototype,{getPathfinderAnchorPoint:function(b){var h=n(this);switch(b.align){case "right":var c="xMax";break;case "left":c="xMin"}switch(b.verticalAlign){case "top":var a=
"yMin";break;case "bottom":a="yMax"}return{x:c?h[c]:(h.xMin+h.xMax)/2,y:a?h[a]:(h.yMin+h.yMax)/2}},getRadiansToVector:function(b,h){var c;y(h)||(c=n(this))&&(h={x:(c.xMin+c.xMax)/2,y:(c.yMin+c.yMax)/2});return Math.atan2(h.y-b.y,b.x-h.x)},getMarkerVector:function(b,h,c){var a=2*Math.PI,e=n(this),l=e.xMax-e.xMin,m=e.yMax-e.yMin,d=Math.atan2(m,l),f=!1;l/=2;var w=m/2,J=e.xMin+l;e=e.yMin+w;for(var G=J,H=e,k=1,u=1;b<-Math.PI;)b+=a;for(;b>Math.PI;)b-=a;a=Math.tan(b);b>-d&&b<=d?(u=-1,f=!0):b>d&&b<=Math.PI-
d?u=-1:b>Math.PI-d||b<=-(Math.PI-d)?(k=-1,f=!0):k=-1;f?(G+=k*l,H+=u*l*a):(G+=m/(2*a)*k,H+=u*w);c.x!==J&&(G=c.x);c.y!==e&&(H=c.y);return{x:G+h*Math.cos(b),y:H-h*Math.sin(b)}}});return q});z(a,"Gantt/PathfinderAlgorithms.js",[a["Core/Utilities.js"]],function(a){function q(h,c,a){a=a||0;var b=h.length-1;c-=1e-7;for(var e,l;a<=b;)if(e=b+a>>1,l=c-h[e].xMin,0<l)a=e+1;else if(0>l)b=e-1;else return e;return 0<a?a-1:0}function m(a,c){for(var h=q(a,c.x+1)+1;h--;){var b;if(b=a[h].xMax>=c.x)b=a[h],b=c.x<=b.xMax&&
c.x>=b.xMin&&c.y<=b.yMax&&c.y>=b.yMin;if(b)return h}return-1}function r(a){var c=[];if(a.length){c.push(["M",a[0].start.x,a[0].start.y]);for(var h=0;h<a.length;++h)c.push(["L",a[h].end.x,a[h].end.y])}return c}function n(a,c){a.yMin=A(a.yMin,c.yMin);a.yMax=x(a.yMax,c.yMax);a.xMin=A(a.xMin,c.xMin);a.xMax=x(a.xMax,c.xMax)}var y=a.extend,z=a.pick,x=Math.min,A=Math.max,B=Math.abs;a=y(function(a,c,l){function b(a,b,c,d,h){a={x:a.x,y:a.y};a[b]=c[d||b]+(h||0);return a}function h(a,c,d){var f=B(c[d]-a[d+"Min"])>
B(c[d]-a[d+"Max"]);return b(c,d,a,d+(f?"Max":"Min"),f?1:-1)}var t=[],g=z(l.startDirectionX,B(c.x-a.x)>B(c.y-a.y))?"x":"y",n=l.chartObstacles,p=m(n,a);l=m(n,c);if(-1<l){var q=n[l];l=h(q,c,g);q={start:l,end:c};var d=l}else d=c;-1<p&&(n=n[p],l=h(n,a,g),t.push({start:a,end:l}),l[g]>=a[g]===l[g]>=d[g]&&(g="y"===g?"x":"y",c=a[g]<c[g],t.push({start:l,end:b(l,g,n,g+(c?"Max":"Min"),c?1:-1)}),g="y"===g?"x":"y"));a=t.length?t[t.length-1].end:a;l=b(a,g,d);t.push({start:a,end:l});g=b(l,"y"===g?"x":"y",d);t.push({start:l,
end:g});t.push(q);return{path:r(t),obstacles:t}},{requiresObstacles:!0});return{fastAvoid:y(function(a,c,l){function b(a,b,c){var d,f=a.x<b.x?1:-1;if(a.x<b.x){var w=a;var e=b}else w=b,e=a;if(a.y<b.y){var h=a;var v=b}else h=b,v=a;for(d=0>f?x(q(k,e.x),k.length-1):0;k[d]&&(0<f&&k[d].xMin<=e.x||0>f&&k[d].xMax>=w.x);){if(k[d].xMin<=e.x&&k[d].xMax>=w.x&&k[d].yMin<=v.y&&k[d].yMax>=h.y)return c?{y:a.y,x:a.x<b.x?k[d].xMin-1:k[d].xMax+1,obstacle:k[d]}:{x:a.x,y:a.y<b.y?k[d].yMin-1:k[d].yMax+1,obstacle:k[d]};
d+=f}return b}function e(a,d,c,f,e){var w=e.soft,h=e.hard,k=f?"x":"y",v={x:d.x,y:d.y},I={x:d.x,y:d.y};e=a[k+"Max"]>=w[k+"Max"];w=a[k+"Min"]<=w[k+"Min"];var g=a[k+"Max"]>=h[k+"Max"];h=a[k+"Min"]<=h[k+"Min"];var K=B(a[k+"Min"]-d[k]),l=B(a[k+"Max"]-d[k]);c=10>B(K-l)?d[k]<c[k]:l<K;I[k]=a[k+"Min"];v[k]=a[k+"Max"];a=b(d,I,f)[k]!==I[k];d=b(d,v,f)[k]!==v[k];c=a?d?c:!0:d?!1:c;c=w?e?c:!0:e?!1:c;return h?g?c:!0:g?!1:c}function h(a,c,f){if(a.x===c.x&&a.y===c.y)return[];var g=f?"x":"y",I=l.obstacleOptions.margin;
var u={soft:{xMin:w,xMax:J,yMin:G,yMax:H},hard:l.hardBounds};var v=m(k,a);if(-1<v){v=k[v];u=e(v,a,c,f,u);n(v,l.hardBounds);var C=f?{y:a.y,x:v[u?"xMax":"xMin"]+(u?1:-1)}:{x:a.x,y:v[u?"yMax":"yMin"]+(u?1:-1)};var E=m(k,C);-1<E&&(E=k[E],n(E,l.hardBounds),C[g]=u?A(v[g+"Max"]-I+1,(E[g+"Min"]+v[g+"Max"])/2):x(v[g+"Min"]+I-1,(E[g+"Max"]+v[g+"Min"])/2),a.x===C.x&&a.y===C.y?(d&&(C[g]=u?A(v[g+"Max"],E[g+"Max"])+1:x(v[g+"Min"],E[g+"Min"])-1),d=!d):d=!1);a=[{start:a,end:C}]}else g=b(a,{x:f?c.x:a.x,y:f?a.y:c.y},
f),a=[{start:a,end:{x:g.x,y:g.y}}],g[f?"x":"y"]!==c[f?"x":"y"]&&(u=e(g.obstacle,g,c,!f,u),n(g.obstacle,l.hardBounds),u={x:f?g.x:g.obstacle[u?"xMax":"xMin"]+(u?1:-1),y:f?g.obstacle[u?"yMax":"yMin"]+(u?1:-1):g.y},f=!f,a=a.concat(h({x:g.x,y:g.y},u,f)));return a=a.concat(h(a[a.length-1].end,c,!f))}function g(a,b,d){var c=x(a.xMax-b.x,b.x-a.xMin)<x(a.yMax-b.y,b.y-a.yMin);d=e(a,b,d,c,{soft:l.hardBounds,hard:l.hardBounds});return c?{y:b.y,x:a[d?"xMax":"xMin"]+(d?1:-1)}:{x:b.x,y:a[d?"yMax":"yMin"]+(d?1:-1)}}
var y=z(l.startDirectionX,B(c.x-a.x)>B(c.y-a.y)),p=y?"x":"y",D=[],d=!1,f=l.obstacleMetrics,w=x(a.x,c.x)-f.maxWidth-10,J=A(a.x,c.x)+f.maxWidth+10,G=x(a.y,c.y)-f.maxHeight-10,H=A(a.y,c.y)+f.maxHeight+10,k=l.chartObstacles;var u=q(k,w);f=q(k,J);k=k.slice(u,f+1);if(-1<(f=m(k,c))){var C=g(k[f],c,a);D.push({end:c,start:C});c=C}for(;-1<(f=m(k,c));)u=0>c[p]-a[p],C={x:c.x,y:c.y},C[p]=k[f][u?p+"Max":p+"Min"]+(u?1:-1),D.push({end:c,start:C}),c=C;a=h(a,c,y);a=a.concat(D.reverse());return{path:r(a),obstacles:a}},
{requiresObstacles:!0}),straight:function(a,c){return{path:[["M",a.x,a.y],["L",c.x,c.y]],obstacles:[{start:a,end:c}]}},simpleConnect:a}});z(a,"Gantt/Pathfinder.js",[a["Gantt/Connection.js"],a["Core/Chart/Chart.js"],a["Core/Globals.js"],a["Core/Options.js"],a["Core/Series/Point.js"],a["Core/Utilities.js"],a["Gantt/PathfinderAlgorithms.js"]],function(a,q,m,r,n,y,z){function x(a){var b=a.shapeArgs;return b?{xMin:b.x,xMax:b.x+b.width,yMin:b.y,yMax:b.y+b.height}:(b=a.graphic&&a.graphic.getBBox())?{xMin:a.plotX-
b.width/2,xMax:a.plotX+b.width/2,yMin:a.plotY-b.height/2,yMax:a.plotY+b.height/2}:null}function A(a){for(var b=a.length,c=0,d,e,g=[],k=function(a,b,c){c=t(c,10);var d=a.yMax+c>b.yMin-c&&a.yMin-c<b.yMax+c,f=a.xMax+c>b.xMin-c&&a.xMin-c<b.xMax+c,g=d?a.xMin>b.xMax?a.xMin-b.xMax:b.xMin-a.xMax:Infinity,e=f?a.yMin>b.yMax?a.yMin-b.yMax:b.yMin-a.yMax:Infinity;return f&&d?c?k(a,b,Math.floor(c/2)):Infinity:p(g,e)};c<b;++c)for(d=c+1;d<b;++d)e=k(a[c],a[d]),80>e&&g.push(e);g.push(80);return F(Math.floor(g.sort(function(a,
b){return a-b})[Math.floor(g.length/10)]/2-1),1)}function B(a){if(a.options.pathfinder||a.series.reduce(function(a,b){b.options&&e(!0,b.options.connectors=b.options.connectors||{},b.options.pathfinder);return a||b.options&&b.options.pathfinder},!1))e(!0,a.options.connectors=a.options.connectors||{},a.options.pathfinder),l('WARNING: Pathfinder options have been renamed. Use "chart.connectors" or "series.connectors" instead.')}"";var h=y.addEvent,c=y.defined,l=y.error,b=y.extend,e=y.merge,t=y.pick,
g=y.splat,F=Math.max,p=Math.min;b(r.defaultOptions,{connectors:{type:"straight",lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}});var D=function(){function b(a){this.lineObstacles=this.group=this.connections=this.chartObstacleMetrics=this.chartObstacles=this.chart=void 0;this.init(a)}b.prototype.init=function(a){this.chart=a;this.connections=[];h(a,"redraw",function(){this.pathfinder.update()})};
b.prototype.update=function(b){var c=this.chart,d=this,f=d.connections;d.connections=[];c.series.forEach(function(b){b.visible&&!b.options.isInternal&&b.points.forEach(function(b){var f=b.options;f&&f.dependency&&(f.connect=f.dependency);var e;f=b.options&&b.options.connect&&g(b.options.connect);b.visible&&!1!==b.isInside&&f&&f.forEach(function(f){e=c.get("string"===typeof f?f:f.to);e instanceof n&&e.series.visible&&e.visible&&!1!==e.isInside&&d.connections.push(new a(b,e,"string"===typeof f?{}:f))})})});
for(var e=0,k,h,l=f.length,m=d.connections.length;e<l;++e){h=!1;for(k=0;k<m;++k)if(f[e].fromPoint===d.connections[k].fromPoint&&f[e].toPoint===d.connections[k].toPoint){d.connections[k].graphics=f[e].graphics;h=!0;break}h||f[e].destroy()}delete this.chartObstacles;delete this.lineObstacles;d.renderConnections(b)};b.prototype.renderConnections=function(a){a?this.chart.series.forEach(function(a){var b=function(){var b=a.chart.pathfinder;(b&&b.connections||[]).forEach(function(b){b.fromPoint&&b.fromPoint.series===
a&&b.render()});a.pathfinderRemoveRenderEvent&&(a.pathfinderRemoveRenderEvent(),delete a.pathfinderRemoveRenderEvent)};!1===a.options.animation?b():a.pathfinderRemoveRenderEvent=h(a,"afterAnimate",b)}):this.connections.forEach(function(a){a.render()})};b.prototype.getChartObstacles=function(a){for(var b=[],d=this.chart.series,f=t(a.algorithmMargin,0),e,k=0,g=d.length;k<g;++k)if(d[k].visible&&!d[k].options.isInternal)for(var h=0,l=d[k].points.length,m;h<l;++h)m=d[k].points[h],m.visible&&(m=x(m))&&
b.push({xMin:m.xMin-f,xMax:m.xMax+f,yMin:m.yMin-f,yMax:m.yMax+f});b=b.sort(function(a,b){return a.xMin-b.xMin});c(a.algorithmMargin)||(e=a.algorithmMargin=A(b),b.forEach(function(a){a.xMin-=e;a.xMax+=e;a.yMin-=e;a.yMax+=e}));return b};b.prototype.getObstacleMetrics=function(a){for(var b=0,c=0,d,f,e=a.length;e--;)d=a[e].xMax-a[e].xMin,f=a[e].yMax-a[e].yMin,b<d&&(b=d),c<f&&(c=f);return{maxHeight:c,maxWidth:b}};b.prototype.getAlgorithmStartDirection=function(a){var b="top"!==a.verticalAlign&&"bottom"!==
a.verticalAlign;return"left"!==a.align&&"right"!==a.align?b?void 0:!1:b?!0:void 0};return b}();D.prototype.algorithms=z;m.Pathfinder=D;b(n.prototype,{getPathfinderAnchorPoint:function(a){var b=x(this);switch(a.align){case "right":var c="xMax";break;case "left":c="xMin"}switch(a.verticalAlign){case "top":var d="yMin";break;case "bottom":d="yMax"}return{x:c?b[c]:(b.xMin+b.xMax)/2,y:d?b[d]:(b.yMin+b.yMax)/2}},getRadiansToVector:function(a,b){var d;c(b)||(d=x(this))&&(b={x:(d.xMin+d.xMax)/2,y:(d.yMin+
d.yMax)/2});return Math.atan2(b.y-a.y,a.x-b.x)},getMarkerVector:function(a,b,c){var d=2*Math.PI,e=x(this),f=e.xMax-e.xMin,g=e.yMax-e.yMin,h=Math.atan2(g,f),l=!1;f/=2;var m=g/2,n=e.xMin+f;e=e.yMin+m;for(var p=n,q=e,r=1,t=1;a<-Math.PI;)a+=d;for(;a>Math.PI;)a-=d;d=Math.tan(a);a>-h&&a<=h?(t=-1,l=!0):a>h&&a<=Math.PI-h?t=-1:a>Math.PI-h||a<=-(Math.PI-h)?(r=-1,l=!0):r=-1;l?(p+=r*f,q+=t*f*d):(p+=g/(2*d)*r,q+=t*m);c.x!==n&&(p=c.x);c.y!==e&&(q=c.y);return{x:p+b*Math.cos(a),y:q-b*Math.sin(a)}}});q.prototype.callbacks.push(function(a){!1!==
a.options.connectors.enabled&&(B(a),this.pathfinder=new D(this),this.pathfinder.update(!0))});return D});z(a,"masters/modules/pathfinder.src.js",[],function(){})});
//# sourceMappingURL=pathfinder.js.map