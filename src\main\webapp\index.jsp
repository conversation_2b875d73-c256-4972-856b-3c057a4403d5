<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>任务数据</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="res/css/layui.css" type="text/css" rel="stylesheet">
    <link href="res/css/mytab.css" type="text/css" rel="stylesheet">
    <link href="res/css/hr.css" type="text/css" rel="stylesheet">
    <meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<meta http-equiv="Cache-control" content="no-cache">
	<meta http-equiv="Cache" content="no-cache">	
<style>
 xm-select > .xm-body .scroll-body {
    max-width: 500px;
};
.zte_buttoncolor {
  background-color: #ff0000; /* 红色 */
};

</style>
</head>
<body>
   <center style="padding-top: 20px">
        <div id="title"></div>
	</center>
	<div  id="jump"></div>
	
<hr class="hr-twill-colorful">
    <ul id="tabs">
      <li><a href="#" name="#tab1">任务概览</a></li>
      <li><a href="#"  onclick="tab2()" name="#tab2">基础性能</a></li>
      <li><a href="#"  onclick="tab3()" name="#tab3">历史数据</a></li>
      <li><a href="#" name="#tab4">其它信息</a></li>    
  	</ul>

  <div id="content">
      <div id="tab1">
          <h2 style="color:rgb(0, 142, 211)">【最新构建】<span style="color:rgb(0, 142, 211)"  class="layui-font-12" id="newbuild"></span></h2>
          <hr class="hr-shadow"> 
          <table class="layui-hide" id="test1"></table>
          <br><br>
		  <div id="container" style="min-width: 310px; height: 300px; margin: 0 auto"></div>
		  <hr class="hr-edge-weak">
          <h2 style="color:rgb(0, 142, 211)">【历史构建】</h2><hr class="hr-shadow"> 
          <div class="layui-form">
					<table class="layui-hide" id="test2"></table>
				</div>
				
      </div>
      <div id="tab2">
      <h2 style="color:rgb(0, 142, 211)">【基础性能 】  <span class="layui-font-12 layui-font-gray">【20个基础指标平均值表格】</span></h2> 
    	<hr class="hr-shadow"> 
    	<table style="padding:2%" class="layui-hide" id="test3"></table>
				<br><br>
		<h2 style="color:rgb(0, 142, 211)">【关键指标 】<span class="layui-font-12 layui-font-gray">【20个基础指标基于用例的绘图】</span></h2>
    	<hr class="hr-shadow"> 
    	<form class="layui-form" >
    	<div class="layui-form-item" style="padding:1%">
		        					<div class="layui-col-md7">
		        						<label class="layui-form-label" style="font-weight:bold">指标选择</label>
                                    <div class=layui-input-block>
				                      <div id="newbuildchart"></div>
				                  </div> </div> 
				                  <button  lay-submit lay-filter="comnew"  style="margin-left:2%"  class="layui-btn layui-bg-blue">查看</button>  
		                       	<hr >
		                       </div>	<!-- 新增选择框，默认四张图，类似自主分析 -->	
		                       <br>
		                       <div id="newselchart">  </div>
    	</form>		
      </div>
      <div id="tab3">
          <h2  style="color:rgb(0, 142, 211)">【数据下载】</h2><hr class="hr-shadow"> 
    	<form class="layui-form" >
    				<table class="layui-hide" id="test5"  lay-filter="dhis1"></table><!-- 历史数据下载 -->
    				<br><br>	
    				  <hr class="hr-edge-weak">
				<br><br>
			  <button id="taskmodel" class="layui-btn demo-dropdown-base layui-bg-blue" style="margin-left:90%">
    <span id="modelname">模板选择</span>
    <i class="layui-icon layui-icon-down layui-font-12"></i>
  </button>	
    	</form>	
				<br>
				<h2 style="color:rgb(0, 142, 211)">【基础性能】<span class="layui-font-12 layui-font-gray">【20个基础指标平均值表格】</span></h2> 
    			<hr class="hr-shadow"> 
    			<form class="layui-form " style="margin-left:-2%">
        					<div class="layui-form-item" >
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline layui-col-md4">
				                      <div id="hbnumber"></div>
				                      </div> 
				                  <button   lay-submit lay-filter="hbase" style="margin-left:1%" class="layui-btn  layui-bg-blue">查看</button>  		                          	  
		                       </div>
		                       </form> 
		                <form class="layui-form " style="margin-left:1%">
		                	<table class="layui-table" id="test10" style="margin-left:15%;max-height: 500px;"></table><!-- 历史构建20基础表 --> 
		                </form>        
		           <br><br>          
				<h2 style="color:rgb(0, 142, 211)">【自助分析】<span class="layui-font-12 layui-font-gray">历史构建图表绘制</span></h2>
				<hr class="hr-shadow"> 
				<form class="layui-form" style="margin-left:-2%">
        					<div class="layui-form-item " >
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline layui-col-md4">
				                      <div id="hnumber1"></div>
				                  </div>
		        						<label class="layui-form-label" style="margin-left:-2%;font-weight:bold">测试用例</label>
                                    <div class="layui-input-inline layui-col-md4">
				                      <div id="htestcase"></div>
				                  </div> 
		        						<label class="layui-form-label" style="font-weight:bold">测试小区</label>
                                    <div class="layui-input-inline layui-col-md4">
				                      <div id="htesetcell"></div>
				                  </div> 
		        						<label class="layui-form-label" style="font-weight:bold">测试指标</label>
                                    <div class="layui-input-inline layui-col-md4">
				                      <div id="htesttask" ></div>
				                  </div> 
                                	<button   lay-submit lay-filter="hana1"   style="margin-left:1%"class="layui-btn  layui-bg-blue" >分析</button>  
		        					
		                       </div><br>
		                       <div id="fhnum">  </div>
		                       </form>
		                      <hr class="hr-edge-weak">
		                       <br><br>
		         <h2 style="color:rgb(0, 142, 211)">【对比分析】<span class="layui-font-12 layui-font-gray">【历史构建Kpi数据对比】</span></h2>
				<hr class="hr-shadow">               
				<form class="layui-form" style="margin-left:-2%">
        					<div class="layui-form-item " >
		        				<label class="layui-form-label" style="margin-left:-0.5%;font-weight:bold">构建号</label>
                                    <div class="layui-input-inline layui-col-md4">
				                      <div id="hnumber2"></div>
				                  </div> 
		        					<label class="layui-form-label" style="margin-left:-2%;font-weight:bold">测试用例</label>
                                    <div class="layui-input-inline layui-col-md4">
				                      <div id="htestcase2"></div>
				                  </div>  
				                  <button   lay-submit lay-filter="hana2" style="margin-left:1%" class="layui-btn layui-bg-blue">对比</button>  
		                       </div>
		                       <br>
		                       <div id=helhcom  >
		                       		 <h5>KPI指标统计及打分</h5>
		                       <table class="layui-hide" id="test8" style="max-height: 500px;"></table><!-- 健康度打分表 -->
		                       <br>
		                        <div id="healthhcharts">  </div><!-- 健康度图 -->
		                        <br>
		                       <table class="layui-hide" id="test7"  lay-filter="hel2"></table><!-- 健康度数据表 -->
		                       </div>
		                       </form>  
      </div>
      <div id="tab4">
          <h2>待规划...........</h2>
      </div>
  </div>
  
  <p id="about">&nbsp;iWork测试平台.2024 </p>


<script type="text/javascript" src="res/jquery.min.js"></script>
<script type="text/javascript" src="res/layui.js"></script>
<script type="text/javascript" src="res/waterMark.js"></script>
<script type="text/javascript" src="res/highcharts/highcharts.js"></script>
<script type="text/javascript" src="res/xm-select/treeTable.js"></script>
<script type="text/javascript" src="res/xm-select/xm-select.js"></script>
<script type="text/javascript" src="res/init.js?v=8"></script>  
<!-- <script type="text/javascript" src="res/initlocal.js?v=3"></script>  -->
<!-- <script type="text/javascript" src="res/inittest.js?v=1"></script> -->
<script type="text/html" id="bar">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm"  lay-event="search" style="margin-left:2%" >搜索</button>
		    </div>
</script>
<script type="text/html" id="bar2">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload2" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm"  lay-event="search2" style="margin-left:2%" >搜索</button>
		    </div>
</script>
<script type="text/html" id="barlog">
<div class="layui-clear-space">
 <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="log">日志下载</a>
 
</div>
</script>
<script type="text/html" id="barmts">
   <a  class="layui-btn layui-btn-xs layui-bg-blue" lay-event="downdata" >MTS数据下载</a>
	</script>
	<script type="text/html" id="barkpi">
    {{#  if((temall==1)||(temall==2)){ }}
     <a id="kpibar" class="layui-btn layui-btn-xs layui-bg-blue layui-btn-disabled">KPI数据下载</a>
    {{#  } else { }}
    <a id="kpibar" class="layui-btn layui-btn-xs layui-bg-blue " lay-event="downdata2" >KPI数据下载</a>
    {{#  } }}
</script>
<script type="text/javascript">init()</script>
<script type="text/javascript">initdata()</script>
<script type="text/javascript">
	$(document).ready(function()
	{
		var user1 = "iWork";
		var user2 = "性能研发四部";
		var user3 = "ZTE";
		watermark({"watermark_txt0":user1,"watermark_txt1":user2,"watermark_txt2":user3});
	});
			
</script>     
</body>
</html>