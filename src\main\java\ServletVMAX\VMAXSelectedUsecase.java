package ServletVMAX;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;


import Dao.DaoCMCC;
import Dao.ResultSetToJsonArray;




/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/VMAXSelectedUsecase")
public class VMAXSelectedUsecase extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public VMAXSelectedUsecase() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

		// TODO Auto-generated method stub
		request.setCharacterEncoding("UTF-8");  
		response.setContentType("text/html;charset=utf-8");
		response.setCharacterEncoding("UTF-8");
		DaoCMCC ci1data = new DaoCMCC();
		String jobid= request.getParameter("jobid");
		String index= request.getParameter("index");
		String msqldata ="";
		JSONArray jajobdata = new JSONArray();
		JSONArray jacountall = new JSONArray();
		String buildnum = (index.substring(1)).replace('-',',');
		//buildnum = "20,21";
		msqldata = "SELECT gnbid,celllocalid,BUILD_NUMBER,usecaseid,useCaseName FROM iwork2.vmaxdata where jobid='"+jobid+"' and BUILD_NUMBER in("+buildnum+") group by gnbid,celllocalid,BUILD_NUMBER,usecaseid,useCaseName order by BUILD_NUMBER,usecaseid; ";	
		
		ResultSet rsci1data= ci1data.executeQuery(msqldata);		
		try {
			jajobdata = ResultSetToJsonArray.resultSetToJsonArray(rsci1data);
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		if(jajobdata.length()<1) {
			
		}else {
			String strdara = "";
			String strdarav = "";
			for(int i=0;i<jajobdata.length();i++) {
					 strdara =  jajobdata.getJSONObject(i).getString("BUILD_NUMBER") +'#' +jajobdata.getJSONObject(i).getString("gnbid")
							+'-' +jajobdata.getJSONObject(i).getString("celllocalid")+'#' +"case"+jajobdata.getJSONObject(i).getString("usecaseid")+'#' +jajobdata.getJSONObject(i).getString("useCaseName").replace("_", "-");
					 
					 strdarav =   jajobdata.getJSONObject(i).getString("BUILD_NUMBER") +'#' +jajobdata.getJSONObject(i).getString("gnbid")
								+'#' +jajobdata.getJSONObject(i).getString("celllocalid")
								+'#' +jajobdata.getJSONObject(i).getString("usecaseid")+'#' +jajobdata.getJSONObject(i).getString("useCaseName");
				
				 JSONObject obj = new JSONObject();
				 obj.put("name", strdara);
		         obj.put("value", strdarav);
		         jacountall.put(obj);
			}
		}		
		PrintWriter out = response.getWriter(); 
		out.print(jacountall);
		ci1data.close();
		out.flush();
		out.close(); 
	}




	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
	
	private int Lookcolumn(String tablename) {
		// TODO Auto-generated method stub
		DaoCMCC dao = new DaoCMCC();
		int countcol=0;
		String sql ="select count(*) \r\n"
				+ "from information_schema.COLUMNS \r\n"
				+ "where TABLE_SCHEMA='iwork2' and table_name='"+tablename+"'";
		ResultSet rs = dao.executeQuery(sql);
		try {
			while(rs.next()) {
				countcol = rs.getInt(1);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dao.close();
		return countcol;
	}
	
	private JSONArray Lookcolumnname(String tablename) {
		// TODO Auto-generated method stub
		DaoCMCC dao = new DaoCMCC();
		JSONArray colnameArray = new JSONArray();
		String sql = "SELECT COLUMN_NAME FROM information_schema.COLUMNS\r\n"
		+ "WHERE TABLE_SCHEMA = 'iwork2' AND TABLE_NAME = '"+tablename+"' ORDER BY ordinal_position;";
		ResultSet rs = dao.executeQuery(sql);
		try {
			while(rs.next()) {
				colnameArray.put(rs.getString(1));
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dao.close();
		return colnameArray;
	}
	
    private static void zipDirectory(File directory, String basePath, ZipOutputStream zos) throws Exception {
        File[] files = directory.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                zipDirectory(file,  file.getName(), zos);
            } else {
                byte[] buffer = new byte[1024];
                FileInputStream fis = new FileInputStream(file);
                zos.putNextEntry(new ZipEntry( file.getName()));
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, length);
                }
                zos.closeEntry();
                fis.close();
            }
        }
    }
}




