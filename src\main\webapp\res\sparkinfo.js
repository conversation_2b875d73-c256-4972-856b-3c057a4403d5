var padata = eval('('+parent.myjson+')');//父界面传递的任务信息
console.log(padata);


//初始页面的表格和图的数据传递和显示：
var datatable1 = [];//历史构建的表
var datatable2 = [];//历史构建的表
var datatable3 = [];//历史构建的表
var datatable4 = [];//历史构建的表
var datatable5 = [];//历史构建的表
var datatable6 = [];//历史构建的表
var datatable7 = [];//历史构建的表
var datatable8 = "";//历史构建的表



//----------------初始页面数据的ajax：
$.ajax({
	type: "get",
				   url:'querySparkInfo2',
				   async:false,
				   data: {jobid:padata.taskid,buildno:padata.buildno},
				    dataType:'json',
				   success: function(data){
//					console.log(data);
//					datatable1=data[0];
//					datatable2=data[1];
//					datatable3=data[2];
//					datatable4=data[3];
//					datatable5=data[4];
//					datatable6=data[5];
//					datatable7=data[6];
//					datatable8=data[7];
//
//					draw5ul();
//					draw5dl();
//					draw6ul();
//					draw6dl();
//					draw7();
//					
//					document.getElementById("text8").innerText=datatable8;
					console.log(data);
					
					var classify = data[0];
					if(data[0]=="VonrSummary"){
						document.getElementById("div1").style.display = "block";
						datatable1 = data[1];
						draw1();
					}else if(data[0]=="VolteSummary"){
						document.getElementById("div2").style.display = "block";

						datatable2 = data[1];
						draw2();
					}else if(data[0]=="NrTput"){
						document.getElementById("div3").style.display = "block";
						datatable3 = data[1];
						draw3();
					}else if(data[0]=="LteTput"){
						document.getElementById("div4").style.display = "block";
						datatable4 = data[1];
						draw4();
					}else if(data[0]=="NrLayuan"){
						document.getElementById("div5").style.display = "block";
						datatable5 = data[1];
						draw5ul();
						draw5dl();
					}else if(data[0]=="LteLayuan"){
						document.getElementById("div6").style.display = "block";
						datatable6 = data[1];
						draw6ul();
						draw6dl();
					}else if(data[0]=="FtpPapo"){
						document.getElementById("div7").style.display = "block";
						datatable7 = data[1];
						draw7();
					}else if(data[0]=="PingData"){
						document.getElementById("div8").style.display = "block";
						datatable8 = data[1];
						draw8();
//						document.getElementById("text8").innerText=datatable8;

						// 假设你的div有一个id为'myDiv'
//						var div = document.getElementById('div8');
//						// 创建一个img元素
//						var img = document.createElement('img');
//						 
//						// 设置img的src属性
//						img.src = 'https://wxiot.zte.com.cn/static/wxiot.png';
//						 
//						// 将img元素添加到div中
//						div.appendChild(img);
					}
				}
});

function draw1(){
	layui.use('table', function(){
//	console.log(datatable2);
	var treeTable = layui.treeTable; 
	var form = layui.form; 
 
  treeTable.render({//任务概览表格数据
    elem: '#VonrSummary'
    ,maxHeight: '500px'
    ,height:'200px'
    ,cols: [[
       {field:'日志名称',title: '日志名称',width:'10%'}
      ,{field:'平均SINR（dB）', title: '平均SINR(dB)',align: 'center',templet:function(d) {return d["平均SINR（dB）"].toFixed(4)}}
      ,{field:'平均RSRP（dBm）', title: '平均RSRP（dBm）',align: 'center',templet:function(d) {return d["平均RSRP（dBm）"].toFixed(4)}}

      ,{field:'呼叫成功率', title: '呼叫成功率', align: 'center',templet:function(d) {return d["呼叫成功率"].toFixed(4)}}
      ,{field:'主叫呼叫次数', title: '主叫呼叫次数',align: 'center',templet:function(d) {return d["主叫呼叫次数"].toFixed(4)}}
      ,{field:'VoNR丢包数',title: 'VoNR丢包数',align: 'center',templet:function(d) {return d["VoNR丢包数"].toFixed(4)}}
      ,{field:'VoNR总丢包率',title: 'VoNR总丢包率',align: 'center',templet:function(d) {return d["VoNR总丢包率"].toFixed(4)}}
      ,{field:'VoNR掉话率（按SIP_BYE-OK信令统计）', title: 'VoNR掉话率（按SIP_BYE-OK信令统计）',align: 'center',templet:function(d) {return d["VoNR掉话率（按SIP_BYE-OK信令统计）"].toFixed(4)}} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'主叫平均起呼时延（s）', title: '主叫平均起呼时延（s）',align: 'center',templet:function(d) {return d["主叫平均起呼时延（s）"].toFixed(4)}}
      ,{field:'VoNR掉话次数（按SIP_BYE-OK信令统计）', title: 'VoNR掉话次数（按SIP_BYE-OK信令统计）',align: 'center',templet:function(d) {return d["VoNR掉话次数（按SIP_BYE-OK信令统计）"].toFixed(4)}}
      ,{field:'VoNR总包数', title: 'VoNR总包数',align: 'center',templet:function(d) {return d["VoNR总包数"].toFixed(4)}}

    ]],
   data:datatable1,
   text:{
       none:'当前任务暂无历史构建数据！'},
   }); 	
  })
}


function draw2(){
	  layui.use('table', function(){
//	console.log(datatable2);
	var treeTable = layui.treeTable; 
	var form = layui.form; 
 
  treeTable.render({//任务概览表格数据
    elem: '#VolteSummary'
    ,maxHeight: '500px'
    ,height:'200px'
    ,cols: [[
       {field:'日志名称',title: '日志名称',width:'10%'}
      ,{field:'4G 平均SINR（dB）',title: '4G 平均SINR（dB）',align: 'center',templet:function(d) {return d["4G 平均SINR（dB）"].toFixed(4)}}
      ,{field:'4G平均RSRP（dBm）',title: '4G平均RSRP（dBm）',align: 'center',templet:function(d) {return d["4G平均RSRP（dBm）"].toFixed(4)}}
      ,{field:'VoLTE呼叫成功率', title: 'VoLTE呼叫成功率', align: 'center',templet:function(d) {return d["VoLTE呼叫成功率"].toFixed(4)}}
      ,{field:'VoLTE掉话次数', title: 'VoLTE掉话次数',align: 'center',templet:function(d) {return d["VoLTE掉话次数"].toFixed(4)}}
     
      ,{field:'VoLTE平均起呼时延（s）', title: 'VoLTE平均起呼时延（s）',align: 'center',templet:function(d) {return d["VoLTE平均起呼时延（s）"].toFixed(4)}} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'VoLTE掉话次率', title: 'VoLTE掉话次率',align: 'center',templet:function(d) {return d["VoLTE掉话次率"].toFixed(4)}}
      ,{field:'VoLTE平均丢包率%', title: 'VoLTE平均丢包率%',align: 'center',templet:function(d) {return d["VoLTE平均丢包率%"].toFixed(4)}}
      ,{field:'VoLTE呼叫次数', title: 'VoLTE呼叫次数',align: 'center',templet:function(d) {return d["VoLTE呼叫次数"].toFixed(4)}}
    ]],
   data:datatable2,
   text:{
       none:'当前任务暂无历史构建数据！'},
   }); 	
  })
}

  function draw3(){
	    layui.use('table', function(){
//	console.log(datatable2);
	var treeTable = layui.treeTable; 
	var form = layui.form; 
 
  treeTable.render({//任务概览表格数据
    elem: '#NrTput'
    ,maxHeight: '500px'
    ,height:'200px'
    ,cols: [[
       {field:'文件名',title: '文件名',width:'10%'}
     ,{field:'平均SSB RSRP ', title: '平均SSB RSRP',align: 'center',templet:function(d) {return d["平均SSB RSRP "].toFixed(4)}}
      ,{field:'平均SSB SINR ', title: '平均SSB SINR',align: 'center',templet:function(d) {return d["平均SSB SINR "].toFixed(4)}}
      ,{field:'上行MAC速率(Mbps)', title: '上行MAC速率(Mbps)',align: 'center',templet:function(d) {return d["上行MAC速率(Mbps)"].toFixed(4)}}
      ,{field:'下行MAC速率(Mbps)', title: '下行MAC速率(Mbps)',align: 'center',templet:function(d) {return d["下行MAC速率(Mbps)"].toFixed(4)}}
      ,{field:'上行MCSAvg', title: '上行MCSAvg', align: 'center',templet:function(d) {return d["上行MCSAvg"].toFixed(4)}}
      ,{field:'下行MCS Avg', title: '下行MCS Avg',align: 'center',templet:function(d) {return d["下行MCS Avg"].toFixed(4)}}
      ,{field:'上行iBLER(%)',title: '上行iBLER(%)',align: 'center',templet:function(d) {return d["上行iBLER(%)"].toFixed(4)}}
      ,{field:'下行iBLER(%)',title: '下行iBLER(%)',align: 'center',templet:function(d) {return d["下行iBLER(%)"].toFixed(4)}}
      ,{field:'上行平均RB', title: '上行平均RB',align: 'center',templet:function(d) {return d["上行平均RB"].toFixed(4)}} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'下行平均RB', title: '下行平均RB',align: 'center',templet:function(d) {return d["下行平均RB"].toFixed(4)}}
      ,{field:'上行行RANK Avg', title: '上行行RANK Avg',align: 'center',templet:function(d) {return d["上行行RANK Avg"].toFixed(4)}}
      ,{field:'下行RANK Avg', title: '下行RANK Avg',align: 'center',templet:function(d) {return d["下行RANK Avg"].toFixed(4)}}
      ,{field:'上行Grant Count', title: '上行Grant Count',align: 'center',templet:function(d) {return d["上行Grant Count"].toFixed(4)}}
      ,{field:'下行Grant Num', title: '下行Grant Num',align: 'center',templet:function(d) {return d["下行Grant Num"].toFixed(4)}}
      


    ]],
   data:datatable3,
   text:{
       none:'当前任务暂无历史构建数据！'},
   }); 	
  })
}

  function draw4(){
	    layui.use('table', function(){
//	console.log(datatable2);
	var treeTable = layui.treeTable; 
	var form = layui.form; 
 
  treeTable.render({//任务概览表格数据
    elem: '#LteTput'
    ,maxHeight: '500px'
    ,height:'200px'
    ,cols: [[
       {field:'文件名',title: '文件名',width:'10%'}
      ,{field:'平均RSRP ', title: '平均RSRP', align: 'center',templet:function(d) {return d["平均RSRP "].toFixed(4)}}
      ,{field:'平均RS-SINR ', title: '平均RS-SINR', align: 'center',templet:function(d) {return d["平均RS-SINR "].toFixed(4)}}

      ,{field:'上行MAC速率(Mbps)', title: '上行MAC速率(Mbps)', align: 'center',templet:function(d) {return d["上行MAC速率(Mbps)"].toFixed(4)}}
      ,{field:'下行MAC速率(Mbps)', title: '下行MAC速率(Mbps)',align: 'center',templet:function(d) {return d["下行MAC速率(Mbps)"].toFixed(4)}}
      ,{field:'上行平均RB',title: '上行平均RB',align: 'center',templet:function(d) {return d["上行平均RB"].toFixed(4)}}
      ,{field:'下行平均RB',title: '下行平均RB',align: 'center',templet:function(d) {return d["下行平均RB"].toFixed(4)}}
      ,{field:'上行Grant Num', title: '上行Grant Num',align: 'center',templet:function(d) {return d["上行Grant Num"].toFixed(4)}} //minWidth：局部定义当前单元格的最小宽度，layui 2.2.1 新增
      ,{field:'下行Grant Num', title: '下行Grant Num',align: 'center',templet:function(d) {return d["下行Grant Num"].toFixed(4)}}
      ,{field:'上行MCS Avg', title: '上行MCS Avg',align: 'center',templet:function(d) {return d["上行MCS Avg"].toFixed(4)}}
      ,{field:'下行MCS Avg', title: '下行MCS Avg',align: 'center',templet:function(d) {return d["下行MCS Avg"].toFixed(4)}}
      ,{field:'上行iBLER(%)', title: '上行iBLER(%)',align: 'center',templet:function(d) {return d["上行iBLER(%)"].toFixed(4)}}
      ,{field:'下行iBLER(%)', title: '下行iBLER(%)',align: 'center',templet:function(d) {return d["下行iBLER(%)"].toFixed(4)}}
//      ,{field:'边缘RS-SINR(CDF等于5%的值 ', title: '边缘RS-SINR(CDF等于5%的值 ', align: 'center',templet:function(d) {return d["边缘RS-SINR(CDF等于5%的值 "].toFixed(4)}}

    ]],
   data:datatable4,
   text:{
       none:'当前任务暂无历史构建数据！'},
   }); 	
  })
}

  
  
  function draw5ul(){
			var length1 =  datatable5.length;
			length1 = length1/2;
			for (let icc= 0; icc<length1; icc++) {
						if (icc% 2 === 0) {//偶数
							$('#draw5').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%'><div style='hight:300px' id='chart5ul"+icc+"'></div></div>");	
						}else{//奇数
							$('#draw5').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%;margin-left:1%'><div style='hight:300px' id='chart5ul"+icc+"' ></div></div>");
						}
					}
					for(let icf=0;icf<length1;icf++){
						    //将 x 和 y 数据组合成对象数组
//					    var data1 = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(function (x, i) {
//					        return { x: x, y: datatable5[icf*2+1]["NR UL MCS Avg Total"][i] };
//					    });
//					    var data2 = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(function (x, i) {
//					        return { x: x, y: datatable5[icf*2+1]["NR UL Avg Rank"][i] };
//					    });
//					    var data3 = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(function (x, i) {
//					        return { x: x, y: datatable5[icf*2+1]["NR UL MAC Throughput(Mbit/s)"][i] };
//					    });
		
						//动态增加图	
						//渲染增加的图：
						let roundedNumbers = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(number => parseFloat(number.toFixed(2)));

						Highcharts.chart('chart5ul'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+datatable5[icf*2]+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [
							{
								categories: roundedNumbers,
//								labels: {
//									  formatter: function() {
//										if(Number.isInteger(this.value)){
//												this.value = ""; 
//										}else{
//											
//										}
//				           return this.value
//				        },
//								rotation:90,
//					         style :{
//						fontSize: 8
//						}
//								},
//						 plotLines:listband,
						 	lineColor:'black'		
							}
						],
						yAxis: [{ // Primary yAxis
//								max:data[1][icf].y[0].max,
//								min:data[1][icf].y[0].min,
//								tickInterval:data[1][icf].y[0].tickInterval,
//								labels: {
//										format: '{value}',
//										style: {
//												color: Highcharts.getOptions().colors[0]
//										}
//								},
//								title: {
//										text: data[1][icf].y[0].label,
//										style: {
//												color: Highcharts.getOptions().colors[0]
//										}
//								}
								
						}
							],
						tooltip: {
						},
						series:[
							{name:"NR UL MCS Avg Total",data:datatable5[icf*2+1]["NR UL MCS Avg Total"]},
							{name:"NR UL Avg Rank",data:datatable5[icf*2+1]["NR UL Avg Rank"]},
							{name:"NR UL MAC Throughput(Mbit/s)",data:datatable5[icf*2+1]["NR UL MAC Throughput(Mbit/s)"]},
//							{name:"NR UL MCS Avg Total",data:data1},
//							{name:"NR UL Avg Rank",data:data2},
//							{name:"NR UL MAC Throughput(Mbit/s)",data:data3},
						
						],
				
				});	
					}
}


  function draw5dl(){
			var length1 =  datatable5.length;
			length1 = length1/2;
			for (let icc= 0; icc<length1; icc++) {
						if (icc% 2 === 0) {//偶数
							$('#draw5').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%'><div style='hight:300px' id='chart5dl"+icc+"'></div></div>");	
						}else{//奇数
							$('#draw5').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%;margin-left:1%'><div style='hight:300px' id='chart5dl"+icc+"' ></div></div>");
						}
					}
					for(let icf=0;icf<length1;icf++){
						    //将 x 和 y 数据组合成对象数组
//					    var data1 = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(function (x, i) {
//					        return { x: x, y: datatable5[icf*2+1]["NR DL MCS Avg Total"][i] };
//					    });
//					    var data2 = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(function (x, i) {
//					        return { x: x, y: datatable5[icf*2+1]["NR DL Avg Rank"][i] };
//					    });
//					    var data3 = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(function (x, i) {
//					        return { x: x, y: datatable5[icf*2+1]["NR DL MAC Throughput(Mbit/s)"][i] };
//					    });
						//渲染增加的图：
						let roundedNumbers = datatable5[icf*2+1]["NR Serving SS Avg RSRP(dBm)"].map(number => parseFloat(number.toFixed(2)));

						Highcharts.chart('chart5dl'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+datatable5[icf*2]+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [
							{
							categories: roundedNumbers,
						 	lineColor:'black'		
							}
						],
						yAxis: [{ // Primary yAxis

						}
							],
						tooltip: {
						},
						series:[
							{name:"NR DL MCS Avg Total",data:datatable5[icf*2+1]["NR DL MCS Avg Total"]},
							{name:"NR DL Avg Rank",data:datatable5[icf*2+1]["NR DL Avg Rank"]},
							{name:"NR DL MAC Throughput(Mbit/s)",data:datatable5[icf*2+1]["NR DL MAC Throughput(Mbit/s)"]},
						],
				
				});	
					}
}



  function draw6ul(){
			var length1 =  datatable6.length;
			length1 = length1/2;
			for (let icc= 0; icc<length1; icc++) {
						if (icc% 2 === 0) {//偶数
							$('#draw6').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%'><div style='hight:300px' id='chart6ul"+icc+"'></div></div>");	
						}else{//奇数
							$('#draw6').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%;margin-left:1%'><div style='hight:300px' id='chart6ul"+icc+"' ></div></div>");
						}
					}
					for(let icf=0;icf<length1;icf++){
						    //将 x 和 y 数据组合成对象数组
//					    var data1 = datatable6[icf*2+1]["RSRP"].map(function (x, i) {
//					        return { x: x, y: datatable6[icf*2+1]["上行MCS"][i] };
//					    });
//					    var data2 = datatable6[icf*2+1]["RSRP"].map(function (x, i) {
//					        return { x: x, y: datatable6[icf*2+1]["上行MAC速率"][i] };
//					    });


						// 使用map方法并结合toFixed方法保留两位小数
						let roundedNumbers = datatable6[icf*2+1]["RSRP"].map(number => parseFloat(number.toFixed(2)));
						

						//渲染增加的图：
						Highcharts.chart('chart6ul'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+datatable6[icf*2]+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [
							{
							categories: roundedNumbers,
						 	lineColor:'black'		
							}
						],
						yAxis: [{ // Primary yAxis

						}
							],
						tooltip: {
						},
						series:[
							{name:"上行MCS",data:datatable6[icf*2+1]["上行MCS"]},
							{name:"上行MAC速率",data:datatable6[icf*2+1]["上行MAC速率"]},
						],
				
				});	
					}
}


  function draw6dl(){
			var length1 =  datatable6.length;
			length1 = length1/2;
			for (let icc= 0; icc<length1; icc++) {
						if (icc% 2 === 0) {//偶数
							$('#draw6').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%'><div style='hight:300px' id='chart6dl"+icc+"'></div></div>");	
						}else{//奇数
							$('#draw6').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%;margin-left:1%'><div style='hight:300px' id='chart6dl"+icc+"' ></div></div>");
						}
					}
					for(let icf=0;icf<length1;icf++){
//						    //将 x 和 y 数据组合成对象数组
//					    var data1 = datatable6[icf*2+1]["RSRP"].map(function (x, i) {
//					        return { x: x, y: datatable6[icf*2+1]["下行MCS"][i] };
//					    });
//					    var data2 = datatable6[icf*2+1]["RSRP"].map(function (x, i) {
//					        return { x: x, y: datatable6[icf*2+1]["RI(Rank Indication)"][i] };
//					    });
//					    var data3 = datatable6[icf*2+1]["RSRP"].map(function (x, i) {
//					        return { x: x, y: datatable6[icf*2+1]["下行MAC速率"][i] };
//					    });
						let roundedNumbers = datatable6[icf*2+1]["RSRP"].map(number => parseFloat(number.toFixed(2)));

						//渲染增加的图：
						Highcharts.chart('chart6dl'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+datatable6[icf*2]+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
						xAxis: [
							{
							categories: roundedNumbers,
						 	lineColor:'black'		
							}
						],
						yAxis: [{ // Primary yAxis

						}
							],
						tooltip: {
						},
						series:[
							{name:"下行MCS",data:datatable6[icf*2+1]["下行MCS"]},
							{name:"RI(Rank Indication)",data:datatable6[icf*2+1]["RI(Rank Indication)"]},
							{name:"下行MAC速率",data:datatable6[icf*2+1]["下行MAC速率"]},
						],
				
				});	
					}
}


  function draw7(){
			var length1 =  datatable7.length;
			length1 = length1/2;
			for (let icc= 0; icc<length1; icc++) {
						if (icc% 2 === 0) {//偶数
							$('#draw7').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%'><div style='hight:300px' id='chart7"+icc+"'></div></div>");	
						}else{//奇数
							$('#draw7').append("<div class='layui-panel layui-col-md6 layui-col-md-custom2'  style='margin-top:1%;margin-left:1%'><div style='hight:300px' id='chart7"+icc+"' ></div></div>");
						}
					}
					for(let icf=0;icf<length1;icf++){
						    //将 x 和 y 数据组合成对象数组
//					    var data1 = datatable7[icf*2+1]["时间点"].map(function (x, i) {
//					        return { x: x, y: datatable7[icf*2+1]["NR DL MAC Throughput(Mbit/s)"][i] };
//					    });
						//渲染增加的图：
						Highcharts.chart('chart7'+icf+'', {
						chart: {
					        backgroundColor: '#fcfcfc'
					    },
						title: {
								text: ""+datatable7[icf*2]+"",
						},
						credits: {
				        enabled: true,
				        text: 'iWork',
				        position: {
				            x: 0,
				            y: -20
				        },
				        opacity: 0.5
				    },
							xAxis: [
							{
								categories: datatable7[icf*2+1]["时间点"],
//								labels: {
//									  formatter: function() {
//										if(Number.isInteger(this.value)){
//												this.value = ""; 
//										}else{
//											
//										}
//				           return this.value
//				        },
//								rotation:90,
//					         style :{
//						fontSize: 8
//						}
//								},
//						 plotLines:listband,
						 	lineColor:'black'		
							}
						],
						yAxis: [{ // Primary yAxis

						}
							],
						tooltip: {
						},
						series:[
							{name:"NR DL MAC Throughput(Mbit/s)",data:datatable7[icf*2+1]["NR DL MAC Throughput(Mbit/s)"]},

						],
				
				});	
					}
}



function draw8(){
//		for (let icc= 0; icc<datatable8.length; icc++) {
//				$('#draw8').append("<table  class='layui-hide' id='draw8"+icc+"' ></table> ");	
//			}
	
//						for(let icf=0;icf<datatable8.length;icf++){
						    //将 x 和 y 数据组合成对象数组
//						    console.log(datatable8[icf]);
//						    var temp = '#draw8'+icf;
//						    console.log(temp);
//						    console.log(datatable8[icf][0]);
//						    console.log(datatable8[icf][1]);
					document.getElementById("pingheader1").innerText=datatable8[0][0];

							layui.use('table', function(){
								var treeTable = layui.treeTable; 
								var form = layui.form; 
						 
							  treeTable.render({//任务概览表格数据
    							elem: '#pingdata1'
							    ,maxHeight: '500px'
							    ,height:'200px'
							    ,cols: [[
							      {field:'时间', title: '时间', align: 'center'}
							        ,{field:'pktloss', title: 'pktloss',align: 'center'}
							 		,{field:'min', title: 'min',align: 'center'}
							        ,{field:'avg', title: 'avg',align: 'center'}
							        ,{field:'max', title: 'max',align: 'center'}
							        ,{field:'mdev', title: 'mdev',align: 'center'}
							    ]],
							   data:datatable8[0][1],
							   text:{
							       none:'当前任务暂无历史构建数据！'},
							   }); 	
						  })
						  					document.getElementById("pingheader2").innerText=datatable8[1][0];

						  layui.use('table', function(){
								var treeTable = layui.treeTable; 
								var form = layui.form; 
						 
							  treeTable.render({//任务概览表格数据
    							elem: '#pingdata2'
							    ,maxHeight: '500px'
							    ,height:'200px'
							    ,cols: [[
							      {field:'时间', title: '时间', align: 'center'}
							        ,{field:'pktloss', title: 'pktloss',align: 'center'}
							 		,{field:'min', title: 'min',align: 'center'}
							        ,{field:'avg', title: 'avg',align: 'center'}
							        ,{field:'max', title: 'max',align: 'center'}
							        ,{field:'mdev', title: 'mdev',align: 'center'}
							    ]],
							   data:datatable8[1][1],
							   text:{
							       none:'当前任务暂无历史构建数据！'},
							   }); 	
						  })
//					    }
}
  
  