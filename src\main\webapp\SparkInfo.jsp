<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>任务数据</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="res/css/layui.css" type="text/css" rel="stylesheet"> 
    <link href="res/layui-v2.9.2/layui/css/layui.css" rel="stylesheet" />
    <link href="res/css/mytab.css" type="text/css" rel="stylesheet">
    <link href="res/css/hr.css" type="text/css" rel="stylesheet">
    <meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<meta http-equiv="Cache-control" content="no-cache">
	<meta http-equiv="Cache" content="no-cache">	
<style>

 xm-select > .xm-body .scroll-body {
    max-width: 500px;
};
.zte_buttoncolor {
  background-color: #ff0000; /* 红色 */
};
body {
  background-color: #ff0000;
};
.layui-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 2px 10px rgba(0, 0, 0, 0.6);
}
.layui-col-md-custom3 {
    width: 99% !important; /* 设置你想要的宽度 */
  }
.layui-col-md-custom {
    width: 24% !important; /* 设置你想要的宽度 */
  }
  .layui-col-md-custom2 {
    width: 49% !important; /* 设置你想要的宽度 */
  }
</style>
</head>
<body >
   <center style="padding-top: 20px">
        <div id="title"></div>
	</center>
	<div id="jump"></div>
<hr class="hr-twill-colorful">	
<!--     <ul id="tabs" style="margin-top:-2%"> -->
<!--       <li><a href="#" name="#tab1">任务概览</a></li> -->
<!--       <li><a href="#"  name="#tab2">健康度</a></li> -->
<!--       <li><a href="#"  name="#tab3">自主分析(表)</a></li> -->
<!--       <li><a href="#"  name="#tab4">自主分析(图)</a></li>  -->
<!--       <li><a href="#"  name="#tab5">AI数据分析</a></li>     -->
         
<!--   	</ul> -->

  <div id="content">
  <!-- *****************************************************tab3******************************************************************** -->    
			<div class="layui-row "  style="margin-top:0.5%;display: none" id="div1">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       					VONR
	       				</div>
						<div class="layui-card-body" >
							<form class="layui-form" style="">
								<table  class="layui-hide" id="VonrSummary" ></table><!-- 基础数据分析-->	 
							</form>
				       </div>
				  </div>	
      		</div>
      	<div class="layui-row "  style="margin-top:0.5%;display: none" id="div2">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       				 VOLTE
	       				</div>
						<div class="layui-card-body" >
							<form class="layui-form" style="">
								<table  class="layui-hide" id="VolteSummary" ></table><!-- 基础数据分析-->	 
							</form>
				       </div>
				  </div>	
      		</div>
      		<div class="layui-row "   style="margin-top:0.5%;display: none" id="div3">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       				 NR定点流量
	       				</div>
						<div class="layui-card-body" >
							<form class="layui-form" style="">
								<table  class="layui-hide" id="NrTput" ></table><!-- 基础数据分析-->	 
							</form>
				       </div>
				  </div>	
      		</div>
      		<div class="layui-row "   style="margin-top:0.5%;display: none" id="div4">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       				 LTE定点流量
	       				</div>
						<div class="layui-card-body" >
							<form class="layui-form" style="">
								<table  class="layui-hide" id="LteTput" ></table><!-- 基础数据分析-->	 
							</form>
				       </div>
				  </div>	
      		</div>
      		
      		
      		    <div class="layui-row "   style="margin-top:0.5%;display: none" id="div5">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       				  NR拉远
	       				</div>
						<div class="layui-card-body" id="draw5">
				       </div>
				  </div>	
      		</div>
      		     		    <div class="layui-row "   style="margin-top:0.5%;display: none" id="div6">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       				   LTE拉远
	       				</div>
						<div class="layui-card-body" id="draw6">
				       </div>
				  </div>	
      		</div>
      		
      		  <div class="layui-row "   style="margin-top:0.5%;display: none" id="div7">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       				   FTP爬坡
	       				</div>
						<div class="layui-card-body" id="draw7">
				       </div>
				  </div>	
      		</div>
      		
      		    <div class="layui-row "   style="margin-top:0.5%;display: none" id="div8">
      				<div class="layui-panel layui-row " style="">
						<div class="layui-card-header" style="font-size:15px;font-weight:bolder;color:#008ED3">
	       				<i class="layui-icon layui-icon-table" style="margin-left:0.5%;font-size: 30px; color: #008ED3;"></i>
	       				   PingData
	       				</div>
	       				<div class="layui-card-body" >
		       				<div class="layui-card-header" id="pingheader1" style="text-align:center;font-size:15px;font-weight:bolder;color:#008ED3">
		       					
		       				</div>
							<form class="layui-form" style="">
								<table  class="layui-hide" id="pingdata1" ></table><!-- 基础数据分析-->	 
							</form>
				       </div>
				       <div class="layui-card-body" >
				       		  <div class="layui-card-header" id="pingheader2" style="text-align:center;font-size:15px;font-weight:bolder;color:#008ED3">
		       					
		       				</div>
							<form class="layui-form" style="">
								<table  class="layui-hide" id="pingdata2" ></table><!-- 基础数据分析-->	 
							</form>
				       </div>
<!-- 						<div class="layui-card-body" id="text8"> -->
<!-- 					<div class="layui-card-body"  id="draw8"> -->
<!-- 							<form class="layui-form" style=""> -->
<!-- 								<table  class="layui-hide" id="text8" ></table>基础数据分析	  -->
<!-- 							</form> -->
<!-- 				       </div> -->
				       </div>
				  </div>	
      		</div>

 
      
      

  </div>
  <br><br><br><br>
  <input id ="temchange" style="display:none"/>
  <p id="about">&nbsp;iWork测试平台.2024 </p>


<script type="text/javascript" src="res/jquery.min.js"></script>
 <script type="text/javascript" src="res/layui.js"></script> 
<script type="text/javascript" src="res/waterMark.js"></script>
<script type="text/javascript" src="res/highcharts/highcharts.js"></script>
<script type="text/javascript" src="res/xm-select/treeTable.js"></script>
<script type="text/javascript" src="res/xm-select/xm-select.js"></script>
<script type="text/javascript" src="res/echarts.js"></script>
 <script type="text/javascript" src="res/sparkinfo.js?v=2"></script> 
<script type="text/html" id="bar">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload" οnkeydοwn="if(event.keyCode==13){return false;}" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm layui-bg-blue"  lay-event="search" style="margin-left:2%" >搜索</button>
		    </div>
</script>
<script type="text/html" id="bar2">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload2" οnkeydοwn="if(event.keyCode==13){return false;}" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm layui-bg-blue"  lay-event="search2" style="margin-left:2%" >搜索</button>
		    </div>
</script>

<script type="text/html" id="bar3">
<div class="layui-inline" style="margin-left:2%">
		        <input class="layui-input" name="id" id="demoReload3" οnkeydοwn="if(event.keyCode==13){return false;}" autocomplete="off" placeholder="请输入搜索值...">
		      </div>
		      <button type="button"  class="layui-btn layui-btn-sm layui-bg-blue"  lay-event="search3" style="margin-left:2%" >搜索</button>
</div>
</script>

<script type="text/html" id="bardown">
  {{#  if(d.level==1){ }}
    <a  class="layui-btn layui-btn-sm layui-bg-orange" lay-event="log" >日志</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downdata2" >MTS</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downdata" >KPI</a>
 {{#  } else { }}
 {{#  } }}
	</script>
	<script type="text/html" id="bardown1">
  {{#  if(d.level==1){ }}
    <a  class="layui-btn layui-btn-sm layui-bg-orange" lay-event="sparkdata" >Spark数据分析</a>
 {{#  } else { }}
 {{#  } }}
	</script>
	
<script type="text/html" id="bardown2">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="preview" >简要预览</a>
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downaidata" >结果下载</a>
</script>
<script type="text/html" id="bardown3">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="downaidata2" >下载</a>
</script>
<script type="text/html" id="bardown4">
	<a  class="layui-btn layui-btn-sm layui-bg-blue" lay-event="delaidata" >删除</a>
</script>
<!-- <script type="text/javascript">init()</script> -->
<script type="text/javascript">
	$(document).ready(function()
	{
		var user1 = "iWork";
		var user2 = "性能研发四部";
		var user3 = "ZTE";
		watermark({"watermark_txt0":user1,"watermark_txt1":user2,"watermark_txt2":user3});
	});
			
</script>  
</body>
</html>