/**
 * @license Highcharts JS v9.0.1 (2021-02-15)
 * @module highcharts/highcharts-3d
 * @requires highcharts
 *
 * 3D features for Highcharts JS
 *
 * License: www.highcharts.com/license
 */
'use strict';
import '../Extensions/Math3D.js';
import '../Core/Renderer/SVG/SVGRenderer3D.js';
import '../Core/Chart/Chart3D.js';
import '../Core/Axis/ZAxis.js';
import '../Core/Axis/Axis3D.js';
import '../Core/Axis/Tick3D.js';
import '../Core/Series/Series3D.js';
import '../Series/Column3D/Column3DComposition.js';
import '../Series/Pie3D/Pie3DComposition.js';
import '../Series/Scatter3D/Scatter3DSeries.js';
import '../Series/Area3DSeries.js';
