[[], [{"series": [{"data": [15.27, 15.63, 16.14, 14.12, null, 15.6, 16.91, 15.74, 15.71, null], "name": "311006_31"}], "x": ["2024-01-18 01:00:00", "2024-01-18 01:15:00", "2024-01-18 01:30:00", "2024-01-18 01:45:00", null, "2024-01-18 05:30:00", "2024-01-18 05:45:00", "2024-01-18 06:00:00", "2024-01-18 06:15:00", null], "y": [{"min": 0, "max": 20, "tickInterval": 2, "label": ""}], "title": "上行平均MCS_1626748539176_1636514252274_PICounter[1]_1663726011220_1667460344143_1594820269733", "case": ["3#case1", "3#case1", "3#case1", "3#case1", null, "4#case1", "4#case1", "4#case1", "4#case1", null]}, {"series": [{"data": [9.83, 10.24, 10.26, 10.44, null, 10.46, 10.47, 11.03, 10.39, null], "name": "311006_31"}], "x": ["2024-01-18 01:00:00", "2024-01-18 01:15:00", "2024-01-18 01:30:00", "2024-01-18 01:45:00", null, "2024-01-18 05:30:00", "2024-01-18 05:45:00", "2024-01-18 06:00:00", "2024-01-18 06:15:00", null], "y": [{"min": 0, "max": 20, "tickInterval": 2, "label": ""}], "title": "下行平均MCS_1626748538540_1636514251538_PICounter[1]_1663726011285_1667460344477_1594820269734", "case": ["3#case1", "3#case1", "3#case1", "3#case1", null, "4#case1", "4#case1", "4#case1", "4#case1", null]}, {"series": [{"data": [73106.1, 111074, 112982, 119843, null, 66477.2, 113976, 120525, 121291, null], "name": "311006_31"}], "x": ["2024-01-18 01:00:00", "2024-01-18 01:15:00", "2024-01-18 01:30:00", "2024-01-18 01:45:00", null, "2024-01-18 05:30:00", "2024-01-18 05:45:00", "2024-01-18 06:00:00", "2024-01-18 06:15:00", null], "y": [{"min": 50000, "max": 200000, "tickInterval": 15000, "label": ""}], "title": "小区下行UE Throughput(kbps)_669501", "case": ["3#case1", "3#case1", "3#case1", "3#case1", null, "4#case1", "4#case1", "4#case1", "4#case1", null]}, {"series": [{"data": [13773.7, 24584.7, 26547.9, 26291.6, null, 14574.3, 25516.1, 28118.5, 23245.2, null], "name": "311006_31"}], "x": ["2024-01-18 01:00:00", "2024-01-18 01:15:00", "2024-01-18 01:30:00", "2024-01-18 01:45:00", null, "2024-01-18 05:30:00", "2024-01-18 05:45:00", "2024-01-18 06:00:00", "2024-01-18 06:15:00", null], "y": [{"min": 0, "max": 30000, "tickInterval": 3000, "label": ""}], "title": "小区上行UE Throughput(kbps)_669798", "case": ["3#case1", "3#case1", "3#case1", "3#case1", null, "4#case1", "4#case1", "4#case1", "4#case1", null]}, {"series": [{"data": [1, 0, 2, 3, null, 4, 2, 1, 2, null], "name": "311006_31"}], "x": ["2024-01-18 01:00:00", "2024-01-18 01:15:00", "2024-01-18 01:30:00", "2024-01-18 01:45:00", null, "2024-01-18 05:30:00", "2024-01-18 05:45:00", "2024-01-18 06:00:00", "2024-01-18 06:15:00", null], "y": [{"min": 0, "max": 120, "tickInterval": 10, "label": "%"}], "title": "Flow掉线率(%)_667186", "case": ["3#case1", "3#case1", "3#case1", "3#case1", null, "4#case1", "4#case1", "4#case1", "4#case1", null]}, {"series": [{"data": [101.43, 100.69, 102.25, 101.26, null, 101.7, 100.29, 99.92, 99.95, null], "name": "311006_31"}], "x": ["2024-01-18 01:00:00", "2024-01-18 01:15:00", "2024-01-18 01:30:00", "2024-01-18 01:45:00", null, "2024-01-18 05:30:00", "2024-01-18 05:45:00", "2024-01-18 06:00:00", "2024-01-18 06:15:00", null], "y": [{"min": 0, "max": 200, "tickInterval": 20, "label": ""}], "title": "RRC连接平均连接用户数_C600000008", "case": ["3#case1", "3#case1", "3#case1", "3#case1", null, "4#case1", "4#case1", "4#case1", "4#case1", null]}, {"series": [{"data": [100, 100, 100, 100, null, 100, 100, 100, 100, null], "name": "311006_31"}], "x": ["2024-01-18 01:00:00", "2024-01-18 01:15:00", "2024-01-18 01:30:00", "2024-01-18 01:45:00", null, "2024-01-18 05:30:00", "2024-01-18 05:45:00", "2024-01-18 06:00:00", "2024-01-18 06:15:00", null], "y": [{"min": 0, "max": 120, "tickInterval": 10, "label": "%"}], "title": "DU物理小区RRC连接建立成功率(%)_664216", "case": ["3#case1", "3#case1", "3#case1", "3#case1", null, "4#case1", "4#case1", "4#case1", "4#case1", null]}]]