﻿[
	[{
		"name": "SN addition success rate(%)",
		"value": "SN addition success rate(%)"
	}, {
		"name": "SN change success rate(%)",
		"value": "SN change success rate(%)"
	}, {
		"name": "Abnormal release rate of SgNB(%)",
		"value": "Abnormal release rate of SgNB(%)"
	}, {
		"name": "SN异常释放率(MN+SN触发,含MN空口原因)_1597370984306",
		"value": "SN异常释放率(MN+SN触发,含MN空口原因)_1597370984306"
	}, {
		"name": "SN异常释放率(仅SN触发)(%)",
		"value": "SN异常释放率(仅SN触发)(%)"
	}, {
		"name": "SN addition average delay(ms)",
		"value": "SN addition average delay(ms)"
	}, {
		"name": "RRC connection establishment success rate in a physical DU cell(%)",
		"value": "RRC connection establishment success rate in a physical DU cell(%)"
	}, {
		"name": "UE Context Setup success rate(%)",
		"value": "UE Context Setup success rate(%)"
	}, {
		"name": "QoS Flow setup success rate by 5QI(%)",
		"value": "QoS Flow setup success rate by 5QI(%)"
	}, {
		"name": "PDU Session setup success rate(%)",
		"value": "PDU Session setup success rate(%)"
	}, {
		"name": "Radio connection rate (DU physical cell)(%)",
		"value": "Radio connection rate (DU physical cell)(%)"
	}, {
		"name": "Radio connection loss rate(%)",
		"value": "Radio connection loss rate(%)"
	}, {
		"name": "Flow connection loss rate(%)",
		"value": "Flow connection loss rate(%)"
	}, {
		"name": "Success rate of intra-RAT outgoing handover(%)",
		"value": "Success rate of intra-RAT outgoing handover(%)"
	}, {
		"name": "Success rate of intra-RAT incoming handover(%)",
		"value": "Success rate of intra-RAT incoming handover(%)"
	}, {
		"name": "Success rate of EPS-fallback handovers(%)",
		"value": "Success rate of EPS-fallback handovers(%)"
	}, {
		"name": "Success rate of inter-RAT outgoing handover(%)",
		"value": "Success rate of inter-RAT outgoing handover(%)"
	}, {
		"name": "RRC connection reestablishment rate (physical cell)(%)",
		"value": "RRC connection reestablishment rate (physical cell)(%)"
	}, {
		"name": "Success rate of RRC connection reestablishment in physical cell(%)",
		"value": "Success rate of RRC connection reestablishment in physical cell(%)"
	}, {
		"name": "Cell UL UE Throughput(kbps)",
		"value": "Cell UL UE Throughput(kbps)"
	}, {
		"name": "Cell DL UE throughput(kbps)",
		"value": "Cell DL UE throughput(kbps)"
	}, {
		"name": "上行频谱效率（bit/PRB）_1669881737007",
		"value": "上行频谱效率（bit/PRB）_1669881737007"
	}, {
		"name": "下行频谱效率（bit/PRB）_1669881737441",
		"value": "下行频谱效率（bit/PRB）_1669881737441"
	}, {
		"name": "UL Data Volume per PRB(kbit/PRB)",
		"value": "UL Data Volume per PRB(kbit/PRB)"
	}, {
		"name": "DL Data Volume per PRB(kbit/PRB)",
		"value": "DL Data Volume per PRB(kbit/PRB)"
	}, {
		"name": "UL residual BLER on the MAC layer(%)",
		"value": "UL residual BLER on the MAC layer(%)"
	}, {
		"name": "DL residual BLER on the MAC layer(%)",
		"value": "DL residual BLER on the MAC layer(%)"
	}, {
		"name": "UL HARQ retransmission rate(%)",
		"value": "UL HARQ retransmission rate(%)"
	}, {
		"name": "DL HARQ retransmission rate(%)",
		"value": "DL HARQ retransmission rate(%)"
	}, {
		"name": "Average UL interference level in a cell(dBm)",
		"value": "Average UL interference level in a cell(dBm)"
	}, {
		"name": "Excellent rate of CQI(%)",
		"value": "Excellent rate of CQI(%)"
	}, {
		"name": "Cell in-service ratio(%)",
		"value": "Cell in-service ratio(%)"
	}, {
		"name": "Average delay of DL first RLC SDU packets in a cell(ms)",
		"value": "Average delay of DL first RLC SDU packets in a cell(ms)"
	}, {
		"name": "Cell DL IP Average delay(ms)",
		"value": "Cell DL IP Average delay(ms)"
	}, {
		"name": "Cell DL RLC SDU Average delay(ms)",
		"value": "Cell DL RLC SDU Average delay(ms)"
	}, {
		"name": "Cell UL RLC SDU Average delay(ms)",
		"value": "Cell UL RLC SDU Average delay(ms)"
	}, {
		"name": "Cell UL RLC layer retransmission rate(%)",
		"value": "Cell UL RLC layer retransmission rate(%)"
	}, {
		"name": "Cell DL RLC layer retransmission rate(%)",
		"value": "Cell DL RLC layer retransmission rate(%)"
	}, {
		"name": "PDU session modification success rate(%)",
		"value": "PDU session modification success rate(%)"
	}, {
		"name": "Success rate of UE logic signalling connection establishments on the NG interface(%)",
		"value": "Success rate of UE logic signalling connection establishments on the NG interface(%)"
	}, {
		"name": "寻呼拥塞率_y_1636514252931_1663726011126_1667460343456_1669881737857",
		"value": "寻呼拥塞率_y_1636514252931_1663726011126_1667460343456_1669881737857"
	}, {
		"name": "Inter-gNB handover success rate(%)",
		"value": "Inter-gNB handover success rate(%)"
	}, {
		"name": "Intra-gNB handover success rate(%)",
		"value": "Intra-gNB handover success rate(%)"
	}, {
		"name": "Success rate of inter-gNB Xn outgoing handover(%)",
		"value": "Success rate of inter-gNB Xn outgoing handover(%)"
	}, {
		"name": "Success rate of inter-gNB Ng outgoing handover(%)",
		"value": "Success rate of inter-gNB Ng outgoing handover(%)"
	}, {
		"name": "Xn口切换占比(%)_y_1665717922173_1668733328161_1669881738137",
		"value": "Xn口切换占比(%)_y_1665717922173_1668733328161_1669881738137"
	}, {
		"name": "Success rate of EPS-fallback handover preparations(%)",
		"value": "Success rate of EPS-fallback handover preparations(%)"
	}, {
		"name": "Average latency of EPS-fallback handover execution(ms)",
		"value": "Average latency of EPS-fallback handover execution(ms)"
	}, {
		"name": "Success rate of preparation for inter-RAT outgoing handover(%)",
		"value": "Success rate of preparation for inter-RAT outgoing handover(%)"
	}, {
		"name": "cell UL RLC Received volume(Mbit)",
		"value": "cell UL RLC Received volume(Mbit)"
	}, {
		"name": "DL RLC SDU data volume sent through Uu(Mbit)",
		"value": "DL RLC SDU data volume sent through Uu(Mbit)"
	}, {
		"name": "Number of bytes of UL PDCP services(MByte)",
		"value": "Number of bytes of UL PDCP services(MByte)"
	}, {
		"name": "Number of bytes of DL PDCP services(MByte)",
		"value": "Number of bytes of DL PDCP services(MByte)"
	}, {
		"name": "UL effective throughput of cell (TNR)(Mbps)",
		"value": "UL effective throughput of cell (TNR)(Mbps)"
	}, {
		"name": "DL effective throughput of cell (TNR)(Mbps)",
		"value": "DL effective throughput of cell (TNR)(Mbps)"
	}, {
		"name": "上行平均MCS_1626748539176_1636514252274_PICounter[1]_1663726011220_1667460344143_1669881738401",
		"value": "上行平均MCS_1626748539176_1636514252274_PICounter[1]_1663726011220_1667460344143_1669881738401"
	}, {
		"name": "下行平均MCS_1626748538540_1636514251538_PICounter[1]_1663726011285_1667460344477_1669881738682",
		"value": "下行平均MCS_1626748538540_1636514251538_PICounter[1]_1663726011285_1667460344477_1669881738682"
	}, {
		"name": "上行DTX比例_y_1636514250131_PICounter[1]_1663726011370_1667460344817_1669881738869",
		"value": "上行DTX比例_y_1636514250131_PICounter[1]_1663726011370_1667460344817_1669881738869"
	}, {
		"name": "下行DTX比例_y_1636514253033_1663726011446_1667460345163_1669881739069",
		"value": "下行DTX比例_y_1636514253033_1663726011446_1667460345163_1669881739069"
	}, {
		"name": "800016:UL CCE Allocation Failure Rate_1663726011516_1667460345500_1669881739295",
		"value": "800016:UL CCE Allocation Failure Rate_1663726011516_1667460345500_1669881739295"
	}, {
		"name": "800017:DL CCE Allocation Failure Rate_1663726011596_1667460345983_1669881739493",
		"value": "800017:DL CCE Allocation Failure Rate_1663726011596_1667460345983_1669881739493"
	}, {
		"name": "pusch dci0 lost占比",
		"value": "pusch dci0 lost占比"
	}, {
		"name": "UL initial BLER of a cell(%)",
		"value": "UL initial BLER of a cell(%)"
	}, {
		"name": "DL initial BLER of a cell(%)",
		"value": "DL initial BLER of a cell(%)"
	}, {
		"name": "Maximum number of active UEs with RRC connections",
		"value": "Maximum number of active UEs with RRC connections"
	}, {
		"name": "Average number of active UEs with RRC connections",
		"value": "Average number of active UEs with RRC connections"
	}, {
		"name": "Maximum number of SA UEs with RRC connections",
		"value": "Maximum number of SA UEs with RRC connections"
	}, {
		"name": "Average number of SA UEs with RRC connections",
		"value": "Average number of SA UEs with RRC connections"
	}, {
		"name": "Maximum number of NSA UEs with RRC connections",
		"value": "Maximum number of NSA UEs with RRC connections"
	}, {
		"name": "Average number of NSA UEs with RRC connections",
		"value": "Average number of NSA UEs with RRC connections"
	}, {
		"name": "Cell Average number of active UEs",
		"value": "Cell Average number of active UEs"
	}, {
		"name": "Cell Average number of DL active UEs",
		"value": "Cell Average number of DL active UEs"
	}, {
		"name": "Cell Average number of UL active UEs",
		"value": "Cell Average number of UL active UEs"
	}, {
		"name": "Average number of UEs scheduled uplink",
		"value": "Average number of UEs scheduled uplink"
	}, {
		"name": "Average number of UEs scheduled downlink",
		"value": "Average number of UEs scheduled downlink"
	}, {
		"name": "Maximum number of UEs scheduled uplink per slot in the cell",
		"value": "Maximum number of UEs scheduled uplink per slot in the cell"
	}, {
		"name": "Maximum number of UEs scheduled downlink per slot in the cell",
		"value": "Maximum number of UEs scheduled downlink per slot in the cell"
	}, {
		"name": "Number of RRC connection establishment failures (physical cell)",
		"value": "Number of RRC connection establishment failures (physical cell)"
	}, {
		"name": "Success rate of RRC connection resumption in physical cell(%)",
		"value": "Success rate of RRC connection resumption in physical cell(%)"
	}, {
		"name": "UL PRB Utilization rate(%)",
		"value": "UL PRB Utilization rate(%)"
	}, {
		"name": "DL PRB Utilization rate(%)",
		"value": "DL PRB Utilization rate(%)"
	}, {
		"name": "UL DTCH PRB utilization rate(%)",
		"value": "UL DTCH PRB utilization rate(%)"
	}, {
		"name": "DL DTCH PRB utilization rate(%)",
		"value": "DL DTCH PRB utilization rate(%)"
	}, {
		"name": "PUSCH PRB Utilization rate(%)",
		"value": "PUSCH PRB Utilization rate(%)"
	}, {
		"name": "PDSCH PRB Utilization rate(%)",
		"value": "PDSCH PRB Utilization rate(%)"
	}, {
		"name": "Average number of used UL CCEs",
		"value": "Average number of used UL CCEs"
	}, {
		"name": "Average number of used DL CCEs",
		"value": "Average number of used DL CCEs"
	}, {
		"name": "CCE occupation rate on the PDCCH (dynamic)(%)",
		"value": "CCE occupation rate on the PDCCH (dynamic)(%)"
	}, {
		"name": "CCE occupation rate on the PDCCH (static)(%)",
		"value": "CCE occupation rate on the PDCCH (static)(%)"
	}, {
		"name": "UL QPSK coding ratio(%)",
		"value": "UL QPSK coding ratio(%)"
	}, {
		"name": "UL 16QAM coding ratio(%)",
		"value": "UL 16QAM coding ratio(%)"
	}, {
		"name": "UL 64QAM coding ratio(%)",
		"value": "UL 64QAM coding ratio(%)"
	}, {
		"name": "UL 256QAM coding ratio(%)",
		"value": "UL 256QAM coding ratio(%)"
	}, {
		"name": "DL QPSK coding ratio(%)",
		"value": "DL QPSK coding ratio(%)"
	}, {
		"name": "DL 16QAM coding ratio(%)",
		"value": "DL 16QAM coding ratio(%)"
	}, {
		"name": "DL 64QAM coding ratio(%)",
		"value": "DL 64QAM coding ratio(%)"
	}, {
		"name": "DL 256QAM coding ratio(%)",
		"value": "DL 256QAM coding ratio(%)"
	}, {
		"name": "DL RANK1 TB ratio(%)",
		"value": "DL RANK1 TB ratio(%)"
	}, {
		"name": "DL RANK2 TB ratio(%)",
		"value": "DL RANK2 TB ratio(%)"
	}, {
		"name": "DL RANK3 TB ratio(%)",
		"value": "DL RANK3 TB ratio(%)"
	}, {
		"name": "DL RANK4 TB ratio(%)",
		"value": "DL RANK4 TB ratio(%)"
	}, {
		"name": "Average UL rank",
		"value": "Average UL rank"
	}, {
		"name": "Average DL rank",
		"value": "Average DL rank"
	}, {
		"name": "下行BF调度比例_1669881739890",
		"value": "下行BF调度比例_1669881739890"
	}, {
		"name": "Average CQI in the 256QAM table reported in PMI mode",
		"value": "Average CQI in the 256QAM table reported in PMI mode"
	}, {
		"name": "Average PUSCH interference level(dBm)",
		"value": "Average PUSCH interference level(dBm)"
	}, {
		"name": "Average PUCCH interference level(dBm)",
		"value": "Average PUCCH interference level(dBm)"
	}, {
		"name": "Ratio of cells with the UL interference level higher than -90 dBm(%)",
		"value": "Ratio of cells with the UL interference level higher than -90 dBm(%)"
	}, {
		"name": "Ratio of cells with the UL interference level higher than -102 dBm(%)",
		"value": "Ratio of cells with the UL interference level higher than -102 dBm(%)"
	}, {
		"name": "Ratio of cells with the UL interference level higher than -107 dBm(%)",
		"value": "Ratio of cells with the UL interference level higher than -107 dBm(%)"
	}, {
		"name": "UL PDCP SDU loss rate(%)",
		"value": "UL PDCP SDU loss rate(%)"
	}, {
		"name": "DL PDCP SDU discard rate(%)",
		"value": "DL PDCP SDU discard rate(%)"
	}, {
		"name": "Cell DL RLC SDU loss rate(%)",
		"value": "Cell DL RLC SDU loss rate(%)"
	}, {
		"name": "Average number of MIMO pairing layers on the UL service channel",
		"value": "Average number of MIMO pairing layers on the UL service channel"
	}, {
		"name": "DL MU-MIMO average pairing layers",
		"value": "DL MU-MIMO average pairing layers"
	}, {
		"name": "PRB ratio for MIMO pairing on the UL service channel(%)",
		"value": "PRB ratio for MIMO pairing on the UL service channel(%)"
	}, {
		"name": "PRB ratio for MIMO pairing on the DL service channel(%)",
		"value": "PRB ratio for MIMO pairing on the DL service channel(%)"
	}, {
		"name": "非竞争RACH接入成功率_MSG1",
		"value": "非竞争RACH接入成功率_MSG1"
	}, {
		"name": "竞争RACH接入成功率_MSG1",
		"value": "竞争RACH接入成功率_MSG1"
	}, {
		"name": "Number of RARs sent",
		"value": "Number of RARs sent"
	}, {
		"name": "Number of sent MSG2 messages",
		"value": "Number of sent MSG2 messages"
	}, {
		"name": "Number of MSG2 messages failing to be scheduled",
		"value": "Number of MSG2 messages failing to be scheduled"
	}, {
		"name": "Number of MSG3 messages successfully received",
		"value": "Number of MSG3 messages successfully received"
	}, {
		"name": "Number of MSG4 messages successfully sent",
		"value": "Number of MSG4 messages successfully sent"
	}, {
		"name": "Number of received MSG1 messages for GroupA Preamble",
		"value": "Number of received MSG1 messages for GroupA Preamble"
	}, {
		"name": "Number of received MSG1 messages for GroupB Preamble",
		"value": "Number of received MSG1 messages for GroupB Preamble"
	}, {
		"name": "Number of received MSG1 messages for non-contention",
		"value": "Number of received MSG1 messages for non-contention"
	}, {
		"name": "Number of received MSG1 messages for non-contention handover",
		"value": "Number of received MSG1 messages for non-contention handover"
	}, {
		"name": "Number of received MSG1 messages for non-contention PDCCH order",
		"value": "Number of received MSG1 messages for non-contention PDCCH order"
	}, {
		"name": "Number of times that the MSG3 message fails to be received",
		"value": "Number of times that the MSG3 message fails to be received"
	}, {
		"name": "Number of times that the MSG4 message fails to be sent",
		"value": "Number of times that the MSG4 message fails to be sent"
	}, {
		"name": "Number of received MSG1 messages for non-contention OSI requests",
		"value": "Number of received MSG1 messages for non-contention OSI requests"
	}, {
		"name": "Number of received MSG1 messages for NSA access and change",
		"value": "Number of received MSG1 messages for NSA access and change"
	}, {
		"name": "Number of times for successfully accessing the MAC layer in non-contention-based random access triggered by the RRC layer",
		"value": "Number of times for successfully accessing the MAC layer in non-contention-based random access triggered by the RRC layer"
	}, {
		"name": "Number of MSG4 messages successfully sent for contention-based access in NSA scenarios",
		"value": "Number of MSG4 messages successfully sent for contention-based access in NSA scenarios"
	}, {
		"name": "Number of contention-based RARs",
		"value": "Number of contention-based RARs"
	}, {
		"name": "Number of times for successfully accessing the MAC layer in non-contention-based random access triggered by a PDCCH Order",
		"value": "Number of times for successfully accessing the MAC layer in non-contention-based random access triggered by a PDCCH Order"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[0,1)",
		"value": "Ratio Distribution of TA values in MSG2 messages[0,1)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[1,3)",
		"value": "Ratio Distribution of TA values in MSG2 messages[1,3)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[3,5)",
		"value": "Ratio Distribution of TA values in MSG2 messages[3,5)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[5,7)",
		"value": "Ratio Distribution of TA values in MSG2 messages[5,7)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[7,9)",
		"value": "Ratio Distribution of TA values in MSG2 messages[7,9)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[9,11)",
		"value": "Ratio Distribution of TA values in MSG2 messages[9,11)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[11,13)",
		"value": "Ratio Distribution of TA values in MSG2 messages[11,13)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[13,20)",
		"value": "Ratio Distribution of TA values in MSG2 messages[13,20)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[20,27)",
		"value": "Ratio Distribution of TA values in MSG2 messages[20,27)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[27,34)",
		"value": "Ratio Distribution of TA values in MSG2 messages[27,34)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[34,40)",
		"value": "Ratio Distribution of TA values in MSG2 messages[34,40)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[40,50)",
		"value": "Ratio Distribution of TA values in MSG2 messages[40,50)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[50,81)",
		"value": "Ratio Distribution of TA values in MSG2 messages[50,81)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[81,129)",
		"value": "Ratio Distribution of TA values in MSG2 messages[81,129)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[129,179)",
		"value": "Ratio Distribution of TA values in MSG2 messages[129,179)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[179,256)",
		"value": "Ratio Distribution of TA values in MSG2 messages[179,256)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[256,384)",
		"value": "Ratio Distribution of TA values in MSG2 messages[256,384)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[384,512)",
		"value": "Ratio Distribution of TA values in MSG2 messages[384,512)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[512,640)",
		"value": "Ratio Distribution of TA values in MSG2 messages[512,640)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[640,769)",
		"value": "Ratio Distribution of TA values in MSG2 messages[640,769)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[769,897)",
		"value": "Ratio Distribution of TA values in MSG2 messages[769,897)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[897,1025)",
		"value": "Ratio Distribution of TA values in MSG2 messages[897,1025)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[1025,1153)",
		"value": "Ratio Distribution of TA values in MSG2 messages[1025,1153)"
	}, {
		"name": "Ratio Distribution of TA values in MSG2 messages[1153,+∞]",
		"value": "Ratio Distribution of TA values in MSG2 messages[1153,+∞]"
	}, {
		"name": "Average UL path loss of service-state UEs(dB)",
		"value": "Average UL path loss of service-state UEs(dB)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs(-∞,85)",
		"value": "Ratio UL path loss distribution of service-state UEs(-∞,85)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[85,90)",
		"value": "Ratio UL path loss distribution of service-state UEs[85,90)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[90,95)",
		"value": "Ratio UL path loss distribution of service-state UEs[90,95)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[95,100)",
		"value": "Ratio UL path loss distribution of service-state UEs[95,100)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[100,105)",
		"value": "Ratio UL path loss distribution of service-state UEs[100,105)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[105,110)",
		"value": "Ratio UL path loss distribution of service-state UEs[105,110)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[110,115)",
		"value": "Ratio UL path loss distribution of service-state UEs[110,115)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[115,120)",
		"value": "Ratio UL path loss distribution of service-state UEs[115,120)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[120,125)",
		"value": "Ratio UL path loss distribution of service-state UEs[120,125)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[125,130)",
		"value": "Ratio UL path loss distribution of service-state UEs[125,130)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[130,135)",
		"value": "Ratio UL path loss distribution of service-state UEs[130,135)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[135,137)",
		"value": "Ratio UL path loss distribution of service-state UEs[135,137)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[137,140)",
		"value": "Ratio UL path loss distribution of service-state UEs[137,140)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[140,143)",
		"value": "Ratio UL path loss distribution of service-state UEs[140,143)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[143,146)",
		"value": "Ratio UL path loss distribution of service-state UEs[143,146)"
	}, {
		"name": "Ratio UL path loss distribution of service-state UEs[146,+∞)",
		"value": "Ratio UL path loss distribution of service-state UEs[146,+∞)"
	}, {
		"name": "TCP RTT of the CN (from the time when the gNodeB sends an SYN message to the time when the gNodeB receives an SYNACK from the CN)(ms)",
		"value": "TCP RTT of the CN (from the time when the gNodeB sends an SYN message to the time when the gNodeB receives an SYNACK from the CN)(ms)"
	}, {
		"name": "TCP RTT of the air interface (from the time when the gNodeB sends an SYNACK message to the time when the gNodeB receives an ACK from the UE)(ms)",
		"value": "TCP RTT of the air interface (from the time when the gNodeB sends an SYNACK message to the time when the gNodeB receives an ACK from the UE)(ms)"
	}, {
		"name": "Average RTT of DL TCP packets in a cell(ms)",
		"value": "Average RTT of DL TCP packets in a cell(ms)"
	}, {
		"name": "Average delay between the ACK and the FirstData packets during a TCP three-way handshake process(ms)",
		"value": "Average delay between the ACK and the FirstData packets during a TCP three-way handshake process(ms)"
	}, {
		"name": "Average DL TCP packet loss rate of a cell(%)",
		"value": "Average DL TCP packet loss rate of a cell(%)"
	}, {
		"name": "Average DL TCP packet disorder rate of a cell(%)",
		"value": "Average DL TCP packet disorder rate of a cell(%)"
	}, {
		"name": "VoNR建立请求次数(5QI1)",
		"value": "VoNR建立请求次数(5QI1)"
	}, {
		"name": "掉线率(5QI1)(小区级)",
		"value": "掉线率(5QI1)(小区级)"
	}, {
		"name": "系统内VoNR用户切换成功率(5QI1)",
		"value": "系统内VoNR用户切换成功率(5QI1)"
	}, {
		"name": "Number of 5QI1 QoS flows in the case of NR-to-LTE outgoing handover preparation requests",
		"value": "Number of 5QI1 QoS flows in the case of NR-to-LTE outgoing handover preparation requests"
	}, {
		"name": "系统间VoNR切换成功率（NG-RAN->EUTRAN）(5QI1)",
		"value": "系统间VoNR切换成功率（NG-RAN->EUTRAN）(5QI1)"
	}, {
		"name": "全网每天空口业务上行字节数(5QI1)",
		"value": "全网每天空口业务上行字节数(5QI1)"
	}, {
		"name": "全网每天空口业务下行字节数(5QI1)",
		"value": "全网每天空口业务下行字节数(5QI1)"
	}, {
		"name": "上行PDCP SDU平均丢包率（5QI1）",
		"value": "上行PDCP SDU平均丢包率（5QI1）"
	}, {
		"name": "下行RLC SDU平均丢包率（5QI1）",
		"value": "下行RLC SDU平均丢包率（5QI1）"
	}, {
		"name": "VoNR建立请求次数(5QI2)",
		"value": "VoNR建立请求次数(5QI2)"
	}, {
		"name": "掉线率(5QI2)(小区级)",
		"value": "掉线率(5QI2)(小区级)"
	}, {
		"name": "系统内VoNR用户切换成功率(5QI2)",
		"value": "系统内VoNR用户切换成功率(5QI2)"
	}, {
		"name": "Number of 5QI2 QoS flows in the case of NR-to-LTE outgoing handover preparation requests",
		"value": "Number of 5QI2 QoS flows in the case of NR-to-LTE outgoing handover preparation requests"
	}, {
		"name": "系统间切换成功率（NG-RAN->EUTRAN）(5QI2)",
		"value": "系统间切换成功率（NG-RAN->EUTRAN）(5QI2)"
	}, {
		"name": "全网每天空口业务上行字节数(5QI2)",
		"value": "全网每天空口业务上行字节数(5QI2)"
	}, {
		"name": "全网每天空口业务下行字节数(5QI2)",
		"value": "全网每天空口业务下行字节数(5QI2)"
	}, {
		"name": "上行PDCP SDU平均丢包率（5QI2）",
		"value": "上行PDCP SDU平均丢包率（5QI2）"
	}, {
		"name": "下行RLC SDU平均丢包率（5QI2）",
		"value": "下行RLC SDU平均丢包率（5QI2）"
	}, {
		"name": "小区mapped 5QI下行RLC SDU平均时延(ms)-5QI1_1652664620378_1667460353662",
		"value": "小区mapped 5QI下行RLC SDU平均时延(ms)-5QI1_1652664620378_1667460353662"
	}, {
		"name": "小区mapped 5QI下行RLC SDU平均时延(ms)-5QI2_1652663696840_1667460353980",
		"value": "小区mapped 5QI下行RLC SDU平均时延(ms)-5QI2_1652663696840_1667460353980"
	}, {
		"name": "5QI1平均个数",
		"value": "5QI1平均个数"
	}, {
		"name": "5QI2平均个数",
		"value": "5QI2平均个数"
	}, {
		"name": "5QI1最大个数",
		"value": "5QI1最大个数"
	}, {
		"name": "5QI2最大个数",
		"value": "5QI2最大个数"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[0,0.25)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[0,0.25)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[0.25,0.5)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[0.25,0.5)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[0.5,1)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[0.5,1)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[1,2)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[1,2)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[2,4)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[2,4)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[4,10)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[4,10)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[10,20)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[10,20)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[20,50)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[20,50)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[50,100)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[50,100)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[100,200)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[100,200)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[200,300)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[200,300)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[300,400)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[300,400)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[400,500)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[400,500)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[500,600)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[500,600)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[600,700)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[600,700)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[700,800)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[700,800)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[800,900)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[800,900)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[900,1000)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[900,1000)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[1000,1100)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[1000,1100)"
	}, {
		"name": "Ratio Cell Speed Distribution of DL UE Throughput[1100,+∞)",
		"value": "Ratio Cell Speed Distribution of DL UE Throughput[1100,+∞)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[0,0.04)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[0,0.04)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[0.04,0.08)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[0.04,0.08)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[0.08,0.16)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[0.08,0.16)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[0.16,0.32)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[0.16,0.32)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[0.32,0.64)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[0.32,0.64)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[0.64,1)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[0.64,1)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[1,2)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[1,2)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[2,6)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[2,6)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[6,12)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[6,12)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[12,20)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[12,20)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[20,35)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[20,35)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[35,50)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[35,50)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[50,65)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[50,65)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[65,80)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[65,80)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[80,95)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[80,95)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[95,110)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[95,110)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[110,125)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[110,125)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[125,140)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[125,140)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[140,155)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[140,155)"
	}, {
		"name": "Ratio Cell Speed Distribution of UL UE Throughput[155,+∞)",
		"value": "Ratio Cell Speed Distribution of UL UE Throughput[155,+∞)"
	}, {
		"name": "上行padding率-new_1667460357060_1669881748052",
		"value": "上行padding率-new_1667460357060_1669881748052"
	}, {
		"name": "下行padding率",
		"value": "下行padding率"
	}, {
		"name": "含尾包的上行速率_y_1667460357728_1669881748300",
		"value": "含尾包的上行速率_y_1667460357728_1669881748300"
	}, {
		"name": "含尾包的下行速率_y_1667460358037_1669881748504",
		"value": "含尾包的下行速率_y_1667460358037_1669881748504"
	}, {
		"name": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[1]",
		"value": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[1]"
	}, {
		"name": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[2]",
		"value": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[2]"
	}, {
		"name": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[3]",
		"value": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[3]"
	}, {
		"name": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[4]",
		"value": "Normal Average number of UEs by uplink dedicated BWPDedicated BWP[4]"
	}, {
		"name": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[1]",
		"value": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[1]"
	}, {
		"name": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[2]",
		"value": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[2]"
	}, {
		"name": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[3]",
		"value": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[3]"
	}, {
		"name": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[4]",
		"value": "Normal Maximum number of UEs by uplink dedicated BWPDedicated BWP[4]"
	}, {
		"name": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[1]",
		"value": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[1]"
	}, {
		"name": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[2]",
		"value": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[2]"
	}, {
		"name": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[3]",
		"value": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[3]"
	}, {
		"name": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[4]",
		"value": "Normal Average number of UEs by downlink dedicated BWPDedicated BWP[4]"
	}, {
		"name": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[1]",
		"value": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[1]"
	}, {
		"name": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[2]",
		"value": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[2]"
	}, {
		"name": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[3]",
		"value": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[3]"
	}, {
		"name": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[4]",
		"value": "Normal Maximum number of UEs by downlink dedicated BWPDedicated BWP[4]"
	}, {
		"name": "UL UE throughput in the full-bandwidth BWP per cell(Mbps)",
		"value": "UL UE throughput in the full-bandwidth BWP per cell(Mbps)"
	}, {
		"name": "DL UE throughput in the full-bandwidth BWP per cell(Mbps)",
		"value": "DL UE throughput in the full-bandwidth BWP per cell(Mbps)"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[1]",
		"value": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[1]"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[2]",
		"value": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[2]"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[3]",
		"value": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[3]"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[4]",
		"value": "Normal Success rate of BWP changes for energy saving by uplink dedicated BWP(%)Dedicated BWP[4]"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[1]",
		"value": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[1]"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[2]",
		"value": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[2]"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[3]",
		"value": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[3]"
	}, {
		"name": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[4]",
		"value": "Normal Success rate of BWP changes for energy saving by downlink dedicated BWP(%)Dedicated BWP[4]"
	}, {
		"name": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[1]",
		"value": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[1]"
	}, {
		"name": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[2]",
		"value": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[2]"
	}, {
		"name": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[3]",
		"value": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[3]"
	}, {
		"name": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[4]",
		"value": "Normal Number of failed CCE allocations by downlink dedicated BWPDedicated BWP[4]"
	}, {
		"name": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[1]",
		"value": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[1]"
	}, {
		"name": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[2]",
		"value": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[2]"
	}, {
		"name": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[3]",
		"value": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[3]"
	}, {
		"name": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[4]",
		"value": "Normal Number of failed CCE allocations in a synchronous-transmission slot by downlink dedicated BWPDedicated BWP[4]"
	}, {
		"name": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[1]",
		"value": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[1]"
	}, {
		"name": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[2]",
		"value": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[2]"
	}, {
		"name": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[3]",
		"value": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[3]"
	}, {
		"name": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[4]",
		"value": "Normal Number of failed uplink CCE allocations by downlink dedicated BWPDedicated BWP[4]"
	}, {
		"name": "Maximum Downlink CA User Number",
		"value": "Maximum Downlink CA User Number"
	}, {
		"name": "Average Downlink CA User Number",
		"value": "Average Downlink CA User Number"
	}, {
		"name": "Average number of UEs that activate UL CA in the PCell under a physical DU cell",
		"value": "Average number of UEs that activate UL CA in the PCell under a physical DU cell"
	}, {
		"name": "Average number of UEs that activate UL CA in an SCell under a physical DU cell",
		"value": "Average number of UEs that activate UL CA in an SCell under a physical DU cell"
	}, {
		"name": "Average number of UEs that activate DL CA in the PCell under a physical DU cell",
		"value": "Average number of UEs that activate DL CA in the PCell under a physical DU cell"
	}, {
		"name": "Average number of UEs that activate DL CA in an SCell under a physical DU cell",
		"value": "Average number of UEs that activate DL CA in an SCell under a physical DU cell"
	}, {
		"name": "DL UE throughput of CA UEs in a cell(kbps)",
		"value": "DL UE throughput of CA UEs in a cell(kbps)"
	}, {
		"name": "SCell addition executions Success Rate(%)",
		"value": "SCell addition executions Success Rate(%)"
	}, {
		"name": "Success rate of SCell activation by MACCE message(%)",
		"value": "Success rate of SCell activation by MACCE message(%)"
	}, {
		"name": "Success rate of SCell deactivation by MACCE message(%)",
		"value": "Success rate of SCell deactivation by MACCE message(%)"
	}, {
		"name": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[2]",
		"value": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[2]"
	}, {
		"name": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[3]",
		"value": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[3]"
	}, {
		"name": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[4]",
		"value": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[4]"
	}, {
		"name": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[5]",
		"value": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[5]"
	}, {
		"name": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[6]",
		"value": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[6]"
	}, {
		"name": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[7]",
		"value": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[7]"
	}, {
		"name": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[8]",
		"value": "Normal DL UE throughput of CA UEs in a cell based on the number of CCs(kbps)CC Num[8]"
	}, {
		"name": "Normal UL UE throughput of active CA UEs in a cell based on the number of CCs(kbps)CC Num[2]",
		"value": "Normal UL UE throughput of active CA UEs in a cell based on the number of CCs(kbps)CC Num[2]"
	}, {
		"name": "Normal UL UE throughput of active CA UEs in a cell based on the number of CCs(kbps)CC Num[3]",
		"value": "Normal UL UE throughput of active CA UEs in a cell based on the number of CCs(kbps)CC Num[3]"
	}, {
		"name": "Normal UL UE throughput of active CA UEs in a cell based on the number of CCs(kbps)CC Num[4]",
		"value": "Normal UL UE throughput of active CA UEs in a cell based on the number of CCs(kbps)CC Num[4]"
	}, {
		"name": "PCell downlink BLER(%)",
		"value": "PCell downlink BLER(%)"
	}, {
		"name": "PCell uplink BLER(%)",
		"value": "PCell uplink BLER(%)"
	}, {
		"name": "SCell downlink BLER(%)",
		"value": "SCell downlink BLER(%)"
	}, {
		"name": "SCell uplink BLER(%)",
		"value": "SCell uplink BLER(%)"
	}],
	[{
		"BUILD_NUMBER": "9"
	}, {
		"BUILD_NUMBER": "8"
	}, {
		"BUILD_NUMBER": "7"
	}, {
		"BUILD_NUMBER": "6"
	}, {
		"BUILD_NUMBER": "3"
	}]
]