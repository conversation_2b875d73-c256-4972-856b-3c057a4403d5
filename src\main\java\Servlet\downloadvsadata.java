package Servlet;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;



/**
 * Servlet implementation class DownloadRequestFile
 */
@WebServlet("/downloadvsadata")
public class downloadvsadata extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public downloadvsadata() {
        super();
        // TODO Auto-generated constructor stub
    }

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		String jobid= request.getParameter("id");
		String buildno= request.getParameter("buildno");

		
        String folderPath = "//data//iwork2//custom//vsadata//"+jobid+"//"+buildno+"";
        String zipFileName = "//data//iwork2//custom//vsadata//"+jobid+"//vsadata"+jobid+"_"+buildno+".zip";

//        // 创建ZIP文件
//        FileOutputStream fos = new FileOutputStream(zipFileName);
//        ZipOutputStream zos = new ZipOutputStream(fos);
//
//        // 遍历文件夹并添加到ZIP
//        addFolderToZip(folderPath, zos);
//
//        zos.close();
//        fos.close();
        
//		String zipfileString = "";
        try {
//        	zipfileString = tempdirectory1 +"//historyKPI.zip";		
            FileOutputStream fos = new FileOutputStream(zipFileName);
            ZipOutputStream zos = new ZipOutputStream(fos);
            addFolderToZip2("",new File( folderPath) , zos);
			zos.close();
	        fos.close();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

//        InputStream is = null;
//        OutputStream os = null;
        response.addHeader("content-type", "application/x-msdownload");
        response.setHeader("Content-Disposition", "attachment;fileName=vsadata"+jobid+"_"+buildno+".zip");

		ServletOutputStream out =  response.getOutputStream();

//	        try {
	              FileInputStream in = new FileInputStream(zipFileName);	
	              byte[] buffer = new byte[1024];
	              int len = 0;
	              while((len=in.read(buffer))!=-1) {
		      			out.write(buffer,0,len);
		      		}
	              in.close();
//	        	is = new FileInputStream(zipFileName);
//	        	out = response.getOutputStream();
//	            response.setContentType("application/octet-stream");
	    		
//	            int len = 0;
//	            byte[] bytes = new byte[1024];
//	            while ((len = is.read(bytes)) != -1) {
//	            	out.write(bytes, 0, bytes.length);
////	                os.flush();
//	            }
//	        } catch (FileNotFoundException e) {
//	            e.printStackTrace();
//	        } catch (IOException e) {
//	            e.printStackTrace();
//	        } finally {
//	            try {
//	                is.close();
//	            } catch (IOException e) {
//	                e.printStackTrace();
//	            }
////	            try {
////	                os.close();
////	            } catch (IOException e) {
////	                e.printStackTrace();
////	            }
//	        }
	        out.close();
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doGet(request, response);
	}
	
    private static void addFolderToZip(String folderPath, ZipOutputStream zos) throws IOException {
        File folder = new File(folderPath);
        File[] files = folder.listFiles();

        for (File file : files) {
            if (file.isDirectory()) {
                addFolderToZip(file.getPath(), zos);
                continue;
            }
            System.out.println(file.getPath());
            FileInputStream fis = new FileInputStream(file);
            ZipEntry ze = new ZipEntry(file.getPath());
            zos.putNextEntry(ze);

            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = fis.read(buffer)) != -1) {
                zos.write(buffer, 0, bytesRead);
            }

            zos.closeEntry();
            fis.close();
        }
    }
    
    private static void zipDirectory(File directory, ZipOutputStream zos) throws Exception {
        File[] files = directory.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                zipDirectory(file, zos);
            } else {
                byte[] buffer = new byte[1024];
                FileInputStream fis = new FileInputStream(file);
                zos.putNextEntry(new ZipEntry( file.getName()));
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, length);
                }
                zos.closeEntry();
                fis.close();
            }
        }
    }
    
    
    private static void addFolderToZip2(String parentPath, File folder, ZipOutputStream zos) throws IOException {
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 递归添加子文件夹中的文件
                    addFolderToZip2(parentPath + file.getName() + "/", file, zos);
                } else {
                    // 新建Zip条目并将文件内容加入到Zip包中
                    ZipEntry zipEntry = new ZipEntry(parentPath + file.getName());
                    zos.putNextEntry(zipEntry);
                    byte[] buffer = new byte[1024];
                    int len;
                    try (FileInputStream fis = new FileInputStream(file)) {
                        while ((len = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, len);
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    } finally {
                        zos.closeEntry(); // 关闭当前ZipEntry，‌准备添加下一个文件或目录到Zip包中。‌注意，‌这里应该是在写入文件内容之后，‌但在添加下一个文件之前关闭当前ZipEntry。‌但在上述代码中，‌应该在try-with-resources块结束时自动关闭，‌因此这里的关闭操作可能是多余的。‌正确的做法是使用try-with-resources来确保资源被正确关闭。‌但由于原始代码没有使用try-with-resources，‌我们在这里保留了关闭操作以符合原始代码的逻辑。‌但在实际开发中，‌应优先考虑使用try-with-resources来管理资源。‌} } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } } 注意：‌上述代码中的注释和格式调整是为了提高可读性，‌实际代码中应遵循Java的编码规范和最佳实践。‌特别是关于资源管理的部分，‌应使用try-with-resources来确保资源（‌如文件流）‌在使用后被正确关闭，‌以避免资源泄漏和其他潜在问题。‌此外，‌错误处理也应该更加完善，‌例如通过适当使用异常处理机制来捕获和处理可能出现的异常情况。‌
                    }
                }
            }
        }
    }
    
    

}

//
//public class ZipUtils {
//    public static void zipFolder(String folderPath, String zipFilePath) throws IOException {
//        FileOutputStream fos = null;
//        ZipOutputStream zos = null;
//        try {
//            fos = new FileOutputStream(zipFilePath);
//            zos = new ZipOutputStream(fos);
//            addFolderToZip("", new File(folderPath), zos);
//        } finally {
//            if (zos != null) {
//                zos.close();
//            }
//            if (fos != null) {
//                fos.close();
//            }
//        }
//    }


